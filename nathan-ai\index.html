<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    
    <!-- Basic Meta Tags -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no" />
    <meta name="description" content="Nathan AI - An emotionally intelligent conversational AI companion with voice and text interaction capabilities" />
    <meta name="keywords" content="AI, chatbot, voice assistant, conversation, artificial intelligence" />
    <meta name="author" content="Nathan AI Team" />
    
    <!-- Title -->
    <title><PERSON> AI - Conversational Assistant</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icon-180x180.png" />
    <link rel="mask-icon" href="/icon-mask.svg" color="#007bff" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff" />
    <meta name="background-color" content="#ffffff" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Nathan AI" />
    <meta name="application-name" content="Nathan AI" />
    <meta name="msapplication-TileColor" content="#007bff" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nathan-ai.app/" />
    <meta property="og:title" content="Nathan AI - Conversational Assistant" />
    <meta property="og:description" content="An emotionally intelligent conversational AI companion with voice and text interaction capabilities" />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://nathan-ai.app/" />
    <meta property="twitter:title" content="Nathan AI - Conversational Assistant" />
    <meta property="twitter:description" content="An emotionally intelligent conversational AI companion with voice and text interaction capabilities" />
    <meta property="twitter:image" content="/twitter-image.png" />
    
    <!-- Performance Optimizations -->
    <link rel="preconnect" href="https://api-inference.huggingface.co" />
    <link rel="preconnect" href="https://api.elevenlabs.io" />
    <link rel="dns-prefetch" href="https://api-inference.huggingface.co" />
    <link rel="dns-prefetch" href="https://api.elevenlabs.io" />
    
    <!-- Critical CSS for mobile performance -->
    <style>
      /* Critical above-the-fold styles */
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #ffffff;
        color: #212529;
        overflow-x: hidden;
      }
      
      #root {
        min-height: 100vh;
        min-height: 100dvh; /* Dynamic viewport height for mobile */
      }
      
      /* Loading screen styles */
      .initial-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        min-height: 100dvh;
        background-color: #ffffff;
        color: #212529;
      }
      
      .loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid #dee2e6;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Dark theme support */
      @media (prefers-color-scheme: dark) {
        body {
          background-color: #1a1a1a;
          color: #ffffff;
        }
        
        .initial-loading {
          background-color: #1a1a1a;
          color: #ffffff;
        }
        
        .loading-spinner {
          border-color: #404040;
          border-top-color: #0d6efd;
        }
      }
      
      /* Mobile optimizations */
      @media (max-width: 768px) {
        body {
          font-size: 16px; /* Prevent zoom on iOS */
          -webkit-text-size-adjust: 100%;
          -webkit-tap-highlight-color: transparent;
        }
        
        #root {
          /* Use CSS custom property for dynamic viewport height */
          min-height: calc(var(--vh, 1vh) * 100);
        }
      }
      
      /* Reduce motion for accessibility */
      @media (prefers-reduced-motion: reduce) {
        .loading-spinner {
          animation: none;
        }
      }
    </style>
    
    <!-- Mobile viewport height fix -->
    <script>
      // Fix for mobile viewport height issues
      function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      }
      
      setViewportHeight();
      window.addEventListener('resize', setViewportHeight);
      window.addEventListener('orientationchange', () => {
        setTimeout(setViewportHeight, 100);
      });
    </script>
  </head>
  <body>
    <!-- Initial loading screen -->
    <div id="root">
      <div class="initial-loading">
        <div class="loading-spinner"></div>
      </div>
    </div>
    
    <!-- Main application script -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Service Worker Registration -->
    <script>
      // Register service worker for offline support
      if ('serviceWorker' in navigator && import.meta.env.PROD) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
      
      // Handle app updates
      window.addEventListener('sw-update-available', (event) => {
        if (confirm('A new version of Nathan AI is available. Update now?')) {
          window.location.reload();
        }
      });
    </script>
    
    <!-- Fallback for no-JS users -->
    <noscript>
      <div style="text-align: center; padding: 2rem; font-family: Arial, sans-serif;">
        <h1>Nathan AI</h1>
        <p>JavaScript is required to run Nathan AI. Please enable JavaScript in your browser.</p>
      </div>
    </noscript>
  </body>
</html>
