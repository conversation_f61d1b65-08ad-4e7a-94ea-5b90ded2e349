/**
 * Caching system for API responses and audio
 */

// Cache configuration
interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * Generic LRU Cache with TTL support
 */
export class LRUCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;
  }

  set(key: string, value: T): void {
    const now = Date.now();
    
    // Remove expired entries before adding new one
    this.cleanup();
    
    // If cache is full, remove least recently used item
    if (this.cache.size >= this.config.maxSize) {
      this.evictLRU();
    }
    
    this.cache.set(key, {
      data: value,
      timestamp: now,
      accessCount: 0,
      lastAccessed: now
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    const now = Date.now();
    
    // Check if entry has expired
    if (now - entry.timestamp > this.config.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;
    
    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    this.cleanup();
    return this.cache.size;
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.ttl) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.cache.delete(key));
  }

  private evictLRU(): void {
    let lruKey: string | null = null;
    let lruTime = Infinity; // Start with infinity to find the minimum
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }
    
    if (lruKey) {
      this.cache.delete(lruKey);
    }
  }

  getStats(): {
    size: number;
    hitRate: number;
    entries: Array<{ key: string; accessCount: number; age: number }>;
  } {
    this.cleanup();
    const now = Date.now();
    const entries: Array<{ key: string; accessCount: number; age: number }> = [];
    let totalAccesses = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      entries.push({
        key,
        accessCount: entry.accessCount,
        age: now - entry.timestamp
      });
      totalAccesses += entry.accessCount;
    }
    
    return {
      size: this.cache.size,
      hitRate: totalAccesses > 0 ? entries.length / totalAccesses : 0,
      entries
    };
  }
}

/**
 * API Response Cache
 */
export class ApiResponseCache {
  private cache: LRUCache<any>;
  
  constructor() {
    this.cache = new LRUCache({
      maxSize: 50, // Store up to 50 API responses
      ttl: 5 * 60 * 1000 // 5 minutes TTL
    });
  }

  generateKey(endpoint: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as Record<string, any>);
    
    return `${endpoint}:${JSON.stringify(sortedParams)}`;
  }

  set(endpoint: string, params: Record<string, any>, response: any): void {
    const key = this.generateKey(endpoint, params);
    this.cache.set(key, response);
  }

  get(endpoint: string, params: Record<string, any>): any | null {
    const key = this.generateKey(endpoint, params);
    return this.cache.get(key);
  }

  has(endpoint: string, params: Record<string, any>): boolean {
    const key = this.generateKey(endpoint, params);
    return this.cache.has(key);
  }

  clear(): void {
    this.cache.clear();
  }

  getStats() {
    return this.cache.getStats();
  }
}

/**
 * Audio Cache for TTS responses
 */
export class AudioCache {
  private cache: LRUCache<ArrayBuffer>;
  private urlCache = new Map<string, string>();
  
  constructor() {
    this.cache = new LRUCache({
      maxSize: 20, // Store up to 20 audio files
      ttl: 10 * 60 * 1000 // 10 minutes TTL
    });
  }

  generateKey(text: string, voiceSettings: Record<string, any>): string {
    // Normalize text for consistent caching
    const normalizedText = text.trim().toLowerCase();
    const settingsKey = JSON.stringify(voiceSettings);
    return `audio:${normalizedText}:${settingsKey}`;
  }

  set(text: string, voiceSettings: Record<string, any>, audioBuffer: ArrayBuffer): void {
    const key = this.generateKey(text, voiceSettings);
    this.cache.set(key, audioBuffer);
    
    // Create blob URL for immediate use (if available)
    if (typeof URL !== 'undefined' && URL.createObjectURL) {
      const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(blob);
      this.urlCache.set(key, url);
    }
  }

  get(text: string, voiceSettings: Record<string, any>): ArrayBuffer | null {
    const key = this.generateKey(text, voiceSettings);
    return this.cache.get(key);
  }

  getUrl(text: string, voiceSettings: Record<string, any>): string | null {
    const key = this.generateKey(text, voiceSettings);
    
    // Check if we have the audio data
    if (!this.cache.has(key)) {
      return null;
    }
    
    // Return existing URL or create new one
    if (this.urlCache.has(key)) {
      return this.urlCache.get(key)!;
    }
    
    const audioBuffer = this.cache.get(key);
    if (audioBuffer && typeof URL !== 'undefined' && URL.createObjectURL) {
      const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(blob);
      this.urlCache.set(key, url);
      return url;
    }
    
    return null;
  }

  has(text: string, voiceSettings: Record<string, any>): boolean {
    const key = this.generateKey(text, voiceSettings);
    return this.cache.has(key);
  }

  clear(): void {
    // Revoke all blob URLs to prevent memory leaks (if available)
    if (typeof URL !== 'undefined' && URL.revokeObjectURL) {
      for (const url of this.urlCache.values()) {
        URL.revokeObjectURL(url);
      }
    }
    
    this.urlCache.clear();
    this.cache.clear();
  }

  getStats() {
    return {
      ...this.cache.getStats(),
      urlCacheSize: this.urlCache.size
    };
  }
}

/**
 * Personality Cache for configuration data
 */
export class PersonalityCache {
  private cache: LRUCache<any>;
  
  constructor() {
    this.cache = new LRUCache({
      maxSize: 10, // Store up to 10 personality configurations
      ttl: 30 * 60 * 1000 // 30 minutes TTL
    });
  }

  set(personalityId: string, config: any): void {
    this.cache.set(personalityId, config);
  }

  get(personalityId: string): any | null {
    return this.cache.get(personalityId);
  }

  has(personalityId: string): boolean {
    return this.cache.has(personalityId);
  }

  clear(): void {
    this.cache.clear();
  }

  getStats() {
    return this.cache.getStats();
  }
}

/**
 * Global cache manager
 */
export class CacheManager {
  public readonly apiCache = new ApiResponseCache();
  public readonly audioCache = new AudioCache();
  public readonly personalityCache = new PersonalityCache();

  clearAll(): void {
    this.apiCache.clear();
    this.audioCache.clear();
    this.personalityCache.clear();
  }

  getGlobalStats() {
    return {
      api: this.apiCache.getStats(),
      audio: this.audioCache.getStats(),
      personality: this.personalityCache.getStats()
    };
  }

  // Memory pressure handler
  handleMemoryPressure(): void {
    console.log('Memory pressure detected, clearing caches');
    
    // Clear least important caches first
    this.apiCache.clear();
    
    // If still under pressure, clear audio cache
    setTimeout(() => {
      if (this.getMemoryUsage() > 50) { // 50MB threshold
        this.audioCache.clear();
      }
    }, 1000);
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024;
    }
    return 0;
  }
}

// Global cache instance
export const cacheManager = new CacheManager();

// Set up memory pressure monitoring
if ('memory' in performance) {
  setInterval(() => {
    const memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024;
    if (memoryUsage > 100) { // 100MB threshold
      cacheManager.handleMemoryPressure();
    }
  }, 30000); // Check every 30 seconds
}