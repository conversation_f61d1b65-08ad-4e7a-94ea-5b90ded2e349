import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getEnvVar, validateEnvironment, config } from '../utils/env';

// Mock import.meta.env
const mockEnv = {
  VITE_HUGGING_FACE_API_KEY: '',
  VITE_ELEVEN_LABS_API_KEY: '',
  VITE_ELEVEN_LABS_VOICE_ID: '',
  VITE_HUGGING_FACE_MODEL: '',
  DEV: false,
  PROD: true,
};

vi.stubGlobal('import', {
  meta: {
    env: mockEnv
  }
});

describe('Environment Utilities', () => {
  beforeEach(() => {
    // Reset mock environment
    Object.keys(mockEnv).forEach(key => {
      mockEnv[key as keyof typeof mockEnv] = '';
    });
    mockEnv.DEV = false;
    mockEnv.PROD = true;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getEnvVar', () => {
    it('returns environment variable value when set', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_test_key';
      
      const result = getEnvVar('VITE_HUGGING_FACE_API_KEY');
      expect(result).toBe('hf_test_key');
    });

    it('returns default value when environment variable is not set', () => {
      const result = getEnvVar('VITE_MISSING_VAR', 'default_value');
      expect(result).toBe('default_value');
    });

    it('throws error when required variable is missing and no default provided', () => {
      expect(() => getEnvVar('VITE_MISSING_VAR')).toThrow(
        'Environment variable VITE_MISSING_VAR is required but not set'
      );
    });

    it('returns empty string when default is empty string', () => {
      const result = getEnvVar('VITE_MISSING_VAR', '');
      expect(result).toBe('');
    });
  });

  describe('validateEnvironment', () => {
    it('returns valid when all required variables are set', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_test_key';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'el_test_key_12345678901234567890';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';
      mockEnv.VITE_HUGGING_FACE_MODEL = 'test_model';

      const result = validateEnvironment();
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('returns errors for missing required variables', () => {
      const result = validateEnvironment();
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required environment variable: VITE_HUGGING_FACE_API_KEY');
      expect(result.errors).toContain('Missing required environment variable: VITE_ELEVEN_LABS_API_KEY');
      expect(result.errors).toContain('Missing required environment variable: VITE_ELEVEN_LABS_VOICE_ID');
    });

    it('returns warnings for missing optional variables', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_test_key';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'el_test_key_12345678901234567890';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';

      const result = validateEnvironment();
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Optional environment variable not set: VITE_HUGGING_FACE_MODEL (using default)');
    });

    it('validates Hugging Face API key format', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'invalid_key';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'el_test_key_12345678901234567890';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';

      const result = validateEnvironment();
      
      expect(result.warnings).toContain('Hugging Face API key should start with "hf_"');
    });

    it('validates Eleven Labs API key length', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_test_key';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'short_key';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';

      const result = validateEnvironment();
      
      expect(result.warnings).toContain('Eleven Labs API key appears to be too short');
    });

    it('handles empty string values as missing', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = '   ';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = '';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';

      const result = validateEnvironment();
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required environment variable: VITE_HUGGING_FACE_API_KEY');
      expect(result.errors).toContain('Missing required environment variable: VITE_ELEVEN_LABS_API_KEY');
    });
  });

  describe('config', () => {
    it('exports configuration object with correct structure', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_test_key';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'el_test_key';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';
      mockEnv.VITE_HUGGING_FACE_MODEL = 'custom_model';
      mockEnv.DEV = true;
      mockEnv.PROD = false;

      expect(config).toHaveProperty('huggingFace');
      expect(config).toHaveProperty('elevenLabs');
      expect(config).toHaveProperty('isDevelopment');
      expect(config).toHaveProperty('isProduction');
    });

    it('uses default model when not specified', () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_test_key';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'el_test_key';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_123';

      expect(config.huggingFace.model).toBe('microsoft/DialoGPT-medium');
    });
  });
});