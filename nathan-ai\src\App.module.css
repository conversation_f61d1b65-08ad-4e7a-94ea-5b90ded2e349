/* CSS Variables for theming */
:root {
  /* Light theme */
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --text-color: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
  --primary-color: #007bff;
  --primary-hover: #0056b3;
  --secondary-color: #6c757d;
  --secondary-hover: #545b62;
  --error-color: #dc3545;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] {
  /* Dark theme */
  --background-color: #1a1a1a;
  --surface-color: #2d2d2d;
  --text-color: #ffffff;
  --text-secondary: #b0b0b0;
  --border-color: #404040;
  --primary-color: #0d6efd;
  --primary-hover: #0b5ed7;
  --secondary-color: #6c757d;
  --secondary-hover: #5a6268;
  --error-color: #dc3545;
  --success-color: #198754;
  --warning-color: #ffc107;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Base app styles */
.app {
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* Header styles */
.header {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color);
}

.controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.controlGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.controlGroup label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.select {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 0.875rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.select:hover {
  border-color: var(--primary-color);
}

.select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Main content */
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 1rem;
  position: relative;
  min-height: 0; /* Allow flex children to shrink */
}

/* Conversation area */
.conversationArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex children to shrink */
  margin-bottom: 1rem;
}

/* Input area */
.inputArea {
  flex-shrink: 0;
  background-color: var(--surface-color);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

/* Global error display */
.globalError {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  max-width: 400px;
}

.errorMessage {
  background-color: var(--error-color);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-lg);
  animation: slideInRight 0.3s ease-out;
}

.errorIcon {
  font-size: 1.125rem;
  flex-shrink: 0;
}

.errorDismiss {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: auto;
  border-radius: 3px;
  font-size: 1rem;
  line-height: 1;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.errorDismiss:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Global loading overlay */
.globalLoading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(2px);
}

.loadingOverlay {
  background-color: var(--surface-color);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-width: 200px;
  text-align: center;
}

.loadingOverlay .loadingSpinner {
  width: 32px;
  height: 32px;
  margin: 0;
}

.loadingOverlay span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.cancelButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancelButton:hover {
  background-color: var(--secondary-hover);
}

.cancelButton:active {
  transform: translateY(1px);
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Visual mode specific styles */
.visual {
  /* Enhanced visual styling for visual mode */
}

.visual .header {
  background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
}

.visual .title {
  background: linear-gradient(135deg, var(--primary-color), #6f42c1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Minimal mode specific styles */
.minimal {
  /* Simplified styling for minimal mode */
}

.minimal .header {
  border-bottom: none;
  box-shadow: none;
  background-color: transparent;
}

.minimal .controls {
  opacity: 0.7;
}

.minimal .controls:hover {
  opacity: 1;
}

/* Loading screen */
.loadingScreen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
}

.loadingContent {
  text-align: center;
  padding: 2rem;
}

.loadingSpinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContent h2 {
  margin: 0 0 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.loadingContent p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Error screen */
.errorScreen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  padding: 2rem;
}

.errorContent {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
}

.errorContent h2 {
  color: var(--error-color);
  margin: 0 0 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.errorContent p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.retryButton {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: var(--primary-hover);
}

.retryButton:active {
  transform: translateY(1px);
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .app {
    /* Use CSS custom property for dynamic viewport height */
    min-height: calc(var(--viewport-height, 100vh));
  }
  
  .headerContent {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    padding: 0 1rem;
  }
  
  .title {
    text-align: center;
    font-size: 1.25rem;
  }
  
  .controls {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.75rem;
  }
  
  .main {
    padding: 0 0.5rem;
    gap: 0.5rem;
    /* Dynamic height accounting for keyboard */
    height: calc(var(--viewport-height, 100vh) - 120px - var(--keyboard-height, 0px));
    transition: height 0.3s ease;
  }
  
  .conversationArea {
    margin-bottom: 0.5rem;
    flex: 1;
    min-height: 0;
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  .inputArea {
    padding: 0.75rem;
    margin: 0 -0.5rem;
    border-radius: 8px 8px 0 0;
    border-left: none;
    border-right: none;
    position: sticky;
    bottom: 0;
    background-color: var(--surface-color);
    z-index: var(--z-fixed);
    /* Safe area support */
    padding-bottom: max(0.75rem, env(safe-area-inset-bottom));
  }
  
  .globalError {
    top: max(0.5rem, env(safe-area-inset-top));
    right: max(0.5rem, env(safe-area-inset-right));
    left: max(0.5rem, env(safe-area-inset-left));
    max-width: none;
  }
  
  .loadingOverlay {
    margin: 1rem;
    padding: 1.5rem;
    max-width: calc(100vw - 2rem);
  }
  
  .loadingContent,
  .errorContent {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .controlGroup {
    width: 100%;
    justify-content: space-between;
    /* Touch-friendly spacing */
    min-height: 44px;
    align-items: center;
  }
  
  .select {
    min-width: 120px;
    min-height: 44px; /* iOS touch target */
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .main {
    padding: 0 0.25rem;
    height: calc(var(--viewport-height, 100vh) - 140px - var(--keyboard-height, 0px));
    transition: height 0.3s ease;
  }
  
  .header {
    padding: 0.75rem 0;
  }
  
  .headerContent {
    padding: 0 max(0.5rem, env(safe-area-inset-left)) 0 max(0.5rem, env(safe-area-inset-right));
  }
  
  .title {
    font-size: 1.125rem;
  }
  
  .inputArea {
    padding: 0.5rem;
    margin: 0 -0.25rem;
    /* Safe area support for bottom */
    padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
  }
}

/* Enhanced Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
  .app {
    min-height: calc(var(--viewport-height, 100vh));
  }
  
  .header {
    padding: 0.5rem 0;
    /* Compact header in landscape */
    min-height: 60px;
  }
  
  .main {
    padding: 0 0.5rem;
    height: calc(var(--viewport-height, 100vh) - 60px - var(--keyboard-height, 0px));
    transition: height 0.3s ease;
  }
  
  .conversationArea {
    margin-bottom: 0.5rem;
    flex: 1;
    min-height: 0;
  }
  
  .inputArea {
    padding: 0.5rem;
    flex-shrink: 0;
    /* Compact input in landscape */
    min-height: auto;
  }
  
  .loadingOverlay {
    padding: 1rem;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .headerContent {
    flex-direction: row;
    gap: 1rem;
    align-items: center;
  }
  
  .controls {
    flex-direction: row;
    gap: 0.75rem;
    flex-wrap: nowrap;
  }
  
  .title {
    font-size: 1rem;
    margin: 0;
  }
  
  /* Hide less important elements in compact landscape */
  .subtitle {
    display: none;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .select,
  .retryButton,
  .errorDismiss {
    min-height: 44px; /* iOS recommended touch target size */
    padding: 0.75rem 1rem;
  }
  
  .controlGroup {
    gap: 0.75rem;
  }
  
  .errorDismiss {
    min-width: 44px;
    padding: 0.75rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .loadingSpinner {
    border-width: 2px;
  }
  
  .errorMessage {
    box-shadow: var(--shadow-lg), 0 0 0 0.5px rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .loadingSpinner {
    animation: none;
  }
  
  .errorMessage {
    animation: none;
  }
  
  .app,
  .select,
  .retryButton {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
}/*
 Offline indicator */
.offlineIndicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #ff9800;
  color: white;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.9rem;
  z-index: 1002;
  animation: slideDown 0.3s ease-out;
  /* Safe area support */
  padding-top: max(0.5rem, env(safe-area-inset-top));
  padding-left: max(0.5rem, env(safe-area-inset-left));
  padding-right: max(0.5rem, env(safe-area-inset-right));
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Battery optimization styles */
.app[data-battery-optimized="true"] * {
  animation-duration: 0.1s !important;
  transition-duration: 0.1s !important;
}

.app[data-battery-optimized="true"] .loadingSpinner {
  animation: none;
  border: 2px solid #ccc;
  border-top: 2px solid #3498db;
}

.app[data-battery-optimized="true"] .visual {
  /* Disable visual enhancements in battery save mode */
  background: none !important;
}

.app[data-battery-optimized="true"] .title {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  color: var(--primary-color) !important;
}

/* Mobile performance optimizations */
@media (max-width: 768px) {
  .app {
    /* Reduce repaints on mobile */
    will-change: auto;
    /* Enable hardware acceleration for smooth scrolling */
    transform: translateZ(0);
  }
  
  .app * {
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    /* Reduce paint complexity */
    backface-visibility: hidden;
  }
  
  /* Reduce animations on mobile for performance */
  .app.mobile-device * {
    animation-duration: 0.2s;
    transition-duration: 0.2s;
  }
  
  /* Optimize for touch devices */
  .app.touch-device {
    /* Improve touch responsiveness */
    touch-action: manipulation;
  }
  
  /* Optimize loading states for mobile */
  .loadingSpinner {
    /* Use CSS transforms for better performance */
    will-change: transform;
  }
  
  /* Reduce complexity of shadows on mobile */
  .errorMessage,
  .loadingOverlay,
  .inputArea {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* Low-end device optimizations */
@media (max-width: 768px) and (max-device-pixel-ratio: 1.5) {
  .app * {
    /* Disable expensive visual effects on low-end devices */
    backdrop-filter: none !important;
    filter: none !important;
  }
  
  .visual .header {
    background: var(--surface-color) !important;
  }
  
  .visual .title {
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    color: var(--primary-color) !important;
  }
}

/* Network-aware optimizations */
.app[data-offline="true"] {
  /* Reduce visual complexity when offline */
  filter: grayscale(0.2);
}

.app[data-offline="true"] .offlineIndicator {
  display: block;
}

/* Optimize for very small screens */
@media (max-width: 320px) {
  .app {
    font-size: 14px;
  }
  
  .title {
    font-size: 1rem;
  }
  
  .controls {
    font-size: 0.8rem;
  }
  
  .select {
    font-size: 14px;
    padding: 0.5rem;
  }
  
  .loadingOverlay,
  .errorContent {
    padding: 1rem;
    margin: 0.5rem;
  }
}

/* Optimize for slow connections */
@media (max-width: 768px) {
  .app {
    /* Prioritize content loading */
    contain: layout style paint;
  }
  
  /* Lazy loading placeholders */
  .lazyPlaceholder {
    background: var(--surface-color);
    border-radius: 4px;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
  
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    100% {
      opacity: 1;
    }
  }
}

/* Memory optimization for long conversations */
.app[data-mobile="true"] .conversationArea {
  /* Limit the number of rendered messages for performance */
  contain: strict;
  /* Enable virtual scrolling optimizations */
  overflow-anchor: none;
}

/* Optimize for different mobile orientations */
@media (orientation: portrait) and (max-width: 768px) {
  .app {
    /* Portrait-specific optimizations */
    --viewport-height: 100vh;
  }
}

@media (orientation: landscape) and (max-width: 768px) {
  .app {
    /* Landscape-specific optimizations */
    --viewport-height: 100vh;
  }
  
  /* Compact layout for landscape */
  .header {
    padding: 0.25rem 0;
  }
  
  .main {
    padding: 0 0.5rem;
  }
}

/* Optimize for devices with notches */
@supports (padding: max(0px)) {
  .app {
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
  }
  
  .header {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .inputArea {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}