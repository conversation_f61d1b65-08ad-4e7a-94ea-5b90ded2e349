"use strict";var f=Object.defineProperty;var r=(e,n)=>f(e,"name",{value:n,configurable:!0});var p=require("node:net"),u=require("./get-pipe-path-BoR10qr8.cjs");const g=r(()=>new Promise(e=>{const n=u.getPipePath(process.ppid),t=p.createConnection(n,()=>{e(r(a=>{const o=Buffer.from(JSON.stringify(a)),c=Buffer.alloc(4);c.writeInt32BE(o.length,0),t.write(Buffer.concat([c,o]))},"sendToParent"))});t.on("error",()=>{e()}),t.unref()}),"connectToServer"),s={send:void 0},i=g();i.then(e=>{s.send=e},()=>{}),exports.connectingToServer=i,exports.parent=s;
