import { useCallback } from 'react';
import { usePersonalityContext } from '../context/PersonalityContext';
import type { PersonalityConfig } from '../types/personality';

/**
 * Custom hook for managing personality state and actions
 * Provides convenient methods for personality operations
 */
export function usePersonality() {
  const { 
    personality, 
    isLoading, 
    error, 
    updatePersonality, 
    resetPersonality 
  } = usePersonalityContext();

  // Update specific personality traits
  const updatePersonalityTraits = useCallback(async (
    traits: Partial<PersonalityConfig['personality']>
  ) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    await updatePersonality({
      personality: {
        ...personality.personality,
        ...traits,
      },
    });
  }, [personality, updatePersonality]);

  // Update personality style
  const updatePersonalityStyle = useCallback(async (
    style: Partial<PersonalityConfig['personality']['style']>
  ) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    await updatePersonality({
      personality: {
        ...personality.personality,
        style: {
          ...personality.personality.style,
          ...style,
        },
      },
    });
  }, [personality, updatePersonality]);

  // Update personality boundaries
  const updatePersonalityBoundaries = useCallback(async (
    boundaries: Partial<PersonalityConfig['personality']['boundaries']>
  ) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    await updatePersonality({
      personality: {
        ...personality.personality,
        boundaries: {
          ...personality.personality.boundaries,
          ...boundaries,
        },
      },
    });
  }, [personality, updatePersonality]);

  // Update dynamic traits
  const updateDynamicTraits = useCallback(async (
    dynamicTraits: Partial<PersonalityConfig['personality']['dynamic_traits']>
  ) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    await updatePersonality({
      personality: {
        ...personality.personality,
        dynamic_traits: {
          ...personality.personality.dynamic_traits,
          ...dynamicTraits,
        },
      },
    });
  }, [personality, updatePersonality]);

  // Update conversation tips
  const updateConversationTips = useCallback(async (
    tips: Partial<PersonalityConfig['conversation_tips']>
  ) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    await updatePersonality({
      conversation_tips: {
        ...personality.conversation_tips,
        ...tips,
      },
    });
  }, [personality, updatePersonality]);

  // Add a hobby to the personality
  const addHobby = useCallback(async (hobby: string) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    const currentHobbies = personality.personality.hobbies;
    if (!currentHobbies.includes(hobby)) {
      await updatePersonalityTraits({
        hobbies: [...currentHobbies, hobby],
      });
    }
  }, [personality, updatePersonalityTraits]);

  // Remove a hobby from the personality
  const removeHobby = useCallback(async (hobby: string) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    const currentHobbies = personality.personality.hobbies;
    const updatedHobbies = currentHobbies.filter(h => h !== hobby);
    
    await updatePersonalityTraits({
      hobbies: updatedHobbies,
    });
  }, [personality, updatePersonalityTraits]);

  // Add a topic to avoid
  const addTopicToAvoid = useCallback(async (topic: string) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    const currentAvoid = personality.personality.boundaries.avoid;
    if (!currentAvoid.includes(topic)) {
      await updatePersonalityBoundaries({
        avoid: [...currentAvoid, topic],
      });
    }
  }, [personality, updatePersonalityBoundaries]);

  // Remove a topic from avoid list
  const removeTopicToAvoid = useCallback(async (topic: string) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    const currentAvoid = personality.personality.boundaries.avoid;
    const updatedAvoid = currentAvoid.filter(t => t !== topic);
    
    await updatePersonalityBoundaries({
      avoid: updatedAvoid,
    });
  }, [personality, updatePersonalityBoundaries]);

  // Add a safe topic
  const addSafeTopic = useCallback(async (topic: string) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    const currentSafe = personality.personality.boundaries.safe_topics;
    if (!currentSafe.includes(topic)) {
      await updatePersonalityBoundaries({
        safe_topics: [...currentSafe, topic],
      });
    }
  }, [personality, updatePersonalityBoundaries]);

  // Remove a safe topic
  const removeSafeTopic = useCallback(async (topic: string) => {
    if (!personality) {
      throw new Error('No personality loaded');
    }
    
    const currentSafe = personality.personality.boundaries.safe_topics;
    const updatedSafe = currentSafe.filter(t => t !== topic);
    
    await updatePersonalityBoundaries({
      safe_topics: updatedSafe,
    });
  }, [personality, updatePersonalityBoundaries]);

  // Get formatted personality prompt for AI services
  const getPersonalityPrompt = useCallback((): string => {
    if (!personality) {
      return '';
    }

    return `You are ${personality.name}, ${personality.personality.role}. 
Your pronouns are ${personality.pronouns}. 
Your tone is ${personality.personality.tone}.
Your speech style is ${personality.personality.style.speech}.
Your humor style is ${personality.personality.style.humor}.
Your conversation depth is ${personality.personality.style.depth}.
Your hobbies include: ${personality.personality.hobbies.join(', ')}.
You should avoid discussing: ${personality.personality.boundaries.avoid.join(', ')}.
Safe topics include: ${personality.personality.boundaries.safe_topics.join(', ')}.
${personality.personality.dynamic_traits.adaptive_empathy ? 'You adapt your empathy to the user\'s emotional state.' : ''}
${personality.personality.dynamic_traits.mirroring_style ? 'You mirror the user\'s communication style.' : ''}
${personality.personality.dynamic_traits.emotionally_available ? 'You are emotionally available and supportive.' : ''}`;
  }, [personality]);

  // Check if personality is ready to use
  const isPersonalityReady = useCallback((): boolean => {
    return !isLoading && !error && personality !== null;
  }, [isLoading, error, personality]);

  return {
    // State
    personality,
    isLoading,
    error,
    isPersonalityReady: isPersonalityReady(),
    
    // Actions
    updatePersonality,
    resetPersonality,
    updatePersonalityTraits,
    updatePersonalityStyle,
    updatePersonalityBoundaries,
    updateDynamicTraits,
    updateConversationTips,
    
    // Hobby management
    addHobby,
    removeHobby,
    
    // Topic management
    addTopicToAvoid,
    removeTopicToAvoid,
    addSafeTopic,
    removeSafeTopic,
    
    // Utility methods
    getPersonalityPrompt,
  };
}