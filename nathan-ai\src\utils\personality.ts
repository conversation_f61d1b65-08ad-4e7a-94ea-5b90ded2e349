import type { PersonalityConfig } from '../types/personality';
import { validatePersonalityConfig as validatePersonalityInput } from './inputValidation';
import { secureLog, sanitizeError } from './security';
import nathanDefaultPersonality from '../assets/personalities/nathan-default.json';
import { 
  savePersonalityPreferences, 
  loadPersonalityPreferences, 
  clearPersonalityPreferences 
} from './statePersistence';

/**
 * Validates a personality configuration object
 * @param config - The personality configuration to validate
 * @returns true if valid, false otherwise
 */
export function validatePersonalityConfig(config: any): config is PersonalityConfig {
  if (!config || typeof config !== 'object') {
    return false;
  }

  // Check required top-level properties
  const requiredProps = ['name', 'pronouns', 'personality', 'conversation_tips', 'version'];
  for (const prop of requiredProps) {
    if (!(prop in config)) {
      return false;
    }
  }

  // Validate personality object structure
  const personality = config.personality;
  if (!personality || typeof personality !== 'object') {
    return false;
  }

  const requiredPersonalityProps = ['tone', 'role', 'hobbies', 'style', 'boundaries', 'dynamic_traits'];
  for (const prop of requiredPersonalityProps) {
    if (!(prop in personality)) {
      return false;
    }
  }

  // Validate style object
  const style = personality.style;
  if (!style || typeof style !== 'object') {
    return false;
  }
  const requiredStyleProps = ['speech', 'humor', 'depth'];
  for (const prop of requiredStyleProps) {
    if (!(prop in style) || typeof style[prop] !== 'string') {
      return false;
    }
  }

  // Validate boundaries object
  const boundaries = personality.boundaries;
  if (!boundaries || typeof boundaries !== 'object') {
    return false;
  }
  if (!Array.isArray(boundaries.avoid) || !Array.isArray(boundaries.safe_topics)) {
    return false;
  }

  // Validate dynamic_traits object
  const dynamicTraits = personality.dynamic_traits;
  if (!dynamicTraits || typeof dynamicTraits !== 'object') {
    return false;
  }
  const requiredTraitProps = ['adaptive_empathy', 'mirroring_style', 'emotionally_available'];
  for (const prop of requiredTraitProps) {
    if (!(prop in dynamicTraits) || typeof dynamicTraits[prop] !== 'boolean') {
      return false;
    }
  }

  // Validate conversation_tips
  const conversationTips = config.conversation_tips;
  if (!conversationTips || typeof conversationTips !== 'object') {
    return false;
  }
  if (!Array.isArray(conversationTips.starter_prompts)) {
    return false;
  }

  // Validate basic types
  if (typeof config.name !== 'string' || 
      typeof config.pronouns !== 'string' || 
      typeof config.version !== 'string' ||
      typeof personality.tone !== 'string' ||
      typeof personality.role !== 'string' ||
      !Array.isArray(personality.hobbies)) {
    return false;
  }

  return true;
}

/**
 * Loads the default Nathan personality configuration
 * @returns The default personality configuration
 */
export function loadDefaultPersonality(): PersonalityConfig {
  return nathanDefaultPersonality as PersonalityConfig;
}

/**
 * Loads personality configuration from localStorage or returns default
 * @returns The personality configuration
 */
export function loadPersonalityConfig(): PersonalityConfig {
  const defaultPersonality = loadDefaultPersonality();
  
  try {
    const preferences = loadPersonalityPreferences();
    if (preferences) {
      // Merge preferences with default personality
      const merged = {
        ...defaultPersonality,
        ...preferences,
        personality: {
          ...defaultPersonality.personality,
          ...preferences.personality,
          style: {
            ...defaultPersonality.personality.style,
            ...(preferences.personality?.style || {})
          },
          boundaries: {
            ...defaultPersonality.personality.boundaries,
            ...(preferences.personality?.boundaries || {})
          },
          dynamic_traits: {
            ...defaultPersonality.personality.dynamic_traits,
            ...(preferences.personality?.dynamic_traits || {})
          }
        },
        conversation_tips: {
          ...defaultPersonality.conversation_tips,
          ...(preferences.conversation_tips || {})
        }
      };
      
      // Use enhanced validation
      const validation = validatePersonalityInput(merged);
      if (validation.isValid) {
        if (validation.warnings.length > 0) {
          secureLog('Personality configuration warnings', { warnings: validation.warnings });
        }
        return validation.sanitizedValue || merged;
      }
      
      secureLog('Invalid merged personality config, using default', {
        errors: validation.errors,
        warnings: validation.warnings,
      });
    }
  } catch (error) {
    secureLog('Error loading personality preferences', { error: sanitizeError(error) });
  }
  
  return defaultPersonality;
}

/**
 * Async version of loadPersonalityConfig for use in contexts
 * @returns Promise resolving to the personality configuration
 */
export async function loadPersonality(): Promise<PersonalityConfig> {
  return loadPersonalityConfig();
}

/**
 * Saves personality configuration to localStorage
 * @param config - The personality configuration to save
 * @throws Error if the configuration is invalid
 */
export function savePersonalityConfig(config: PersonalityConfig): void {
  // Use enhanced validation
  const validation = validatePersonalityInput(config);
  if (!validation.isValid) {
    const errorMessage = `Invalid personality configuration: ${validation.errors.join(', ')}`;
    secureLog('Failed to save personality config', { 
      errors: validation.errors,
      warnings: validation.warnings,
    });
    throw new Error(errorMessage);
  }
  
  if (validation.warnings.length > 0) {
    secureLog('Personality configuration warnings during save', { warnings: validation.warnings });
  }
  
  try {
    // Save the sanitized version
    const configToSave = validation.sanitizedValue || config;
    savePersonalityPreferences(configToSave);
    secureLog('Personality configuration saved successfully');
  } catch (error) {
    const errorMessage = `Failed to save personality configuration: ${sanitizeError(error)}`;
    secureLog('Error saving personality preferences', { error: sanitizeError(error) });
    throw new Error(errorMessage);
  }
}

/**
 * Async version of savePersonalityConfig for use in contexts
 * @param config - The personality configuration to save
 * @throws Error if the configuration is invalid
 */
export async function savePersonality(config: PersonalityConfig): Promise<void> {
  savePersonalityConfig(config);
}

/**
 * Updates specific parts of the personality configuration
 * @param updates - Partial personality configuration updates
 * @returns The updated personality configuration
 */
export function updatePersonalityConfig(updates: Partial<PersonalityConfig>): PersonalityConfig {
  const current = loadPersonalityConfig();
  const updated = { ...current, ...updates };
  
  // Deep merge personality object if provided
  if (updates.personality) {
    updated.personality = {
      ...current.personality,
      ...updates.personality,
      style: {
        ...current.personality.style,
        ...(updates.personality.style || {})
      },
      boundaries: {
        ...current.personality.boundaries,
        ...(updates.personality.boundaries || {})
      },
      dynamic_traits: {
        ...current.personality.dynamic_traits,
        ...(updates.personality.dynamic_traits || {})
      }
    };
  }
  
  // Deep merge conversation_tips if provided
  if (updates.conversation_tips) {
    updated.conversation_tips = {
      ...current.conversation_tips,
      ...updates.conversation_tips
    };
  }
  
  savePersonalityConfig(updated);
  return updated;
}

/**
 * Resets personality configuration to default
 * @returns The default personality configuration
 */
export function resetPersonalityConfig(): PersonalityConfig {
  const defaultConfig = loadDefaultPersonality();
  clearPersonalityPreferences();
  return defaultConfig;
}

/**
 * Creates a personality prompt for AI services
 * @param config - The personality configuration
 * @returns A formatted prompt string
 */
export function formatPersonalityPrompt(config: PersonalityConfig): string {
  const { personality } = config;
  
  return `You are ${config.name} (${config.pronouns}), a ${personality.role}. 

Your personality traits:
- Tone: ${personality.tone}
- Speech style: ${personality.style.speech}
- Humor: ${personality.style.humor}
- Conversation depth: ${personality.style.depth}
- Hobbies and interests: ${personality.hobbies.join(', ')}

Dynamic traits:
- Adaptive empathy: ${personality.dynamic_traits.adaptive_empathy ? 'enabled' : 'disabled'}
- Style mirroring: ${personality.dynamic_traits.mirroring_style ? 'enabled' : 'disabled'}
- Emotional availability: ${personality.dynamic_traits.emotionally_available ? 'enabled' : 'disabled'}

Boundaries:
- Avoid topics: ${personality.boundaries.avoid.join(', ')}
- Safe topics: ${personality.boundaries.safe_topics.join(', ')}

Respond naturally and authentically according to these personality traits.`;
}