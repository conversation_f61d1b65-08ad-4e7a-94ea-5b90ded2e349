import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useBrowserCompatibility } from '../hooks/useBrowserCompatibility';
import type { VoiceInputProps, VoiceInputState } from '../types/components';
import styles from './VoiceInput.module.css';

// Extend the Window interface to include webkitSpeechRecognition
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

export const VoiceInput: React.FC<VoiceInputProps> = ({
  onTranscript,
  onListeningChange,
  onError,
  isActive
}) => {
  const { 
    features, 
    browserInfo, 
    createSpeechRecognition,
    issues 
  } = useBrowserCompatibility();

  const [state, setState] = useState<VoiceInputState>({
    isListening: false,
    transcript: '',
    error: null,
    isSupported: features.webSpeechAPI
  });

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize speech recognition with browser compatibility
  useEffect(() => {
    if (!features.webSpeechAPI) {
      const speechIssue = issues.find(issue => issue.feature === 'webSpeechAPI');
      if (speechIssue) {
        setState(prev => ({ 
          ...prev, 
          error: speechIssue.message,
          isSupported: false 
        }));
        onError?.(speechIssue.message);
      }
      return;
    }

    try {
      const recognition = createSpeechRecognition();
      if (!recognition) {
        setState(prev => ({ 
          ...prev, 
          error: 'Speech recognition could not be initialized',
          isSupported: false 
        }));
        onError?.('Speech recognition could not be initialized');
        return;
      }

      // Configure recognition based on browser
      recognition.continuous = false;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      // Apply mobile-specific optimizations
      if (browserInfo.isMobile) {
        recognition.continuous = false; // Better for mobile
        recognition.interimResults = false; // Reduce processing on mobile
      }

      // Apply browser-specific optimizations
      if (browserInfo.isFirefox) {
        recognition.continuous = false; // Firefox has issues with continuous
        recognition.interimResults = false; // Firefox has limited interim support
      }

      recognitionRef.current = recognition;
      setState(prev => ({ ...prev, isSupported: true, error: null }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize speech recognition';
      setState(prev => ({ 
        ...prev, 
        error: errorMessage,
        isSupported: false 
      }));
      onError?.(errorMessage);
    }
  }, [features.webSpeechAPI, createSpeechRecognition, browserInfo, issues, onError]);

  // Handle recognition events
  useEffect(() => {
    const recognition = recognitionRef.current;
    if (!recognition) return;

    const handleResult = (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      const currentTranscript = finalTranscript || interimTranscript;
      setState(prev => ({ ...prev, transcript: currentTranscript, error: null }));

      if (finalTranscript) {
        onTranscript(finalTranscript.trim());
        stopListening();
      }
    };

    const handleError = (event: SpeechRecognitionErrorEvent) => {
      let errorMessage = 'Speech recognition error occurred';
      
      switch (event.error) {
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.';
          break;
        case 'audio-capture':
          errorMessage = 'Microphone not accessible. Please check permissions.';
          break;
        case 'not-allowed':
          errorMessage = 'Microphone access denied. Please allow microphone access.';
          break;
        case 'network':
          errorMessage = 'Network error occurred. Please check your connection.';
          break;
        case 'aborted':
          errorMessage = 'Speech recognition was aborted.';
          break;
        default:
          errorMessage = `Speech recognition error: ${event.error}`;
      }

      setState(prev => ({ ...prev, error: errorMessage, isListening: false }));
      onError(errorMessage);
      onListeningChange(false);
    };

    const handleStart = () => {
      setState(prev => ({ ...prev, isListening: true, error: null, transcript: '' }));
      onListeningChange(true);
      
      // Set timeout to stop listening after 30 seconds
      timeoutRef.current = setTimeout(() => {
        stopListening();
      }, 30000);
    };

    const handleEnd = () => {
      setState(prev => ({ ...prev, isListening: false }));
      onListeningChange(false);
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };

    recognition.addEventListener('result', handleResult);
    recognition.addEventListener('error', handleError);
    recognition.addEventListener('start', handleStart);
    recognition.addEventListener('end', handleEnd);

    return () => {
      recognition.removeEventListener('result', handleResult);
      recognition.removeEventListener('error', handleError);
      recognition.removeEventListener('start', handleStart);
      recognition.removeEventListener('end', handleEnd);
    };
  }, [onTranscript, onError, onListeningChange]);

  const startListening = useCallback(() => {
    const recognition = recognitionRef.current;
    if (!recognition || state.isListening) return;

    try {
      recognition.start();
    } catch (error) {
      const errorMessage = 'Failed to start speech recognition';
      setState(prev => ({ ...prev, error: errorMessage }));
      onError(errorMessage);
    }
  }, [state.isListening, onError]);

  const stopListening = useCallback(() => {
    const recognition = recognitionRef.current;
    if (!recognition || !state.isListening) return;

    recognition.stop();
  }, [state.isListening]);

  const toggleListening = useCallback(() => {
    if (state.isListening) {
      stopListening();
    } else {
      startListening();
    }
  }, [state.isListening, startListening, stopListening]);

  // Handle active prop changes
  useEffect(() => {
    if (!isActive && state.isListening) {
      stopListening();
    }
  }, [isActive, state.isListening, stopListening]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (recognitionRef.current && state.isListening) {
        recognitionRef.current.stop();
      }
    };
  }, [state.isListening]);

  if (!state.isSupported) {
    return (
      <div className={styles.voiceInputContainer}>
        <div className={styles.unsupportedMessage}>
          <svg className={styles.warningIcon} viewBox="0 0 24 24" fill="currentColor">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
          </svg>
          <span>Voice input is not supported in this browser</span>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.voiceInputContainer}>
      <div className={styles.controls}>
        <button
          onClick={toggleListening}
          disabled={!isActive}
          className={`${styles.micButton} ${state.isListening ? styles.listening : ''} ${!isActive ? styles.disabled : ''}`}
          aria-label={state.isListening ? 'Stop listening' : 'Start listening'}
        >
          {state.isListening ? (
            <div className={styles.listeningIndicator}>
              <div className={styles.pulseRing}></div>
              <svg className={styles.micIcon} viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
              </svg>
            </div>
          ) : (
            <svg className={styles.micIcon} viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
              <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
            </svg>
          )}
        </button>
      </div>

      {state.transcript && (
        <div className={styles.transcriptDisplay}>
          <span className={styles.transcriptText}>{state.transcript}</span>
        </div>
      )}

      {state.isListening && (
        <div className={styles.listeningStatus}>
          <div className={styles.statusIndicator}>
            <div className={styles.waveform}>
              <div className={styles.wave}></div>
              <div className={styles.wave}></div>
              <div className={styles.wave}></div>
            </div>
            <span>Listening...</span>
          </div>
        </div>
      )}

      {state.error && (
        <div className={styles.errorMessage}>
          <svg className={styles.errorIcon} viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <span>{state.error}</span>
        </div>
      )}
    </div>
  );
};