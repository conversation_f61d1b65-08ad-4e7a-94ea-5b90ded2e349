/* Mobile-First Responsive Design System */

/* CSS Custom Properties for Responsive Design */
:root {
  /* Breakpoints */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1200px;
  
  /* Spacing Scale - Mobile First */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 0.75rem;   /* 12px */
  --spacing-lg: 1rem;      /* 16px */
  --spacing-xl: 1.5rem;    /* 24px */
  --spacing-2xl: 2rem;     /* 32px */
  --spacing-3xl: 3rem;     /* 48px */
  
  /* Typography Scale - Mobile First */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  
  /* Touch Target Sizes */
  --touch-target-min: 44px; /* iOS/Android minimum */
  --touch-target-comfortable: 48px;
  
  /* Container Widths */
  --container-mobile: 100%;
  --container-tablet: 768px;
  --container-desktop: 1024px;
  --container-wide: 1200px;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Base Mobile-First Styles */
* {
  box-sizing: border-box;
}

html {
  /* Prevent horizontal scroll on mobile */
  overflow-x: hidden;
  /* Improve text rendering */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  /* Prevent bounce scrolling on iOS */
  -webkit-overflow-scrolling: touch;
  /* Improve touch scrolling */
  overscroll-behavior-y: contain;
}

/* Container System */
.container {
  width: 100%;
  max-width: var(--container-mobile);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-tablet);
    padding: 0 var(--spacing-xl);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: var(--container-desktop);
    padding: 0 var(--spacing-2xl);
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: var(--container-wide);
  }
}

/* Responsive Utilities */
.mobile-only {
  display: block;
}

.tablet-up {
  display: none;
}

.desktop-up {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .tablet-up {
    display: block;
  }
}

@media (min-width: 1024px) {
  .desktop-up {
    display: block;
  }
}

/* Touch-Friendly Interactive Elements */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-target-comfortable {
  min-height: var(--touch-target-comfortable);
  min-width: var(--touch-target-comfortable);
}

/* Mobile-Specific Interactions */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover-effect:hover {
    transform: none;
  }
  
  /* Increase touch targets */
  button,
  .button,
  [role="button"] {
    min-height: var(--touch-target-min);
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  /* Improve form controls */
  input,
  textarea,
  select {
    min-height: var(--touch-target-min);
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Orientation Handling */
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-compact {
    padding: var(--spacing-sm);
  }
  
  .landscape-hide {
    display: none;
  }
}

/* Safe Area Support for iOS */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(var(--spacing-lg), env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(var(--spacing-lg), env(safe-area-inset-bottom));
  }
  
  .safe-area-inset-left {
    padding-left: max(var(--spacing-lg), env(safe-area-inset-left));
  }
  
  .safe-area-inset-right {
    padding-right: max(var(--spacing-lg), env(safe-area-inset-right));
  }
}

/* Responsive Typography */
.text-responsive {
  font-size: var(--text-sm);
  line-height: 1.5;
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: var(--text-base);
    line-height: 1.6;
  }
}

@media (min-width: 1024px) {
  .text-responsive {
    font-size: var(--text-lg);
    line-height: 1.7;
  }
}

/* Responsive Spacing */
.spacing-responsive {
  padding: var(--spacing-md);
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: var(--spacing-xl);
  }
}

/* Mobile Navigation Patterns */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--surface-color);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-lg);
  z-index: var(--z-fixed);
}

@media (min-width: 768px) {
  .mobile-nav {
    position: static;
    border: none;
    padding: 0;
    background: transparent;
  }
}

/* Responsive Grid System */
.grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .grid-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-lg-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive Flexbox Utilities */
.flex-mobile-column {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

@media (min-width: 768px) {
  .flex-tablet-row {
    flex-direction: row;
    gap: var(--spacing-lg);
  }
}

/* Mobile-First Animation Performance */
@media (prefers-reduced-motion: no-preference) {
  .animate-mobile {
    transition: transform 0.2s ease, opacity 0.2s ease;
  }
  
  @media (min-width: 768px) {
    .animate-desktop {
      transition: all 0.3s ease;
    }
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-border {
    border-width: 0.5px;
  }
}

/* Accessibility Improvements */
@media (prefers-contrast: high) {
  .high-contrast {
    border-width: 2px;
    border-style: solid;
  }
}

@media (prefers-color-scheme: dark) {
  .dark-mode-responsive {
    background-color: var(--background-color);
    color: var(--text-color);
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-full-width {
    width: 100% !important;
    max-width: none !important;
  }
}

/* Responsive Image Utilities */
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

.img-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.img-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Mobile-Specific Layout Utilities */
.mobile-full-width {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

@media (min-width: 768px) {
  .mobile-full-width {
    width: auto;
    margin-left: 0;
  }
}

/* Responsive Overflow Handling */
.overflow-mobile-scroll {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.overflow-mobile-hidden {
  overflow: hidden;
}

@media (min-width: 768px) {
  .overflow-desktop-visible {
    overflow: visible;
  }
}

/* Mobile Gesture Support */
.swipe-area {
  touch-action: pan-x;
  -webkit-user-select: none;
  user-select: none;
}

.pinch-zoom {
  touch-action: pinch-zoom;
}

/* Responsive Modal/Dialog */
.modal-mobile {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
}

@media (min-width: 768px) {
  .modal-desktop {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: auto;
    height: auto;
    max-width: 90vw;
    max-height: 90vh;
  }
}