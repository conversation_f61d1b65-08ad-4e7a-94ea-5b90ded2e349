import { useCallback, useEffect, useRef, useState } from 'react';
import { useConversation } from './useConversation';
import { usePersonality } from './usePersonality';
import { AIService } from '../services/AIService';
import type { AudioQueueItem } from '../types/components';
import type { Message } from '../types/message';

/**
 * Custom hook for managing audio-enabled conversations
 * Integrates TTS generation with conversation flow and audio playback
 */
export function useAudioConversation() {
  const conversation = useConversation();
  const { personality } = usePersonality();
  const [audioQueue, setAudioQueue] = useState<AudioQueueItem[]>([]);
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  const aiServiceRef = useRef<AIService | null>(null);
  const audioUrlCacheRef = useRef<Map<string, string>>(new Map());

  // Initialize AI service
  useEffect(() => {
    if (!aiServiceRef.current) {
      aiServiceRef.current = new AIService({
        enableTTS: true,
        enableCaching: true,
        maxCacheSize: 50
      });
    }
  }, []);

  // Convert ArrayBuffer to blob URL for audio playback
  const createAudioUrl = useCallback((audioBuffer: ArrayBuffer): string => {
    const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
    return URL.createObjectURL(blob);
  }, []);

  // Clean up audio URLs to prevent memory leaks
  const cleanupAudioUrl = useCallback((url: string) => {
    URL.revokeObjectURL(url);
  }, []);

  // Send a message and generate audio response
  const sendMessageWithAudio = useCallback(async (userMessage: string) => {
    if (!aiServiceRef.current) {
      throw new Error('AI Service not initialized');
    }

    try {
      // Add user message to conversation
      const userMsg = conversation.addUserMessage(userMessage);
      
      // Set loading state
      conversation.setLoading(true);
      setIsGeneratingAudio(true);

      // Generate AI response with audio
      const response = await aiServiceRef.current.generateResponse(
        userMessage,
        personality,
        { max_length: 150, temperature: 0.8 },
        { voice_settings: { stability: 0.75, similarity_boost: 0.75 } }
      );

      // Create Nathan message
      let audioUrl: string | undefined;
      if (response.audio) {
        audioUrl = createAudioUrl(response.audio);
        // Cache the URL for cleanup later
        audioUrlCacheRef.current.set(userMsg.id, audioUrl);
      }

      const nathanMsg = conversation.addNathanMessage(
        response.text,
        response.emotion as Message['emotion'],
        audioUrl
      );

      // Add to audio queue for playback
      if (audioUrl) {
        const audioItem: AudioQueueItem = {
          id: nathanMsg.id,
          audioUrl,
          text: response.text,
          priority: 1
        };

        setAudioQueue(prev => [...prev, audioItem]);
      }

      return { userMessage: userMsg, nathanMessage: nathanMsg };

    } catch (error) {
      console.error('Error sending message with audio:', error);
      conversation.setError(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    } finally {
      conversation.setLoading(false);
      setIsGeneratingAudio(false);
    }
  }, [conversation, personality, createAudioUrl]);

  // Generate audio for existing message
  const generateAudioForMessage = useCallback(async (message: Message) => {
    if (!aiServiceRef.current || message.sender !== 'nathan') {
      return;
    }

    try {
      setIsGeneratingAudio(true);

      const audioBuffer = await aiServiceRef.current.generateAudio(
        message.content,
        { voice_settings: { stability: 0.75, similarity_boost: 0.75 } }
      );

      const audioUrl = createAudioUrl(audioBuffer);
      audioUrlCacheRef.current.set(message.id, audioUrl);

      // Update the message with audio URL
      const updatedMessage: Message = {
        ...message,
        audioUrl
      };

      // Add to conversation (this will replace the existing message)
      conversation.addMessage(updatedMessage);

      // Add to audio queue
      const audioItem: AudioQueueItem = {
        id: message.id,
        audioUrl,
        text: message.content,
        priority: 1
      };

      setAudioQueue(prev => [...prev, audioItem]);

    } catch (error) {
      console.error('Error generating audio for message:', error);
      conversation.setError(`Failed to generate audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGeneratingAudio(false);
    }
  }, [conversation, createAudioUrl]);

  // Handle audio playback state changes
  const handleAudioPlayingChange = useCallback((isPlaying: boolean) => {
    conversation.setPlaying(isPlaying);
  }, [conversation]);

  // Handle audio completion
  const handleAudioComplete = useCallback((audioId: string) => {
    // Remove completed audio from queue
    setAudioQueue(prev => prev.filter(item => item.id !== audioId));
    
    // Clean up audio URL
    const audioUrl = audioUrlCacheRef.current.get(audioId);
    if (audioUrl) {
      cleanupAudioUrl(audioUrl);
      audioUrlCacheRef.current.delete(audioId);
    }
  }, [cleanupAudioUrl]);

  // Handle audio errors
  const handleAudioError = useCallback((error: string) => {
    console.error('Audio playback error:', error);
    conversation.setError(`Audio playback failed: ${error}`);
  }, [conversation]);

  // Interrupt current audio playback (e.g., when user sends new message)
  const interruptAudio = useCallback(() => {
    // Clear the audio queue
    setAudioQueue([]);
    
    // Clean up all cached audio URLs
    audioUrlCacheRef.current.forEach((url) => {
      cleanupAudioUrl(url);
    });
    audioUrlCacheRef.current.clear();
    
    // Set playing state to false
    conversation.setPlaying(false);
  }, [conversation, cleanupAudioUrl]);

  // Send message with automatic audio interruption
  const sendMessage = useCallback(async (userMessage: string) => {
    // Interrupt any current audio playback
    interruptAudio();
    
    // Send message with audio
    return await sendMessageWithAudio(userMessage);
  }, [interruptAudio, sendMessageWithAudio]);

  // Clear conversation and audio
  const clearConversation = useCallback(() => {
    // Interrupt audio
    interruptAudio();
    
    // Clear conversation messages
    conversation.clearMessages();
    
    // Clear AI service history
    if (aiServiceRef.current) {
      aiServiceRef.current.clearConversationHistory();
    }
  }, [conversation, interruptAudio]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up all audio URLs
      audioUrlCacheRef.current.forEach((url) => {
        cleanupAudioUrl(url);
      });
      audioUrlCacheRef.current.clear();
    };
  }, [cleanupAudioUrl]);

  return {
    // Conversation state
    ...conversation,
    
    // Audio state
    audioQueue,
    isGeneratingAudio,
    
    // Actions
    sendMessage,
    sendMessageWithAudio,
    generateAudioForMessage,
    interruptAudio,
    clearConversation,
    
    // Audio event handlers
    handleAudioPlayingChange,
    handleAudioComplete,
    handleAudioError,
    
    // Utilities
    aiService: aiServiceRef.current,
  };
}