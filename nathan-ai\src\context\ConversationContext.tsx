import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import type { ConversationState, ConversationAction } from '../types/conversation';
import type { Message } from '../types/message';

// Initial state for the conversation
const initialConversationState: ConversationState = {
  messages: [],
  isLoading: false,
  inputMode: 'text',
  visualMode: 'visual',
  isListening: false,
  isPlaying: false,
  error: null,
};

// Conversation reducer function
export function conversationReducer(
  state: ConversationState,
  action: ConversationAction
): ConversationState {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
        error: null, // Clear any previous errors when adding a message
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'SET_INPUT_MODE':
      return {
        ...state,
        inputMode: action.payload,
        // Stop listening when switching away from voice mode
        isListening: action.payload === 'voice' ? state.isListening : false,
      };

    case 'SET_VISUAL_MODE':
      return {
        ...state,
        visualMode: action.payload,
      };

    case 'SET_LISTENING':
      return {
        ...state,
        isListening: action.payload,
        error: action.payload ? null : state.error, // Clear error when starting to listen
      };

    case 'SET_PLAYING':
      return {
        ...state,
        isPlaying: action.payload,
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false, // Stop loading when there's an error
        isListening: false, // Stop listening when there's an error
      };

    case 'CLEAR_MESSAGES':
      return {
        ...state,
        messages: [],
        error: null,
      };

    default:
      return state;
  }
}

// Context type definition
interface ConversationContextType {
  state: ConversationState;
  dispatch: React.Dispatch<ConversationAction>;
}

// Create the context
const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

// Provider component props
interface ConversationProviderProps {
  children: ReactNode;
  initialState?: Partial<ConversationState>;
}

// Provider component
export function ConversationProvider({ 
  children, 
  initialState 
}: ConversationProviderProps) {
  const [state, dispatch] = useReducer(
    conversationReducer,
    { ...initialConversationState, ...initialState }
  );

  return (
    <ConversationContext.Provider value={{ state, dispatch }}>
      {children}
    </ConversationContext.Provider>
  );
}

// Hook to use the conversation context
export function useConversationContext(): ConversationContextType {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversationContext must be used within a ConversationProvider');
  }
  return context;
}