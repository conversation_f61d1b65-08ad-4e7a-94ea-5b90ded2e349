import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useMobileResponsive } from '../hooks/useMobileResponsive';

// Mock window properties
const mockMatchMedia = vi.fn();
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

Object.defineProperty(window, 'addEventListener', {
  writable: true,
  value: mockAddEventListener,
});

Object.defineProperty(window, 'removeEventListener', {
  writable: true,
  value: mockRemoveEventListener,
});

describe('useMobileResponsive', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset window dimensions
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 768,
    });

    // Reset user agent
    Object.defineProperty(navigator, 'userAgent', {
      writable: true,
      configurable: true,
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    });

    // Default matchMedia mock
    mockMatchMedia.mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('basic functionality', () => {
    it('detects desktop by default', () => {
      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(false);
      expect(result.current.isTablet).toBe(false);
      expect(result.current.isDesktop).toBe(true);
      expect(result.current.screenSize).toBe('desktop');
    });

    it('detects mobile by screen width', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(true);
      expect(result.current.isTablet).toBe(false);
      expect(result.current.isDesktop).toBe(false);
      expect(result.current.screenSize).toBe('mobile');
    });

    it('detects tablet by screen width', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 800,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(false);
      expect(result.current.isTablet).toBe(true);
      expect(result.current.isDesktop).toBe(false);
      expect(result.current.screenSize).toBe('tablet');
    });

    it('detects mobile by user agent', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        configurable: true,
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(true);
    });
  });

  describe('orientation detection', () => {
    it('detects landscape orientation', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 800,
      });

      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 600,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.orientation).toBe('landscape');
      expect(result.current.isLandscape).toBe(true);
      expect(result.current.isPortrait).toBe(false);
    });

    it('detects portrait orientation', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.orientation).toBe('portrait');
      expect(result.current.isLandscape).toBe(false);
      expect(result.current.isPortrait).toBe(true);
    });
  });

  describe('touch capability detection', () => {
    it('detects touch capability', () => {
      Object.defineProperty(window, 'ontouchstart', {
        writable: true,
        value: null,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.hasTouch).toBe(true);
    });

    it('detects no touch capability', () => {
      Object.defineProperty(window, 'ontouchstart', {
        writable: true,
        value: undefined,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.hasTouch).toBe(false);
    });
  });

  describe('responsive breakpoints', () => {
    it('provides correct breakpoint values', () => {
      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.breakpoints).toEqual({
        mobile: 768,
        tablet: 1024,
        desktop: 1200,
      });
    });

    it('matches custom breakpoints', () => {
      const customBreakpoints = {
        mobile: 600,
        tablet: 900,
        desktop: 1200,
      };

      const { result } = renderHook(() => useMobileResponsive(customBreakpoints));

      expect(result.current.breakpoints).toEqual(customBreakpoints);
    });
  });

  describe('event listeners', () => {
    it('sets up resize event listener', () => {
      renderHook(() => useMobileResponsive());

      expect(mockAddEventListener).toHaveBeenCalledWith(
        'resize',
        expect.any(Function)
      );
    });

    it('sets up orientationchange event listener', () => {
      renderHook(() => useMobileResponsive());

      expect(mockAddEventListener).toHaveBeenCalledWith(
        'orientationchange',
        expect.any(Function)
      );
    });

    it('cleans up event listeners on unmount', () => {
      const { unmount } = renderHook(() => useMobileResponsive());

      unmount();

      expect(mockRemoveEventListener).toHaveBeenCalledWith(
        'resize',
        expect.any(Function)
      );
      expect(mockRemoveEventListener).toHaveBeenCalledWith(
        'orientationchange',
        expect.any(Function)
      );
    });
  });

  describe('responsive updates', () => {
    it('updates on window resize', () => {
      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(false);

      // Simulate resize to mobile
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      act(() => {
        // Trigger resize event
        const resizeCallback = mockAddEventListener.mock.calls.find(
          call => call[0] === 'resize'
        )?.[1];
        resizeCallback?.();
      });

      expect(result.current.isMobile).toBe(true);
      expect(result.current.screenSize).toBe('mobile');
    });

    it('updates on orientation change', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.orientation).toBe('portrait');

      // Simulate orientation change
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 667,
      });

      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 375,
      });

      act(() => {
        // Trigger orientationchange event
        const orientationCallback = mockAddEventListener.mock.calls.find(
          call => call[0] === 'orientationchange'
        )?.[1];
        orientationCallback?.();
      });

      expect(result.current.orientation).toBe('landscape');
    });
  });

  describe('media query integration', () => {
    it('uses media queries for hover capability', () => {
      renderHook(() => useMobileResponsive());

      expect(mockMatchMedia).toHaveBeenCalledWith('(hover: hover)');
    });

    it('uses media queries for pointer precision', () => {
      renderHook(() => useMobileResponsive());

      expect(mockMatchMedia).toHaveBeenCalledWith('(pointer: fine)');
    });

    it('detects hover capability', () => {
      mockMatchMedia.mockImplementation(query => ({
        matches: query === '(hover: hover)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.canHover).toBe(true);
    });

    it('detects fine pointer', () => {
      mockMatchMedia.mockImplementation(query => ({
        matches: query === '(pointer: fine)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.hasFinePointer).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('handles missing window object', () => {
      const originalWindow = global.window;
      // @ts-ignore
      delete global.window;

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(false);
      expect(result.current.screenSize).toBe('desktop');

      global.window = originalWindow;
    });

    it('handles missing navigator object', () => {
      const originalNavigator = global.navigator;
      // @ts-ignore
      delete global.navigator;

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.isMobile).toBe(false);

      global.navigator = originalNavigator;
    });

    it('handles missing matchMedia', () => {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: undefined,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.canHover).toBe(false);
      expect(result.current.hasFinePointer).toBe(false);
    });

    it('handles zero dimensions gracefully', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 0,
      });

      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 0,
      });

      const { result } = renderHook(() => useMobileResponsive());

      expect(result.current.screenSize).toBe('mobile');
      expect(result.current.orientation).toBe('portrait');
    });
  });

  describe('performance', () => {
    it('debounces resize events', () => {
      vi.useFakeTimers();

      const { result } = renderHook(() => useMobileResponsive());

      const initialScreenSize = result.current.screenSize;

      // Simulate multiple rapid resize events
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const resizeCallback = mockAddEventListener.mock.calls.find(
        call => call[0] === 'resize'
      )?.[1];

      act(() => {
        resizeCallback?.();
        resizeCallback?.();
        resizeCallback?.();
      });

      // Should not update immediately
      expect(result.current.screenSize).toBe(initialScreenSize);

      // Should update after debounce delay
      act(() => {
        vi.advanceTimersByTime(100);
      });

      expect(result.current.screenSize).toBe('mobile');

      vi.useRealTimers();
    });
  });
});