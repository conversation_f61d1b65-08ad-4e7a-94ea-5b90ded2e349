// Security utilities and configuration
import { config } from './env';

export interface SecurityConfig {
  apiRateLimit: {
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
  };
  headers: {
    contentSecurityPolicy: string;
    xFrameOptions: string;
    xContentTypeOptions: string;
    referrerPolicy: string;
  };
  validation: {
    maxInputLength: number;
    allowedCharacters: RegExp;
  };
}

// Security configuration based on environment
export const securityConfig: SecurityConfig = {
  apiRateLimit: {
    maxRequestsPerMinute: config.isProduction ? 30 : 60,
    maxRequestsPerHour: config.isProduction ? 500 : 1000,
  },
  headers: {
    contentSecurityPolicy: config.isProduction 
      ? "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io; media-src 'self' blob:; worker-src 'self' blob:;"
      : "default-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io ws: wss:; media-src 'self' blob:; worker-src 'self' blob:;",
    xFrameOptions: 'DENY',
    xContentTypeOptions: 'nosniff',
    referrerPolicy: 'strict-origin-when-cross-origin',
  },
  validation: {
    maxInputLength: 2000,
    allowedCharacters: /^[\w\s\p{P}\p{S}]*$/u, // Unicode word chars, whitespace, punctuation, symbols
  },
};

// Rate limiting tracker
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  isAllowed(identifier: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get existing requests for this identifier
    const existingRequests = this.requests.get(identifier) || [];
    
    // Filter out requests outside the window
    const recentRequests = existingRequests.filter(time => time > windowStart);
    
    // Check if we're under the limit
    if (recentRequests.length >= maxRequests) {
      return false;
    }
    
    // Add current request and update
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    
    return true;
  }
  
  getRemainingRequests(identifier: string, maxRequests: number, windowMs: number): number {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    const existingRequests = this.requests.get(identifier) || [];
    const recentRequests = existingRequests.filter(time => time > windowStart);
    
    return Math.max(0, maxRequests - recentRequests.length);
  }
  
  reset(identifier?: string): void {
    if (identifier) {
      this.requests.delete(identifier);
    } else {
      this.requests.clear();
    }
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();

// API rate limiting helper
export const checkApiRateLimit = (apiType: 'huggingface' | 'elevenlabs'): boolean => {
  const identifier = `${apiType}_api`;
  const { maxRequestsPerMinute, maxRequestsPerHour } = securityConfig.apiRateLimit;
  
  // Check both minute and hour limits
  const minuteAllowed = rateLimiter.isAllowed(
    `${identifier}_minute`, 
    maxRequestsPerMinute, 
    60 * 1000
  );
  
  const hourAllowed = rateLimiter.isAllowed(
    `${identifier}_hour`, 
    maxRequestsPerHour, 
    60 * 60 * 1000
  );
  
  return minuteAllowed && hourAllowed;
};

// Get remaining API requests
export const getRemainingApiRequests = (apiType: 'huggingface' | 'elevenlabs'): {
  perMinute: number;
  perHour: number;
} => {
  const identifier = `${apiType}_api`;
  const { maxRequestsPerMinute, maxRequestsPerHour } = securityConfig.apiRateLimit;
  
  return {
    perMinute: rateLimiter.getRemainingRequests(
      `${identifier}_minute`, 
      maxRequestsPerMinute, 
      60 * 1000
    ),
    perHour: rateLimiter.getRemainingRequests(
      `${identifier}_hour`, 
      maxRequestsPerHour, 
      60 * 60 * 1000
    ),
  };
};

// Input sanitization
export const sanitizeInput = (input: string): string => {
  if (!input || typeof input !== 'string') {
    return '';
  }
  
  // Trim and limit length
  let sanitized = input.trim().slice(0, securityConfig.validation.maxInputLength);
  
  // Remove potentially dangerous characters
  sanitized = sanitized.replace(/<[^>]*>/g, ''); // Remove HTML tags completely
  sanitized = sanitized.replace(/javascript:/gi, ''); // Remove javascript: protocol
  sanitized = sanitized.replace(/data:/gi, ''); // Remove data: protocol
  sanitized = sanitized.replace(/vbscript:/gi, ''); // Remove vbscript: protocol
  
  // Validate against allowed characters
  if (!securityConfig.validation.allowedCharacters.test(sanitized)) {
    // If validation fails, remove non-allowed characters
    sanitized = sanitized.replace(/[^\w\s\p{P}\p{S}]/gu, '');
  }
  
  return sanitized;
};

// Validate input format
export const validateInput = (input: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!input || typeof input !== 'string') {
    errors.push('Input must be a non-empty string');
    return { isValid: false, errors };
  }
  
  if (input.length > securityConfig.validation.maxInputLength) {
    errors.push(`Input exceeds maximum length of ${securityConfig.validation.maxInputLength} characters`);
  }
  
  if (!securityConfig.validation.allowedCharacters.test(input)) {
    errors.push('Input contains invalid characters');
  }
  
  // Check for potential XSS patterns
  const xssPatterns = [
    /<script/i,
    /javascript:/i,
    /data:text\/html/i,
    /vbscript:/i,
    /onload=/i,
    /onerror=/i,
    /onclick=/i,
  ];
  
  for (const pattern of xssPatterns) {
    if (pattern.test(input)) {
      errors.push('Input contains potentially malicious content');
      break;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Security headers helper for development
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    'Content-Security-Policy': securityConfig.headers.contentSecurityPolicy,
    'X-Frame-Options': securityConfig.headers.xFrameOptions,
    'X-Content-Type-Options': securityConfig.headers.xContentTypeOptions,
    'Referrer-Policy': securityConfig.headers.referrerPolicy,
  };
};

// API key masking for logging
export const maskApiKey = (key: string): string => {
  if (!key || key.length < 8) {
    return '[INVALID]';
  }
  
  const start = key.slice(0, 4);
  const end = key.slice(-4);
  const middle = '*'.repeat(24); // Fixed length for consistency
  
  return `${start}${middle}${end}`;
};

// Secure logging helper
export const secureLog = (message: string, data?: any): void => {
  if (config.isProduction) {
    // In production, don't log sensitive data
    console.log(message);
  } else {
    // In development, log with data but mask sensitive fields
    if (data) {
      const sanitizedData = { ...data };
      
      // Mask API keys in logs
      if (sanitizedData.apiKey) {
        sanitizedData.apiKey = maskApiKey(sanitizedData.apiKey);
      }
      
      if (sanitizedData.authorization) {
        sanitizedData.authorization = '[MASKED]';
      }
      
      console.log(message, sanitizedData);
    } else {
      console.log(message);
    }
  }
};

// Error sanitization for user display
export const sanitizeError = (error: any): string => {
  if (!error) {
    return 'An unknown error occurred';
  }
  
  // Don't expose internal error details in production
  if (config.isProduction) {
    // Generic error messages for production
    if (error.status === 401 || error.status === 403) {
      return 'Authentication failed. Please check your API configuration.';
    }
    
    if (error.status === 429) {
      return 'Too many requests. Please wait a moment and try again.';
    }
    
    if (error.status >= 500) {
      return 'Service temporarily unavailable. Please try again later.';
    }
    
    return 'An error occurred while processing your request.';
  }
  
  // More detailed errors for development
  return error.message || error.toString() || 'An unknown error occurred';
};