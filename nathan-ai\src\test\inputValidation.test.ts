import { describe, it, expect, beforeEach } from 'vitest';
import {
  validateUserMessage,
  validatePersonalityConfig,
  validateFileUpload,
} from '../utils/inputValidation';

describe('Input Validation', () => {
  describe('validateUserMessage', () => {
    it('should validate normal messages', () => {
      const result = validateUserMessage('Hello, how are you?');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitizedValue).toBe('Hello, how are you?');
    });

    it('should reject empty messages', () => {
      const result = validateUserMessage('');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('non-empty string'))).toBe(true);
    });

    it('should warn about very short messages', () => {
      const result = validateUserMessage('Hi');
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => warning.includes('very short'))).toBe(true);
    });

    it('should warn about very long messages', () => {
      const longMessage = 'a'.repeat(1500);
      const result = validateUserMessage(longMessage);
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => warning.includes('Very long'))).toBe(true);
    });

    it('should reject messages with excessive repeated characters', () => {
      const result = validateUserMessage('aaaaaaaaaaaaa hello');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('repeated characters'))).toBe(true);
    });

    it('should warn about excessive special characters', () => {
      const result = validateUserMessage('!@#$%^&*()_+{}|:"<>?');
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => warning.includes('special characters'))).toBe(true);
    });

    it('should sanitize HTML content', () => {
      const result = validateUserMessage('Hello <script>alert("xss")</script> world');
      expect(result.sanitizedValue).toBe('Hello alert("xss") world');
    });

    it('should handle null and undefined input', () => {
      expect(validateUserMessage(null as any).isValid).toBe(false);
      expect(validateUserMessage(undefined as any).isValid).toBe(false);
    });
  });

  describe('validatePersonalityConfig', () => {
    const validConfig = {
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'assistant',
        hobbies: ['coding', 'music'],
        style: {
          speech: 'casual',
          humor: 'light',
          depth: 'moderate',
        },
        boundaries: {
          avoid: ['politics'],
          safe_topics: ['technology', 'music'],
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: false,
          emotionally_available: true,
        },
      },
      conversation_tips: {
        starter_prompts: ['How can I help you today?'],
      },
      version: '1.0.0',
    };

    it('should validate correct personality config', () => {
      const result = validatePersonalityConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject null or undefined config', () => {
      expect(validatePersonalityConfig(null).isValid).toBe(false);
      expect(validatePersonalityConfig(undefined).isValid).toBe(false);
    });

    it('should reject config missing required fields', () => {
      const invalidConfig = { ...validConfig };
      delete (invalidConfig as any).name;
      
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Missing required field: name'))).toBe(true);
    });

    it('should validate name field', () => {
      const invalidConfig = { ...validConfig, name: 123 };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Name must be a string'))).toBe(true);
    });

    it('should validate name length', () => {
      const longName = 'a'.repeat(100);
      const invalidConfig = { ...validConfig, name: longName };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Name must be between 1 and 50 characters'))).toBe(true);
    });

    it('should validate pronouns field', () => {
      const invalidConfig = { ...validConfig, pronouns: 123 };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Pronouns must be a string'))).toBe(true);
    });

    it('should validate personality object structure', () => {
      const invalidConfig = { ...validConfig, personality: 'invalid' };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Personality must be an object'))).toBe(true);
    });

    it('should validate hobbies array', () => {
      const invalidConfig = {
        ...validConfig,
        personality: {
          ...validConfig.personality,
          hobbies: 'not an array',
        },
      };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Hobbies must be an array'))).toBe(true);
    });

    it('should warn about too many hobbies', () => {
      const manyHobbies = Array(25).fill('hobby');
      const configWithManyHobbies = {
        ...validConfig,
        personality: {
          ...validConfig.personality,
          hobbies: manyHobbies,
        },
      };
      const result = validatePersonalityConfig(configWithManyHobbies);
      expect(result.warnings.some(warning => warning.includes('Too many hobbies'))).toBe(true);
    });

    it('should validate style object', () => {
      const invalidConfig = {
        ...validConfig,
        personality: {
          ...validConfig.personality,
          style: {
            speech: 123, // Should be string
            humor: 'light',
            depth: 'moderate',
          },
        },
      };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Style speech must be a string'))).toBe(true);
    });

    it('should validate boundaries arrays', () => {
      const invalidConfig = {
        ...validConfig,
        personality: {
          ...validConfig.personality,
          boundaries: {
            avoid: 'not an array',
            safe_topics: ['technology'],
          },
        },
      };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Boundaries avoid must be an array'))).toBe(true);
    });

    it('should validate dynamic traits boolean values', () => {
      const invalidConfig = {
        ...validConfig,
        personality: {
          ...validConfig.personality,
          dynamic_traits: {
            adaptive_empathy: 'not a boolean',
            mirroring_style: false,
            emotionally_available: true,
          },
        },
      };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Dynamic trait adaptive_empathy must be a boolean'))).toBe(true);
    });

    it('should validate conversation tips', () => {
      const invalidConfig = {
        ...validConfig,
        conversation_tips: {
          starter_prompts: 'not an array',
        },
      };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Starter prompts must be an array'))).toBe(true);
    });

    it('should validate version format', () => {
      const invalidConfig = { ...validConfig, version: 'invalid-version' };
      const result = validatePersonalityConfig(invalidConfig);
      expect(result.isValid).toBe(true); // Should still be valid
      expect(result.warnings.some(warning => warning.includes('semantic versioning'))).toBe(true);
    });

    it('should sanitize personality config', () => {
      const configWithHtml = {
        ...validConfig,
        name: 'Nathan<script>alert("xss")</script>',
      };
      const result = validatePersonalityConfig(configWithHtml);
      expect(result.sanitizedValue?.name).toBe('Nathanalert("xss")');
    });
  });

  describe('validateFileUpload', () => {
    it('should validate JSON files', () => {
      const file = new File(['{}'], 'test.json', { type: 'application/json' });
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-JSON files', () => {
      const file = new File(['content'], 'test.txt', { type: 'text/plain' });
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('must be a JSON file'))).toBe(true);
    });

    it('should validate file extension for JSON', () => {
      const file = new File(['{}'], 'test.json', { type: 'text/plain' });
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(true); // Should pass because of .json extension
    });

    it('should reject files that are too large', () => {
      const largeContent = 'a'.repeat(2 * 1024 * 1024); // 2MB
      const file = new File([largeContent], 'test.json', { type: 'application/json' });
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('less than 1MB'))).toBe(true);
    });

    it('should warn about long file names', () => {
      const longName = 'a'.repeat(150) + '.json';
      const file = new File(['{}'], longName, { type: 'application/json' });
      const result = validateFileUpload(file);
      expect(result.warnings.some(warning => warning.includes('very long'))).toBe(true);
    });

    it('should sanitize file names', () => {
      const file = new File(['{}'], 'test<script>.json', { type: 'application/json' });
      const result = validateFileUpload(file);
      expect(result.sanitizedValue).toBe('testscript.json');
    });
  });
});