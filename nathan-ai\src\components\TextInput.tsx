import React, { useState, useRef, useEffect } from 'react';
import type { KeyboardEvent, ChangeEvent } from 'react';
import type { TextInputProps } from '../types/components';
import { validateUserMessage } from '../utils/inputValidation';
import { secureLog } from '../utils/security';
import styles from './TextInput.module.css';

export const TextInput: React.FC<TextInputProps> = ({
  onMessageSend,
  isLoading,
  placeholder = "Type your message...",
  maxLength = 1000
}) => {
  const [message, setMessage] = useState('');
  const [charCount, setCharCount] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [message]);

  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setMessage(value);
      setCharCount(value.length);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !isLoading) {
      // Validate the message before sending
      const validation = validateUserMessage(trimmedMessage);
      
      if (!validation.isValid) {
        secureLog('Invalid message input', { 
          errors: validation.errors,
          messageLength: trimmedMessage.length 
        });
        // You could show an error message to the user here
        return;
      }
      
      if (validation.warnings.length > 0) {
        secureLog('Message validation warnings', { warnings: validation.warnings });
      }
      
      // Send the sanitized message
      const sanitizedMessage = validation.sanitizedValue || trimmedMessage;
      onMessageSend(sanitizedMessage);
      setMessage('');
      setCharCount(0);
    }
  };

  const isDisabled = isLoading || !message.trim();

  return (
    <div className={styles.textInputContainer}>
      <div className={styles.inputWrapper}>
        <textarea
          ref={textareaRef}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={isLoading}
          className={`${styles.textarea} ${isLoading ? styles.disabled : ''}`}
          rows={1}
          maxLength={maxLength}
        />
        <button
          onClick={handleSend}
          disabled={isDisabled}
          className={`${styles.sendButton} ${isDisabled ? styles.disabled : ''}`}
          aria-label="Send message"
        >
          {isLoading ? (
            <div className={styles.loadingSpinner} />
          ) : (
            <svg className={styles.sendIcon} viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
          )}
        </button>
      </div>
      <div className={styles.footer}>
        <span className={`${styles.charCount} ${charCount >= maxLength * 0.9 ? styles.warning : ''}`}>
          {charCount}/{maxLength}
        </span>
      </div>
    </div>
  );
};