{"name": "nathan-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:prod": "node scripts/build.cjs", "deploy": "node scripts/deploy.cjs", "deploy:netlify": "DEPLOYMENT_TARGET=netlify node scripts/deploy.cjs", "deploy:vercel": "DEPLOYMENT_TARGET=vercel node scripts/deploy.cjs", "deploy:github": "DEPLOYMENT_TARGET=github-pages node scripts/deploy.cjs", "deploy:static": "DEPLOYMENT_TARGET=static node scripts/deploy.cjs", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "vitest --run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:performance": "vitest --run src/test/performance.test.ts", "preview": "vite preview", "health": "tsx src/utils/healthCheck.ts"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "dotenv": "^17.2.1", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4"}}