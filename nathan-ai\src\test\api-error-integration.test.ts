import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AIService } from '../services/AIService';
import { HFError, ELError } from '../types/api';
import type { PersonalityConfig } from '../types/personality';

// Mock environment variables
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  }
}));

// Mock the API clients
vi.mock('../services/HuggingFaceClient');
vi.mock('../services/ElevenLabsClient');

// Mock window.navigator
Object.defineProperty(window, 'navigator', {
  value: {
    onLine: true
  },
  writable: true
});

// Mock window.addEventListener
const mockAddEventListener = vi.fn();
Object.defineProperty(window, 'addEventListener', {
  value: mockAddEventListener,
  writable: true
});

// Mock global notification system
const mockAddNotification = vi.fn();
(window as any).__addNotification = mockAddNotification;

describe('API Error Handling Integration', () => {
  let aiService: AIService;
  let mockPersonality: PersonalityConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAddNotification.mockClear();
    
    aiService = new AIService();
    mockPersonality = {
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'companion',
        hobbies: ['coding'],
        style: {
          speech: 'casual',
          humor: 'light',
          depth: 'thoughtful'
        },
        boundaries: {
          avoid: [],
          safe_topics: []
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: []
      },
      version: '1.0.0'
    };
  });

  afterEach(() => {
    delete (window as any).__addNotification;
  });

  describe('HuggingFace Error Scenarios', () => {
    it('should handle HuggingFace authentication errors gracefully', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const mockClient = vi.mocked(HuggingFaceClient);
      
      mockClient.prototype.generatePersonalityResponse = vi.fn()
        .mockRejectedValue(new HFError('Unauthorized', 401));

      await expect(
        aiService.generateTextResponse('Hello', mockPersonality)
      ).rejects.toThrow('Failed to generate text response');

      // Should show authentication error notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Authentication Error',
          message: expect.stringContaining('Invalid API key')
        })
      );
    });

    it('should retry on HuggingFace server errors', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const mockClient = vi.mocked(HuggingFaceClient);
      
      mockClient.prototype.generatePersonalityResponse = vi.fn()
        .mockRejectedValueOnce(new HFError('Server error', 503))
        .mockRejectedValueOnce(new HFError('Server error', 503))
        .mockResolvedValue('Hello there!');

      const result = await aiService.generateTextResponse('Hello', mockPersonality);
      
      expect(result).toBe('Hello there!');
      expect(mockClient.prototype.generatePersonalityResponse).toHaveBeenCalledTimes(3);
    });

    it('should handle HuggingFace rate limiting', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const mockClient = vi.mocked(HuggingFaceClient);
      
      mockClient.prototype.generatePersonalityResponse = vi.fn()
        .mockRejectedValue(new HFError('Rate limited', 429));

      await expect(
        aiService.generateTextResponse('Hello', mockPersonality)
      ).rejects.toThrow('Failed to generate text response');

      // Should show rate limiting notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Rate Limited',
          message: expect.stringContaining('Too many requests')
        })
      );
    });
  });

  describe('ElevenLabs Error Scenarios', () => {
    it('should handle ElevenLabs errors gracefully in full response', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const { ElevenLabsClient } = await import('../services/ElevenLabsClient');
      
      const mockHFClient = vi.mocked(HuggingFaceClient);
      const mockELClient = vi.mocked(ElevenLabsClient);
      
      mockHFClient.prototype.generatePersonalityResponse = vi.fn()
        .mockResolvedValue('Hello there!');
      
      mockELClient.prototype.textToSpeech = vi.fn()
        .mockRejectedValue(new ELError('Voice service unavailable', 503));

      const response = await aiService.generateResponse('Hello', mockPersonality);
      
      expect(response.text).toBe('Hello there!');
      expect(response.audio).toBeUndefined(); // Should continue without audio
      
      // Should show voice service error notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Voice Service Unavailable',
          message: expect.stringContaining('Voice synthesis temporarily unavailable')
        })
      );
    });

    it('should retry ElevenLabs audio generation', async () => {
      const { ElevenLabsClient } = await import('../services/ElevenLabsClient');
      const mockClient = vi.mocked(ElevenLabsClient);
      
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockClient.prototype.textToSpeech = vi.fn()
        .mockRejectedValueOnce(new ELError('Server error', 503))
        .mockResolvedValue(mockAudioBuffer);

      const result = await aiService.generateAudio('Hello there!');
      
      expect(result).toBe(mockAudioBuffer);
      expect(mockClient.prototype.textToSpeech).toHaveBeenCalledTimes(2);
    });

    it('should handle ElevenLabs authentication errors', async () => {
      const { ElevenLabsClient } = await import('../services/ElevenLabsClient');
      const mockClient = vi.mocked(ElevenLabsClient);
      
      mockClient.prototype.textToSpeech = vi.fn()
        .mockRejectedValue(new ELError('Unauthorized', 401));

      await expect(
        aiService.generateAudio('Hello there!')
      ).rejects.toThrow('Failed to generate audio');

      // Should show voice authentication error notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Voice Authentication Error',
          message: expect.stringContaining('Invalid Eleven Labs API key')
        })
      );
    });
  });

  describe('Offline Handling', () => {
    beforeEach(() => {
      // Mock offline state
      Object.defineProperty(window.navigator, 'onLine', {
        value: false,
        writable: true
      });
    });

    it('should queue text requests when offline', async () => {
      // Create new service to pick up offline state
      const offlineService = new AIService();
      
      await expect(
        offlineService.generateTextResponse('Hello', mockPersonality)
      ).rejects.toThrow('Currently offline');

      const queueStatus = offlineService.getOfflineQueueStatus();
      expect(queueStatus.count).toBe(1);
      expect(queueStatus.items[0].type).toBe('text');
      expect(queueStatus.items[0].data.userMessage).toBe('Hello');
    });

    it('should queue audio requests when offline', async () => {
      // Create new service to pick up offline state
      const offlineService = new AIService();
      
      await expect(
        offlineService.generateAudio('Hello there!')
      ).rejects.toThrow('Currently offline');

      const queueStatus = offlineService.getOfflineQueueStatus();
      expect(queueStatus.count).toBe(1);
      expect(queueStatus.items[0].type).toBe('audio');
      expect(queueStatus.items[0].data.text).toBe('Hello there!');
    });

    it('should show offline notification when queuing', async () => {
      // Create new service to pick up offline state
      const offlineService = new AIService();
      
      try {
        await offlineService.generateTextResponse('Hello', mockPersonality);
      } catch (error) {
        // Expected to throw
      }

      // Should show queued message notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'info',
          title: 'Message Queued',
          message: expect.stringContaining('queued and will be sent when connection is restored')
        })
      );
    });
  });

  describe('Health Check Integration', () => {
    it('should perform comprehensive health check', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const { ElevenLabsClient } = await import('../services/ElevenLabsClient');
      
      const mockHFClient = vi.mocked(HuggingFaceClient);
      const mockELClient = vi.mocked(ElevenLabsClient);
      
      mockHFClient.prototype.generateText = vi.fn().mockResolvedValue('test');
      mockELClient.prototype.textToSpeech = vi.fn().mockResolvedValue(new ArrayBuffer(1024));

      const health = await aiService.healthCheck();
      
      expect(health.online).toBe(true);
      expect(health.huggingFace).toBe(true);
      expect(health.elevenLabs).toBe(true);
      expect(health.overall).toBe(true);
    });

    it('should handle health check failures', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const { ElevenLabsClient } = await import('../services/ElevenLabsClient');
      
      const mockHFClient = vi.mocked(HuggingFaceClient);
      const mockELClient = vi.mocked(ElevenLabsClient);
      
      mockHFClient.prototype.generateText = vi.fn()
        .mockRejectedValue(new HFError('Service unavailable', 503));
      mockELClient.prototype.textToSpeech = vi.fn()
        .mockRejectedValue(new ELError('Service unavailable', 503));

      const health = await aiService.healthCheck();
      
      expect(health.huggingFace).toBe(false);
      expect(health.elevenLabs).toBe(false);
      expect(health.overall).toBe(false);
      
      // Should show error notifications for failed health checks
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Service Unavailable'
        })
      );
    });

    it('should return offline status when offline', async () => {
      // Mock offline state
      Object.defineProperty(window.navigator, 'onLine', {
        value: false,
        writable: true
      });
      
      // Create new service to pick up offline state
      const offlineService = new AIService();
      
      const health = await offlineService.healthCheck();
      
      expect(health.online).toBe(false);
      expect(health.huggingFace).toBe(false);
      expect(health.elevenLabs).toBe(false);
      expect(health.overall).toBe(false);
    });
  });

  describe('Error Recovery', () => {
    it('should recover from temporary HuggingFace failures', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const mockClient = vi.mocked(HuggingFaceClient);
      
      // First call fails, second succeeds
      mockClient.prototype.generatePersonalityResponse = vi.fn()
        .mockRejectedValueOnce(new HFError('Temporary failure', 503))
        .mockResolvedValue('Recovery successful!');

      const result = await aiService.generateTextResponse('Hello', mockPersonality);
      
      expect(result).toBe('Recovery successful!');
      expect(mockClient.prototype.generatePersonalityResponse).toHaveBeenCalledTimes(2);
    });

    it('should continue with text-only when TTS fails', async () => {
      const { HuggingFaceClient } = await import('../services/HuggingFaceClient');
      const { ElevenLabsClient } = await import('../services/ElevenLabsClient');
      
      const mockHFClient = vi.mocked(HuggingFaceClient);
      const mockELClient = vi.mocked(ElevenLabsClient);
      
      mockHFClient.prototype.generatePersonalityResponse = vi.fn()
        .mockResolvedValue('Text response works!');
      
      mockELClient.prototype.textToSpeech = vi.fn()
        .mockRejectedValue(new ELError('TTS service down', 503));

      const response = await aiService.generateResponse('Hello', mockPersonality);
      
      expect(response.text).toBe('Text response works!');
      expect(response.audio).toBeUndefined();
      
      // Should show graceful degradation notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          message: expect.stringContaining('Voice synthesis temporarily unavailable')
        })
      );
    });
  });

  describe('Queue Management', () => {
    it('should clear offline queue', () => {
      // Mock offline state
      Object.defineProperty(window.navigator, 'onLine', {
        value: false,
        writable: true
      });
      
      const offlineService = new AIService();
      const errorHandler = offlineService.getErrorHandler();
      
      errorHandler.addToOfflineQueue('text', { message: 'test' });
      expect(offlineService.getOfflineQueueStatus().count).toBe(1);
      
      offlineService.clearOfflineQueue();
      expect(offlineService.getOfflineQueueStatus().count).toBe(0);
    });

    it('should provide queue status', () => {
      // Mock offline state
      Object.defineProperty(window.navigator, 'onLine', {
        value: false,
        writable: true
      });
      
      const offlineService = new AIService();
      const errorHandler = offlineService.getErrorHandler();
      
      errorHandler.addToOfflineQueue('text', { message: 'test1' });
      errorHandler.addToOfflineQueue('audio', { text: 'test2' });
      
      const status = offlineService.getOfflineQueueStatus();
      expect(status.count).toBe(2);
      expect(status.items).toHaveLength(2);
      expect(status.items[0].type).toBe('text');
      expect(status.items[1].type).toBe('audio');
    });
  });
});