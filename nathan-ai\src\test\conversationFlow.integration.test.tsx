import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import App from '../App';
import { AIService } from '../services/AIService';
import type { PersonalityConfig } from '../types/personality';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  }
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock Web Speech API
const mockSpeechRecognition = {
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: 'en-US',
  onstart: null,
  onend: null,
  onresult: null,
  onerror: null
};

Object.defineProperty(window, 'SpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

// Mock Audio API
const mockAudio = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 10,
  paused: true,
  ended: false,
  volume: 1,
  src: ''
};

Object.defineProperty(window, 'Audio', {
  value: vi.fn(() => mockAudio),
  writable: true
});

// Mock URL methods
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-audio-url');
global.URL.revokeObjectURL = vi.fn();

describe('Complete Conversation Flow Integration', () => {
  let mockAIService: any;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    vi.clearAllMocks();
    user = userEvent.setup();
    
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
      healthCheck: vi.fn().mockResolvedValue({
        online: true,
        huggingFace: true,
        elevenLabs: true,
        overall: true
      })
    };
    
    vi.mocked(AIService).mockImplementation(() => mockAIService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Text Input Mode Conversation Flow', () => {
    it('should handle complete text conversation with personality integration', async () => {
      const mockPersonality: PersonalityConfig = {
        name: 'Nathan',
        pronouns: 'he/him',
        personality: {
          tone: 'friendly',
          role: 'companion',
          hobbies: ['coding'],
          style: {
            speech: 'casual',
            humor: 'light',
            depth: 'thoughtful'
          },
          boundaries: {
            avoid: [],
            safe_topics: []
          },
          dynamic_traits: {
            adaptive_empathy: true,
            mirroring_style: true,
            emotionally_available: true
          }
        },
        conversation_tips: {
          starter_prompts: []
        },
        version: '1.0.0'
      };

      mockAIService.generateResponse.mockResolvedValueOnce({
        text: 'Hello! Nice to meet you!',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      // Wait for app to initialize
      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Find text input and send button
      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Type a message
      await user.type(textInput, 'Hello Nathan!');
      expect(textInput).toHaveValue('Hello Nathan!');

      // Send the message
      await user.click(sendButton);

      // Wait for response
      await waitFor(() => {
        expect(screen.getByText('Hello Nathan!')).toBeInTheDocument();
        expect(screen.getByText('Hello! Nice to meet you!')).toBeInTheDocument();
      });

      // Verify AI service was called with personality
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Hello Nathan!',
        expect.objectContaining({
          name: 'Nathan',
          personality: expect.objectContaining({
            tone: 'friendly',
            role: 'companion'
          })
        })
      );

      // Input should be cleared after sending
      expect(textInput).toHaveValue('');
    });

    it('should handle Enter key submission', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Response to Enter key',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');

      await user.type(textInput, 'Message sent with Enter');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByText('Message sent with Enter')).toBeInTheDocument();
        expect(screen.getByText('Response to Enter key')).toBeInTheDocument();
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Message sent with Enter',
        expect.any(Object)
      );
    });

    it('should prevent sending empty messages', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const sendButton = screen.getByTestId('send-button');

      // Try to send empty message
      await user.click(sendButton);

      // Should not call AI service
      expect(mockAIService.generateResponse).not.toHaveBeenCalled();

      // Should show validation feedback
      expect(screen.getByText(/message cannot be empty/i)).toBeInTheDocument();
    });
  });

  describe('Voice Input Mode Conversation Flow', () => {
    it('should handle voice input conversation', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'I heard you loud and clear!',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Verify voice input is active
      expect(screen.getByTestId('voice-input')).toBeInTheDocument();

      // Start listening
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      expect(mockSpeechRecognition.start).toHaveBeenCalled();

      // Simulate speech recognition result
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello via voice' },
            isFinal: true
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Simulate recognition end
      act(() => {
        mockSpeechRecognition.onend?.();
      });

      await waitFor(() => {
        expect(screen.getByText('Hello via voice')).toBeInTheDocument();
        expect(screen.getByText('I heard you loud and clear!')).toBeInTheDocument();
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Hello via voice',
        expect.any(Object)
      );
    });

    it('should handle voice recognition errors', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Start listening
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Simulate recognition error
      act(() => {
        const mockError = { error: 'not-allowed' };
        mockSpeechRecognition.onerror?.(mockError);
      });

      await waitFor(() => {
        expect(screen.getByText(/microphone access denied/i)).toBeInTheDocument();
      });

      // Should automatically switch to text mode
      expect(screen.getByTestId('text-input')).toBeInTheDocument();
    });

    it('should handle interim speech results', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Simulate interim results
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello this is' },
            isFinal: false
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Should show interim transcript
      expect(screen.getByText('Hello this is')).toBeInTheDocument();
      expect(screen.getByTestId('interim-transcript')).toBeInTheDocument();

      // Final result
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello this is a complete message' },
            isFinal: true
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Should not call AI service until final result
      expect(mockAIService.generateResponse).not.toHaveBeenCalled();
    });
  });

  describe('Audio Output Integration', () => {
    it('should play audio responses automatically', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Audio response',
        emotion: 'happy',
        audio: mockAudioBuffer
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Audio response')).toBeInTheDocument();
      });

      // Should create audio URL and start playback
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(expect.any(Blob));
      expect(mockAudio.play).toHaveBeenCalled();
    });

    it('should interrupt audio when new message is sent', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'First response',
          emotion: 'neutral',
          audio: mockAudioBuffer
        })
        .mockResolvedValueOnce({
          text: 'Second response',
          emotion: 'neutral',
          audio: mockAudioBuffer
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Send first message
      await user.type(textInput, 'First message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('First response')).toBeInTheDocument();
      });

      // Send second message while first audio might be playing
      await user.type(textInput, 'Second message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Second response')).toBeInTheDocument();
      });

      // Should have paused previous audio
      expect(mockAudio.pause).toHaveBeenCalled();
    });

    it('should handle audio playback errors', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Response with audio error',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      // Mock audio play to reject
      mockAudio.play.mockRejectedValue(new Error('Audio playback failed'));

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Response with audio error')).toBeInTheDocument();
      });

      // Should show error notification but continue with text
      await waitFor(() => {
        expect(screen.getByText(/audio playback failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Mode Switching Integration', () => {
    it('should switch between text and voice modes seamlessly', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Start in text mode
      expect(screen.getByTestId('text-input')).toBeInTheDocument();

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      expect(screen.queryByTestId('text-input')).not.toBeInTheDocument();

      // Switch back to text mode
      const textToggle = screen.getByTestId('text-toggle');
      await user.click(textToggle);

      expect(screen.getByTestId('text-input')).toBeInTheDocument();
      expect(screen.queryByTestId('voice-input')).not.toBeInTheDocument();
    });

    it('should switch between visual and minimal modes', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Start in visual mode
      expect(screen.getByTestId('avatar-container')).toBeInTheDocument();

      // Switch to minimal mode
      const minimalToggle = screen.getByTestId('minimal-toggle');
      await user.click(minimalToggle);

      expect(screen.queryByTestId('avatar-container')).not.toBeInTheDocument();
      expect(screen.getByTestId('minimal-interface')).toBeInTheDocument();

      // Switch back to visual mode
      const visualToggle = screen.getByTestId('visual-toggle');
      await user.click(visualToggle);

      expect(screen.getByTestId('avatar-container')).toBeInTheDocument();
      expect(screen.queryByTestId('minimal-interface')).not.toBeInTheDocument();
    });

    it('should preserve conversation when switching modes', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Mode switch response',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Send message in text mode
      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Message before switch');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Message before switch')).toBeInTheDocument();
        expect(screen.getByText('Mode switch response')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Messages should still be visible
      expect(screen.getByText('Message before switch')).toBeInTheDocument();
      expect(screen.getByText('Mode switch response')).toBeInTheDocument();

      // Switch to minimal mode
      const minimalToggle = screen.getByTestId('minimal-toggle');
      await user.click(minimalToggle);

      // Messages should still be visible
      expect(screen.getByText('Message before switch')).toBeInTheDocument();
      expect(screen.getByText('Mode switch response')).toBeInTheDocument();
    });
  });

  describe('Error Recovery Integration', () => {
    it('should recover from AI service failures', async () => {
      mockAIService.generateResponse
        .mockRejectedValueOnce(new Error('Service temporarily unavailable'))
        .mockResolvedValueOnce({
          text: 'Recovery successful',
          emotion: 'happy',
          audio: new ArrayBuffer(512)
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // First attempt fails
      await user.type(textInput, 'First attempt');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/service temporarily unavailable/i)).toBeInTheDocument();
      });

      // Retry button should appear
      const retryButton = screen.getByTestId('retry-button');
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('Recovery successful')).toBeInTheDocument();
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
    });

    it('should handle network connectivity issues', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should show offline indicator
      expect(screen.getByTestId('offline-indicator')).toBeInTheDocument();

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Offline message');
      await user.click(sendButton);

      // Should show queued message notification
      await waitFor(() => {
        expect(screen.getByText(/message queued/i)).toBeInTheDocument();
      });

      // Simulate coming back online
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });

      // Trigger online event
      act(() => {
        window.dispatchEvent(new Event('online'));
      });

      await waitFor(() => {
        expect(screen.queryByTestId('offline-indicator')).not.toBeInTheDocument();
      });
    });

    it('should gracefully degrade when TTS fails', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Text only response',
        emotion: 'neutral',
        audio: null // TTS failed
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Text only response')).toBeInTheDocument();
      });

      // Should show TTS unavailable notification
      expect(screen.getByText(/voice synthesis temporarily unavailable/i)).toBeInTheDocument();

      // Should not attempt audio playback
      expect(mockAudio.play).not.toHaveBeenCalled();
    });
  });

  describe('Personality System Integration', () => {
    it('should load and apply personality configuration', async () => {
      const customPersonality: PersonalityConfig = {
        name: 'CustomNathan',
        pronouns: 'they/them',
        personality: {
          tone: 'formal',
          role: 'assistant',
          hobbies: ['research'],
          style: {
            speech: 'professional',
            humor: 'subtle',
            depth: 'analytical'
          },
          boundaries: {
            avoid: ['personal topics'],
            safe_topics: ['work', 'technology']
          },
          dynamic_traits: {
            adaptive_empathy: false,
            mirroring_style: false,
            emotionally_available: true
          }
        },
        conversation_tips: {
          starter_prompts: ['How may I assist you?']
        },
        version: '2.0.0'
      };

      // Mock personality loading
      vi.doMock('../utils/personality', () => ({
        loadPersonality: vi.fn().mockResolvedValue(customPersonality),
        savePersonality: vi.fn().mockResolvedValue(undefined)
      }));

      mockAIService.generateResponse.mockResolvedValue({
        text: 'Formal response from CustomNathan',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should display custom personality name
      expect(screen.getByText('CustomNathan')).toBeInTheDocument();

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Hello');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Formal response from CustomNathan')).toBeInTheDocument();
      });

      // Verify custom personality was passed to AI service
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Hello',
        expect.objectContaining({
          name: 'CustomNathan',
          pronouns: 'they/them',
          personality: expect.objectContaining({
            tone: 'formal',
            role: 'assistant'
          })
        })
      );
    });

    it('should handle personality loading errors', async () => {
      // Mock personality loading failure
      vi.doMock('../utils/personality', () => ({
        loadPersonality: vi.fn().mockRejectedValue(new Error('Failed to load personality')),
        savePersonality: vi.fn().mockResolvedValue(undefined)
      }));

      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/failed to load personality/i)).toBeInTheDocument();
      });

      // Should show retry option
      const retryButton = screen.getByTestId('retry-personality-button');
      expect(retryButton).toBeInTheDocument();
    });

    it('should update personality dynamically', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Response with updated personality',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Update personality tone
      const toneSelect = screen.getByTestId('personality-tone-select');
      await user.selectOptions(toneSelect, 'enthusiastic');

      // Save changes
      const saveButton = screen.getByTestId('save-personality-button');
      await user.click(saveButton);

      // Send a message to test updated personality
      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test updated personality');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Response with updated personality')).toBeInTheDocument();
      });

      // Verify updated personality was used
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Test updated personality',
        expect.objectContaining({
          personality: expect.objectContaining({
            tone: 'enthusiastic'
          })
        })
      );
    });
  });

  describe('State Persistence Integration', () => {
    it('should persist conversation state across page reloads', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Persistent response',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      const { unmount } = render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Message to persist');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Message to persist')).toBeInTheDocument();
        expect(screen.getByText('Persistent response')).toBeInTheDocument();
      });

      // Unmount and remount to simulate page reload
      unmount();
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Messages should be restored
      expect(screen.getByText('Message to persist')).toBeInTheDocument();
      expect(screen.getByText('Persistent response')).toBeInTheDocument();
    });

    it('should persist mode preferences', async () => {
      const { unmount } = render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Switch to minimal mode
      const minimalToggle = screen.getByTestId('minimal-toggle');
      await user.click(minimalToggle);

      // Unmount and remount
      unmount();
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should restore previous modes
      expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      expect(screen.getByTestId('minimal-interface')).toBeInTheDocument();
    });
  });
});