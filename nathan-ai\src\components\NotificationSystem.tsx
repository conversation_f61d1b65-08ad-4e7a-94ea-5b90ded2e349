import React, { useState, useEffect, useCallback } from 'react';
import { ApiErrorNotification } from '../services/ApiErrorHandler';
import styles from './NotificationSystem.module.css';

interface NotificationWithId extends ApiErrorNotification {
  id: string;
  timestamp: number;
}

interface NotificationSystemProps {
  onNotificationReceived?: (notification: ApiErrorNotification) => void;
}

export const NotificationSystem: React.FC<NotificationSystemProps> = ({
  onNotificationReceived
}) => {
  const [notifications, setNotifications] = useState<NotificationWithId[]>([]);

  const addNotification = useCallback((notification: ApiErrorNotification) => {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const notificationWithId: NotificationWithId = {
      ...notification,
      id,
      timestamp: Date.now()
    };

    setNotifications(prev => [...prev, notificationWithId]);

    // Auto-dismiss if duration is set
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration);
    }

    // Call external handler if provided
    onNotificationReceived?.(notification);
  }, [onNotificationReceived]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const handleAction = useCallback((notification: NotificationWithId) => {
    if (notification.action) {
      notification.action.handler();
      removeNotification(notification.id);
    }
  }, [removeNotification]);

  // Expose addNotification method globally for the error handler
  useEffect(() => {
    (window as any).__addNotification = addNotification;
    return () => {
      delete (window as any).__addNotification;
    };
  }, [addNotification]);

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={styles.notificationContainer}>
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={`${styles.notification} ${styles[notification.type]}`}
          role="alert"
          aria-live="polite"
        >
          <div className={styles.content}>
            <div className={styles.header}>
              <h4 className={styles.title}>{notification.title}</h4>
              <button
                className={styles.closeButton}
                onClick={() => removeNotification(notification.id)}
                aria-label="Close notification"
              >
                ×
              </button>
            </div>
            <p className={styles.message}>{notification.message}</p>
            {notification.action && (
              <button
                className={styles.actionButton}
                onClick={() => handleAction(notification)}
              >
                {notification.action.label}
              </button>
            )}
          </div>
          <div className={styles.progressBar}>
            {notification.duration && notification.duration > 0 && (
              <div
                className={styles.progress}
                style={{
                  animationDuration: `${notification.duration}ms`
                }}
              />
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationSystem;