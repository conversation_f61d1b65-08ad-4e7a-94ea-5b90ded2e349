import React from 'react';
import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { VoiceInput } from '../components/VoiceInput';

// Mock SpeechRecognition
class MockSpeechRecognition {
  continuous = false;
  interimResults = false;
  lang = '';
  maxAlternatives = 1;
  
  private eventListeners: { [key: string]: Function[] } = {};
  
  addEventListener(event: string, callback: Function) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(callback);
  }
  
  removeEventListener(event: string, callback: Function) {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }
  }
  
  start() {
    // Immediately dispatch start event
    this.dispatchEvent('start');
  }
  
  stop() {
    // Immediately dispatch end event
    this.dispatchEvent('end');
  }
  
  abort() {
    this.dispatchEvent('end');
  }
  
  dispatchEvent(eventType: string, data?: any) {
    if (this.eventListeners[eventType]) {
      this.eventListeners[eventType].forEach(callback => {
        callback(data || { type: eventType });
      });
    }
  }
  
  // Helper methods for testing
  simulateResult(transcript: string, isFinal: boolean = false) {
    const event = {
      resultIndex: 0,
      results: [{
        0: { transcript },
        isFinal,
        length: 1
      }],
      type: 'result'
    };
    this.dispatchEvent('result', event);
  }
  
  simulateError(error: string) {
    const event = {
      error,
      type: 'error'
    };
    this.dispatchEvent('error', event);
  }
}

// Global mocks
const mockRecognition = new MockSpeechRecognition();

Object.defineProperty(window, 'SpeechRecognition', {
  writable: true,
  value: vi.fn(() => mockRecognition)
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  writable: true,
  value: vi.fn(() => mockRecognition)
});

describe('VoiceInput', () => {
  const mockOnTranscript = vi.fn();
  const mockOnListeningChange = vi.fn();
  const mockOnError = vi.fn();
  
  const defaultProps = {
    onTranscript: mockOnTranscript,
    onListeningChange: mockOnListeningChange,
    onError: mockOnError,
    isActive: true
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Browser Support Detection', () => {
    it('renders microphone button when speech recognition is supported', () => {
      render(<VoiceInput {...defaultProps} />);
      expect(screen.getByRole('button', { name: 'Start listening' })).toBeInTheDocument();
    });

    it('shows unsupported message when speech recognition is not available', () => {
      // Temporarily remove SpeechRecognition support
      const originalSpeechRecognition = window.SpeechRecognition;
      const originalWebkitSpeechRecognition = window.webkitSpeechRecognition;
      
      Object.defineProperty(window, 'SpeechRecognition', {
        writable: true,
        value: undefined
      });
      Object.defineProperty(window, 'webkitSpeechRecognition', {
        writable: true,
        value: undefined
      });
      
      render(<VoiceInput {...defaultProps} />);
      expect(screen.getByText('Voice input is not supported in this browser')).toBeInTheDocument();
      
      // Restore support
      window.SpeechRecognition = originalSpeechRecognition;
      window.webkitSpeechRecognition = originalWebkitSpeechRecognition;
    });
  });

  describe('Microphone Button Behavior', () => {
    it('starts listening when microphone button is clicked', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      expect(mockOnListeningChange).toHaveBeenCalledWith(true);
    });

    it('is disabled when isActive is false', () => {
      render(<VoiceInput {...defaultProps} isActive={false} />);
      const micButton = screen.getByRole('button');
      expect(micButton).toBeDisabled();
    });

    it('shows listening state visually when active', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      expect(micButton.className).toContain('listening');
      expect(screen.getByText('Listening...')).toBeInTheDocument();
    });
  });

  describe('Speech Recognition Events', () => {
    it('calls onTranscript with final result', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      // Simulate final result
      act(() => {
        mockRecognition.simulateResult('Hello world', true);
      });
      
      expect(mockOnTranscript).toHaveBeenCalledWith('Hello world');
    });
  });

  describe('Error Handling', () => {
    it('calls onError with correct message for no-speech error', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      act(() => {
        mockRecognition.simulateError('no-speech');
      });
      
      expect(mockOnError).toHaveBeenCalledWith('No speech detected. Please try again.');
    });

    it('calls onError with correct message for audio-capture error', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      act(() => {
        mockRecognition.simulateError('audio-capture');
      });
      
      expect(mockOnError).toHaveBeenCalledWith('Microphone not accessible. Please check permissions.');
    });

    it('stops listening when error occurs', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      act(() => {
        mockRecognition.simulateError('no-speech');
      });
      
      expect(mockOnListeningChange).toHaveBeenCalledWith(false);
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for different states', () => {
      render(<VoiceInput {...defaultProps} />);
      
      let micButton = screen.getByRole('button', { name: 'Start listening' });
      expect(micButton).toHaveAttribute('aria-label', 'Start listening');
    });

    it('is keyboard accessible', () => {
      render(<VoiceInput {...defaultProps} />);
      const micButton = screen.getByRole('button');
      
      micButton.focus();
      expect(micButton).toHaveFocus();
    });
  });

  describe('Visual Feedback', () => {
    it('shows listening status with waveform animation', async () => {
      const user = userEvent.setup();
      render(<VoiceInput {...defaultProps} />);
      
      const micButton = screen.getByRole('button', { name: 'Start listening' });
      await user.click(micButton);
      
      expect(screen.getByText('Listening...')).toBeInTheDocument();
      
      // Check for waveform elements
      const waveform = document.querySelector('[class*="waveform"]');
      expect(waveform).toBeInTheDocument();
    });
  });
});

