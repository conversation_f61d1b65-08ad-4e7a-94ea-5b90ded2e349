import React, { ReactNode } from 'react';
import { ErrorBoundary } from './ErrorBoundary';

interface ApiErrorBoundaryProps {
  children: ReactNode;
  onRetry?: () => void;
  serviceName?: string;
}

export const ApiErrorBoundary: React.FC<ApiErrorBoundaryProps> = ({
  children,
  onRetry,
  serviceName = 'API service',
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log API-specific error details
    console.error(`API Error in ${serviceName}:`, {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      service: serviceName,
      timestamp: new Date().toISOString(),
    });

    // Could send to error tracking service here
    // trackError('api_error', { service: serviceName, error: error.message });
  };

  const fallbackUI = (
    <div style={{
      padding: '2rem',
      textAlign: 'center',
      backgroundColor: '#fff3cd',
      border: '1px solid #ffeaa7',
      borderRadius: '8px',
      margin: '1rem 0',
    }}>
      <h3 style={{ color: '#856404', marginBottom: '1rem' }}>
        {serviceName} Unavailable
      </h3>
      <p style={{ color: '#856404', marginBottom: '1.5rem' }}>
        We're having trouble connecting to {serviceName}. This might be a temporary issue.
      </p>
      <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
        {onRetry && (
          <button
            onClick={onRetry}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            Retry Connection
          </button>
        )}
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Refresh Page
        </button>
      </div>
    </div>
  );

  return (
    <ErrorBoundary
      level="component"
      onError={handleError}
      fallback={fallbackUI}
      resetOnPropsChange={true}
    >
      {children}
    </ErrorBoundary>
  );
};