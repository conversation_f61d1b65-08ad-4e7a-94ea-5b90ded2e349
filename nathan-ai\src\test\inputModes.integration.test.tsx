import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { InputController } from '../components/InputController';
import { ConversationProvider, PersonalityProvider } from '../context';
import { AIService } from '../services/AIService';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  },
  getEnvVar: vi.fn().mockImplementation((key: string, defaultValue?: string) => {
    const mockEnv: Record<string, string> = {
      'VITE_HUGGING_FACE_API_KEY': 'test-hf-key',
      'VITE_ELEVEN_LABS_API_KEY': 'test-el-key',
      'VITE_ELEVEN_LABS_VOICE_ID': 'test-voice-id'
    };
    return mockEnv[key] || defaultValue || '';
  })
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock Web Speech API
const mockSpeechRecognition = {
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: 'en-US',
  onstart: null,
  onend: null,
  onresult: null,
  onerror: null
};

Object.defineProperty(window, 'SpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

// Mock personality utilities
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn().mockResolvedValue({
    name: 'Nathan',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['technology'],
      style: {
        speech: 'casual',
        humor: 'light',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: [],
        safe_topics: []
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: []
    },
    version: '1.0.0'
  }),
  savePersonality: vi.fn().mockResolvedValue(undefined)
}));

function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <PersonalityProvider>
      <ConversationProvider>
        {children}
      </ConversationProvider>
    </PersonalityProvider>
  );
}

describe('Input Modes Integration Tests', () => {
  let mockAIService: any;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    vi.clearAllMocks();
    user = userEvent.setup();
    
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn()
    };
    
    vi.mocked(AIService).mockImplementation(() => mockAIService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Text Input Mode', () => {
    it('should handle text input and message sending', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Hello! How can I help you?',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      // Wait for component to initialize
      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Should start in text mode
      expect(screen.getByTestId('text-input')).toBeInTheDocument();

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Type a message
      await user.type(textInput, 'Hello Nathan!');
      expect(textInput).toHaveValue('Hello Nathan!');

      // Send the message
      await user.click(sendButton);

      // Input should be cleared after sending
      await waitFor(() => {
        expect(textInput).toHaveValue('');
      });
    });

    it('should handle Enter key submission', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Response to Enter key',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');

      await user.type(textInput, 'Message sent with Enter');
      await user.keyboard('{Enter}');

      // Input should be cleared
      await waitFor(() => {
        expect(textInput).toHaveValue('');
      });
    });

    it('should prevent sending empty messages', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      const sendButton = screen.getByTestId('send-button');

      // Try to send empty message
      await user.click(sendButton);

      // Should not call AI service
      expect(mockAIService.generateResponse).not.toHaveBeenCalled();

      // Should show validation feedback
      await waitFor(() => {
        expect(screen.getByText(/message cannot be empty/i)).toBeInTheDocument();
      });
    });
  });

  describe('Voice Input Mode', () => {
    it('should switch to voice mode and handle voice input', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'I heard you loud and clear!',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Verify voice input is active
      await waitFor(() => {
        expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      });

      // Start listening
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      expect(mockSpeechRecognition.start).toHaveBeenCalled();

      // Simulate speech recognition result
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello via voice' },
            isFinal: true
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Simulate recognition end
      act(() => {
        mockSpeechRecognition.onend?.();
      });

      // Should process the voice input
      await waitFor(() => {
        expect(screen.getByText('Hello via voice')).toBeInTheDocument();
      });
    });

    it('should handle voice recognition errors gracefully', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Start listening
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Simulate recognition error
      act(() => {
        const mockError = { error: 'not-allowed' };
        mockSpeechRecognition.onerror?.(mockError);
      });

      await waitFor(() => {
        expect(screen.getByText(/microphone access denied/i)).toBeInTheDocument();
      });

      // Should automatically switch to text mode
      await waitFor(() => {
        expect(screen.getByTestId('text-input')).toBeInTheDocument();
      });
    });

    it('should handle interim speech results', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Simulate interim results
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello this is' },
            isFinal: false
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Should show interim transcript
      await waitFor(() => {
        expect(screen.getByText('Hello this is')).toBeInTheDocument();
      });

      expect(screen.getByTestId('interim-transcript')).toBeInTheDocument();

      // Final result
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello this is a complete message' },
            isFinal: true
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Should show final transcript
      await waitFor(() => {
        expect(screen.getByText('Hello this is a complete message')).toBeInTheDocument();
      });
    });
  });

  describe('Mode Switching', () => {
    it('should switch between text and voice modes seamlessly', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Start in text mode
      expect(screen.getByTestId('text-input')).toBeInTheDocument();

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      await waitFor(() => {
        expect(screen.getByTestId('voice-input')).toBeInTheDocument();
        expect(screen.queryByTestId('text-input')).not.toBeInTheDocument();
      });

      // Switch back to text mode
      const textToggle = screen.getByTestId('text-toggle');
      await user.click(textToggle);

      await waitFor(() => {
        expect(screen.getByTestId('text-input')).toBeInTheDocument();
        expect(screen.queryByTestId('voice-input')).not.toBeInTheDocument();
      });
    });

    it('should maintain input state when switching modes', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Type in text mode
      const textInput = screen.getByTestId('text-input');
      await user.type(textInput, 'Partial message');

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Switch back to text mode
      const textToggle = screen.getByTestId('text-toggle');
      await user.click(textToggle);

      // Text should be preserved
      await waitFor(() => {
        expect(screen.getByTestId('text-input')).toHaveValue('Partial message');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle AI service failures gracefully', async () => {
      mockAIService.generateResponse.mockRejectedValue(new Error('Service unavailable'));

      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test message');
      await user.click(sendButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/service unavailable/i)).toBeInTheDocument();
      });

      // Should show retry option
      expect(screen.getByTestId('retry-button')).toBeInTheDocument();
    });

    it('should handle network connectivity issues', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Should show offline indicator
      expect(screen.getByTestId('offline-indicator')).toBeInTheDocument();

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Offline message');
      await user.click(sendButton);

      // Should show queued message notification
      await waitFor(() => {
        expect(screen.getByText(/message queued/i)).toBeInTheDocument();
      });
    });

    it('should recover when coming back online', async () => {
      // Start offline
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Should show offline indicator
      expect(screen.getByTestId('offline-indicator')).toBeInTheDocument();

      // Come back online
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });

      mockAIService.generateResponse.mockResolvedValue({
        text: 'Back online response',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      // Trigger online event
      act(() => {
        window.dispatchEvent(new Event('online'));
      });

      // Offline indicator should disappear
      await waitFor(() => {
        expect(screen.queryByTestId('offline-indicator')).not.toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should support keyboard navigation', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');
      const voiceToggle = screen.getByTestId('voice-toggle');

      // Tab navigation should work
      textInput.focus();
      expect(document.activeElement).toBe(textInput);

      await user.keyboard('{Tab}');
      expect(document.activeElement).toBe(sendButton);

      await user.keyboard('{Tab}');
      expect(document.activeElement).toBe(voiceToggle);
    });

    it('should provide proper ARIA labels', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');
      const voiceToggle = screen.getByTestId('voice-toggle');

      expect(textInput).toHaveAttribute('aria-label');
      expect(sendButton).toHaveAttribute('aria-label');
      expect(voiceToggle).toHaveAttribute('aria-label');
    });

    it('should announce state changes to screen readers', async () => {
      render(
        <TestWrapper>
          <InputController />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Should have live region announcement
      await waitFor(() => {
        expect(screen.getByRole('status')).toHaveTextContent(/switched to voice input/i);
      });
    });
  });
});