import React from 'react';
import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { InputController } from '../components/InputController';

// Mock the child components
vi.mock('../components/TextInput', () => ({
  TextInput: ({ onMessageSend, isLoading, placeholder }: any) => (
    <div data-testid="text-input">
      <input
        placeholder={placeholder}
        disabled={isLoading}
        onChange={(e) => {
          if (e.target.value === 'test message') {
            onMessageSend('test message');
          }
        }}
      />
      {isLoading && <div data-testid="text-loading">Loading...</div>}
    </div>
  )
}));

vi.mock('../components/VoiceInput', () => ({
  VoiceInput: ({ onTranscript, onListeningChange, onError, isActive }: any) => (
    <div data-testid="voice-input">
      <button
        disabled={!isActive}
        onClick={() => {
          onTranscript('voice message');
        }}
      >
        Voice Input
      </button>
      <button
        onClick={() => onError('Voice error')}
        data-testid="voice-error-trigger"
      >
        Trigger Error
      </button>
      {!isActive && <div data-testid="voice-disabled">Voice Disabled</div>}
    </div>
  )
}));

describe('InputController', () => {
  const mockOnModeChange = vi.fn();
  const mockOnMessageSend = vi.fn();
  
  const defaultProps = {
    inputMode: 'text' as const,
    onModeChange: mockOnModeChange,
    onMessageSend: mockOnMessageSend,
    isListening: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Mode Toggle', () => {
    it('renders with text mode by default', () => {
      render(<InputController {...defaultProps} />);
      
      expect(screen.getByTestId('text-input')).toBeInTheDocument();
      expect(screen.queryByTestId('voice-input')).not.toBeInTheDocument();
      expect(screen.getByText('Text')).toBeInTheDocument();
    });

    it('renders with voice mode when specified', () => {
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      expect(screen.queryByTestId('text-input')).not.toBeInTheDocument();
      expect(screen.getByText('Voice')).toBeInTheDocument();
    });

    it('calls onModeChange when toggle button is clicked', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} />);
      
      const toggleButton = screen.getByRole('button', { name: /switch to voice input/i });
      await user.click(toggleButton);
      
      expect(mockOnModeChange).toHaveBeenCalledWith('voice');
    });

    it('switches from voice to text mode', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const toggleButton = screen.getByRole('button', { name: /switch to text input/i });
      await user.click(toggleButton);
      
      expect(mockOnModeChange).toHaveBeenCalledWith('text');
    });

    it('has proper accessibility labels', () => {
      render(<InputController {...defaultProps} />);
      
      const toggleButton = screen.getByRole('button', { name: /switch to voice input/i });
      expect(toggleButton).toHaveAttribute('aria-label', 'Switch to voice input');
    });
  });

  describe('Text Input Mode', () => {
    it('renders TextInput component with correct props', () => {
      render(<InputController {...defaultProps} />);
      
      const textInput = screen.getByTestId('text-input');
      expect(textInput).toBeInTheDocument();
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      expect(input).toBeInTheDocument();
    });

    it('handles message sending from TextInput', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      expect(mockOnMessageSend).toHaveBeenCalledWith('test message');
    });

    it('shows loading state during message sending', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      expect(screen.getByText('Processing...')).toBeInTheDocument();
      expect(screen.getByTestId('text-loading')).toBeInTheDocument();
    });
  });

  describe('Voice Input Mode', () => {
    it('renders VoiceInput component with correct props', () => {
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const voiceInput = screen.getByTestId('voice-input');
      expect(voiceInput).toBeInTheDocument();
      
      const voiceButton = screen.getByText('Voice Input');
      expect(voiceButton).toBeInTheDocument();
    });

    it('handles transcript from VoiceInput', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const voiceButton = screen.getByText('Voice Input');
      await user.click(voiceButton);
      
      expect(mockOnMessageSend).toHaveBeenCalledWith('voice message');
    });

    it('disables voice input during loading', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const voiceButton = screen.getByText('Voice Input');
      await user.click(voiceButton);
      
      expect(screen.getByTestId('voice-disabled')).toBeInTheDocument();
    });

    it('handles voice errors', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const errorTrigger = screen.getByTestId('voice-error-trigger');
      await user.click(errorTrigger);
      
      expect(screen.getByText('Voice error')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('displays error message when message sending fails', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockRejectedValue(new Error('Network error'));
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      // Wait for error to appear
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });

    it('displays generic error message for non-Error objects', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockRejectedValue('String error');
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      // Wait for error to appear
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      expect(screen.getByText('Failed to send message')).toBeInTheDocument();
    });

    it('allows dismissing error messages', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockRejectedValue(new Error('Network error'));
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      // Wait for error to appear
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      const dismissButton = screen.getByRole('button', { name: 'Dismiss error' });
      await user.click(dismissButton);
      
      expect(screen.queryByText('Network error')).not.toBeInTheDocument();
    });

    it('clears errors when switching modes', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      // Trigger voice error
      const errorTrigger = screen.getByTestId('voice-error-trigger');
      await user.click(errorTrigger);
      
      expect(screen.getByText('Voice error')).toBeInTheDocument();
      
      // Switch modes
      const toggleButton = screen.getByRole('button', { name: /switch to text input/i });
      await user.click(toggleButton);
      
      expect(screen.queryByText('Voice error')).not.toBeInTheDocument();
    });
  });

  describe('Status Indicators', () => {
    it('shows processing indicator during message sending', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      expect(screen.getByText('Processing...')).toBeInTheDocument();
    });

    it('shows listening indicator when in voice mode and listening', () => {
      render(<InputController {...defaultProps} inputMode="voice" isListening={true} />);
      
      expect(screen.getByText('Listening...')).toBeInTheDocument();
    });

    it('shows correct mode indicator for text mode', () => {
      render(<InputController {...defaultProps} />);
      
      expect(screen.getByText('⌨️ Text Mode')).toBeInTheDocument();
    });

    it('shows correct mode indicator for voice mode', () => {
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      expect(screen.getByText('🎤 Voice Mode')).toBeInTheDocument();
    });
  });

  describe('Message Handling', () => {
    it('does not send empty messages', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, '   '); // Only whitespace
      
      expect(mockOnMessageSend).not.toHaveBeenCalled();
    });

    it('handles voice transcript correctly', async () => {
      const user = userEvent.setup();
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const voiceButton = screen.getByText('Voice Input');
      await user.click(voiceButton);
      
      expect(mockOnMessageSend).toHaveBeenCalledWith('voice message');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for interactive elements', () => {
      render(<InputController {...defaultProps} />);
      
      const toggleButton = screen.getByRole('button', { name: /switch to voice input/i });
      expect(toggleButton).toHaveAttribute('aria-label');
    });

    it('provides accessible error dismissal', async () => {
      const user = userEvent.setup();
      mockOnMessageSend.mockRejectedValue(new Error('Test error'));
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      // Wait for error to appear
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      const dismissButton = screen.getByRole('button', { name: 'Dismiss error' });
      expect(dismissButton).toHaveAttribute('aria-label', 'Dismiss error');
    });
  });

  describe('Component Integration', () => {
    it('passes correct props to TextInput', () => {
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      expect(input).toBeInTheDocument();
    });

    it('passes correct props to VoiceInput', () => {
      render(<InputController {...defaultProps} inputMode="voice" />);
      
      const voiceInput = screen.getByTestId('voice-input');
      expect(voiceInput).toBeInTheDocument();
    });

    it('handles async message sending correctly', async () => {
      const user = userEvent.setup();
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      mockOnMessageSend.mockReturnValue(promise);
      
      render(<InputController {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Type your message to Nathan...');
      await user.type(input, 'test message');
      
      // Should show loading
      expect(screen.getByText('Processing...')).toBeInTheDocument();
      
      // Resolve the promise
      act(() => {
        resolvePromise!(undefined);
      });
      
      // Wait for loading to disappear
      await act(async () => {
        await promise;
      });
      
      expect(screen.queryByText('Processing...')).not.toBeInTheDocument();
    });
  });
});