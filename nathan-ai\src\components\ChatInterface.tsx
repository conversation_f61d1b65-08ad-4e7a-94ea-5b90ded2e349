import React, { useEffect, useRef } from 'react';
import { MessageBubble } from './MessageBubble';
import { Avatar } from './Avatar';
import { useConversation } from '../hooks/useConversation';
import type { ChatInterfaceProps } from '../types/components';
import type { EmotionType } from '../types/message';
import styles from './ChatInterface.module.css';

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  mode,
  personality
}) => {
  const { 
    messages, 
    isLoading,
    visualMode,
    setVisualMode,
    getLastMessage
  } = useConversation();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, [messages]);

  // Sync mode prop with internal state
  useEffect(() => {
    if (mode !== visualMode) {
      setVisualMode(mode);
    }
  }, [mode, visualMode, setVisualMode]);

  // Get <PERSON>'s current emotion based on the last message
  const getCurrentEmotion = (): EmotionType => {
    const lastMessage = getLastMessage();
    if (lastMessage && lastMessage.sender === 'nathan' && lastMessage.emotion) {
      return lastMessage.emotion;
    }
    return 'neutral';
  };

  // Handle mode toggle
  const handleModeToggle = () => {
    const newMode = visualMode === 'visual' ? 'minimal' : 'visual';
    setVisualMode(newMode);
  };

  // Show avatar in visual mode
  const showAvatar = visualMode === 'visual';

  return (
    <div className={`${styles.chatInterface} ${styles[visualMode]}`}>
      {/* Header with mode toggle */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          {showAvatar && (
            <Avatar
              emotion={getCurrentEmotion()}
              isAnimated={true}
              size="medium"
            />
          )}
          <div className={styles.headerInfo}>
            <h2 className={styles.title}>
              {personality.name}
            </h2>
            {showAvatar && (
              <p className={styles.subtitle}>
                {personality.pronouns} • {personality.personality.role}
              </p>
            )}
          </div>
        </div>
        
        <button
          className={styles.modeToggle}
          onClick={handleModeToggle}
          aria-label={`Switch to ${visualMode === 'visual' ? 'minimal' : 'visual'} mode`}
          title={`Switch to ${visualMode === 'visual' ? 'minimal' : 'visual'} mode`}
        >
          {visualMode === 'visual' ? '🔍' : '🎨'}
        </button>
      </div>

      {/* Messages container */}
      <div 
        className={styles.messagesContainer}
        ref={messagesContainerRef}
        role="log"
        aria-label="Conversation messages"
        aria-live="polite"
      >
        {messages.length === 0 && !isLoading ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyStateContent}>
              {showAvatar && (
                <Avatar
                  emotion="neutral"
                  isAnimated={true}
                  size="large"
                />
              )}
              <h3 className={styles.emptyStateTitle}>
                Start a conversation with {personality.name}
              </h3>
              <p className={styles.emptyStateDescription}>
                {personality.conversation_tips.starter_prompts[0] || 
                 "Say hello or ask me anything!"}
              </p>
            </div>
          </div>
        ) : (
          <div className={styles.messagesList}>
            {messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isUser={message.sender === 'user'}
                showAvatar={showAvatar}
                personality={personality}
              />
            ))}
            
            {/* Loading indicator */}
            {isLoading && (
              <div className={styles.loadingMessage}>
                <div className={styles.loadingBubble}>
                  {showAvatar && (
                    <Avatar
                      emotion="thoughtful"
                      isAnimated={true}
                      size="small"
                    />
                  )}
                  <div className={styles.loadingContent}>
                    <div className={styles.typingIndicator}>
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <span className={styles.loadingText}>
                      {personality.name} is thinking...
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>



      {/* Footer with conversation stats (minimal mode only) */}
      {visualMode === 'minimal' && messages.length > 0 && (
        <div className={styles.footer}>
          <span className={styles.messageCount}>
            {messages.length} message{messages.length !== 1 ? 's' : ''}
          </span>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;