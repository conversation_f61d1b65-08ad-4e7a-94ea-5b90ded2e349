import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { ConversationProvider, PersonalityProvider } from '../context';
import { useConversation, usePersonality } from '../hooks';
import { AIService } from '../services/AIService';
import type { PersonalityConfig } from '../types/personality';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  },
  getEnvVar: vi.fn().mockImplementation((key: string, defaultValue?: string) => {
    const mockEnv: Record<string, string> = {
      'VITE_HUGGING_FACE_API_KEY': 'test-hf-key',
      'VITE_ELEVEN_LABS_API_KEY': 'test-el-key',
      'VITE_ELEVEN_LABS_VOICE_ID': 'test-voice-id'
    };
    return mockEnv[key] || defaultValue || '';
  })
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock personality utilities
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn(),
  savePersonality: vi.fn(),
  validatePersonality: vi.fn().mockReturnValue({ isValid: true, errors: [] }),
  createDefaultPersonality: vi.fn().mockReturnValue({
    name: 'Nathan',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['technology', 'learning'],
      style: {
        speech: 'natural',
        humor: 'light',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: ['harmful content'],
        safe_topics: ['technology', 'general conversation']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How can I help?']
    },
    version: '1.0.0'
  })
}));

// Test component that uses both hooks
function TestComponent() {
  const conversation = useConversation();
  const personality = usePersonality();

  const handleAddMessage = () => {
    conversation.addUserMessage('Test message');
  };

  const handleAddNathanMessage = () => {
    conversation.addNathanMessage('Nathan response', 'happy');
  };

  const handleToggleMode = () => {
    conversation.toggleInputMode();
  };

  const handleToggleVisualMode = () => {
    conversation.toggleVisualMode();
  };

  const handleUpdatePersonality = () => {
    if (personality.personality) {
      personality.updatePersonality({
        ...personality.personality,
        personality: {
          ...personality.personality.personality,
          tone: 'enthusiastic'
        }
      });
    }
  };

  return (
    <div>
      <div data-testid="message-count">{conversation.messages.length}</div>
      <div data-testid="input-mode">{conversation.inputMode}</div>
      <div data-testid="visual-mode">{conversation.visualMode}</div>
      <div data-testid="is-loading">{conversation.isLoading.toString()}</div>
      <div data-testid="is-listening">{conversation.isListening.toString()}</div>
      <div data-testid="is-playing">{conversation.isPlaying.toString()}</div>
      <div data-testid="error">{conversation.error || 'none'}</div>
      <div data-testid="personality-name">{personality.personality?.name || 'Loading...'}</div>
      <div data-testid="personality-ready">{personality.isPersonalityReady.toString()}</div>
      <div data-testid="personality-tone">{personality.personality?.personality.tone || 'none'}</div>
      
      <button onClick={handleAddMessage} data-testid="add-message">Add Message</button>
      <button onClick={handleAddNathanMessage} data-testid="add-nathan-message">Add Nathan Message</button>
      <button onClick={handleToggleMode} data-testid="toggle-mode">Toggle Mode</button>
      <button onClick={handleToggleVisualMode} data-testid="toggle-visual-mode">Toggle Visual Mode</button>
      <button onClick={handleUpdatePersonality} data-testid="update-personality">Update Personality</button>
      
      <button onClick={() => conversation.setLoading(true)} data-testid="set-loading">Set Loading</button>
      <button onClick={() => conversation.setListening(true)} data-testid="set-listening">Set Listening</button>
      <button onClick={() => conversation.setPlaying(true)} data-testid="set-playing">Set Playing</button>
      <button onClick={() => conversation.setError('Test error')} data-testid="set-error">Set Error</button>
      <button onClick={() => conversation.clearMessages()} data-testid="clear-messages">Clear Messages</button>
    </div>
  );
}

// Provider wrapper
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <PersonalityProvider>
      <ConversationProvider>
        {children}
      </ConversationProvider>
    </PersonalityProvider>
  );
}

describe('State Integration Tests', () => {
  let mockAIService: any;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(async () => {
    vi.clearAllMocks();
    user = userEvent.setup();
    
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn()
    };
    
    vi.mocked(AIService).mockImplementation(() => mockAIService);

    // Setup personality mock
    const { loadPersonality, savePersonality } = await import('../utils/personality');
    vi.mocked(loadPersonality).mockResolvedValue({
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'AI companion',
        hobbies: ['technology', 'learning', 'helping others'],
        style: {
          speech: 'natural and conversational',
          humor: 'light and appropriate',
          depth: 'thoughtful and engaging'
        },
        boundaries: {
          avoid: ['harmful content', 'inappropriate topics'],
          safe_topics: ['technology', 'general conversation', 'learning']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: [
          'Hello! How are you doing today?',
          'What would you like to talk about?',
          'I\'m here to help with anything you need!'
        ]
      },
      version: '1.0.0'
    });
    vi.mocked(savePersonality).mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Conversation State Management', () => {
    it('should integrate conversation and personality state management', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Initial state
      expect(screen.getByTestId('message-count')).toHaveTextContent('0');
      expect(screen.getByTestId('input-mode')).toHaveTextContent('text');
      expect(screen.getByTestId('visual-mode')).toHaveTextContent('visual');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
      expect(screen.getByTestId('is-listening')).toHaveTextContent('false');
      expect(screen.getByTestId('is-playing')).toHaveTextContent('false');
      expect(screen.getByTestId('error')).toHaveTextContent('none');

      // Wait for personality to load
      await waitFor(() => {
        expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
        expect(screen.getByTestId('personality-tone')).toHaveTextContent('friendly');
      });

      // Test conversation actions
      act(() => {
        screen.getByTestId('add-message').click();
      });

      expect(screen.getByTestId('message-count')).toHaveTextContent('1');

      // Test mode toggle
      act(() => {
        screen.getByTestId('toggle-mode').click();
      });

      expect(screen.getByTestId('input-mode')).toHaveTextContent('voice');

      // Test visual mode toggle
      act(() => {
        screen.getByTestId('toggle-visual-mode').click();
      });

      expect(screen.getByTestId('visual-mode')).toHaveTextContent('minimal');
    });

    it('should handle multiple message additions', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Add multiple messages
      act(() => {
        screen.getByTestId('add-message').click();
        screen.getByTestId('add-nathan-message').click();
        screen.getByTestId('add-message').click();
      });

      expect(screen.getByTestId('message-count')).toHaveTextContent('3');
    });

    it('should maintain state consistency across re-renders', async () => {
      const { rerender } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Add a message and change modes
      act(() => {
        screen.getByTestId('add-message').click();
        screen.getByTestId('toggle-mode').click();
        screen.getByTestId('set-loading').click();
      });

      expect(screen.getByTestId('message-count')).toHaveTextContent('1');
      expect(screen.getByTestId('input-mode')).toHaveTextContent('voice');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('true');

      // Re-render the component
      rerender(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // State should be maintained
      expect(screen.getByTestId('message-count')).toHaveTextContent('1');
      expect(screen.getByTestId('input-mode')).toHaveTextContent('voice');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('true');
    });

    it('should handle all conversation state updates', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Test loading state
      act(() => {
        screen.getByTestId('set-loading').click();
      });
      expect(screen.getByTestId('is-loading')).toHaveTextContent('true');

      // Test listening state
      act(() => {
        screen.getByTestId('set-listening').click();
      });
      expect(screen.getByTestId('is-listening')).toHaveTextContent('true');

      // Test playing state
      act(() => {
        screen.getByTestId('set-playing').click();
      });
      expect(screen.getByTestId('is-playing')).toHaveTextContent('true');

      // Test error state
      act(() => {
        screen.getByTestId('set-error').click();
      });
      expect(screen.getByTestId('error')).toHaveTextContent('Test error');

      // Test clear messages
      act(() => {
        screen.getByTestId('add-message').click();
        screen.getByTestId('add-message').click();
      });
      expect(screen.getByTestId('message-count')).toHaveTextContent('2');

      act(() => {
        screen.getByTestId('clear-messages').click();
      });
      expect(screen.getByTestId('message-count')).toHaveTextContent('0');
    });
  });

  describe('Personality State Management', () => {
    it('should load personality on initialization', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for personality to load
      await waitFor(() => {
        expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
        expect(screen.getByTestId('personality-tone')).toHaveTextContent('friendly');
      });

      const { loadPersonality } = await import('../utils/personality');
      expect(loadPersonality).toHaveBeenCalled();
    });

    it('should update personality dynamically', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for initial personality load
      await waitFor(() => {
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
      });

      expect(screen.getByTestId('personality-tone')).toHaveTextContent('friendly');

      // Update personality
      act(() => {
        screen.getByTestId('update-personality').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('personality-tone')).toHaveTextContent('enthusiastic');
      });

      const { savePersonality } = await import('../utils/personality');
      expect(savePersonality).toHaveBeenCalled();
    });

    it('should handle personality loading errors', async () => {
      const { loadPersonality } = await import('../utils/personality');
      vi.mocked(loadPersonality).mockRejectedValueOnce(new Error('Failed to load personality'));

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Should show loading state initially
      expect(screen.getByTestId('personality-ready')).toHaveTextContent('false');

      // Should handle error and show default
      await waitFor(() => {
        expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
      });
    });

    it('should handle personality save errors', async () => {
      const { savePersonality } = await import('../utils/personality');
      vi.mocked(savePersonality).mockRejectedValueOnce(new Error('Failed to save personality'));

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
      });

      // Try to update personality (will fail)
      act(() => {
        screen.getByTestId('update-personality').click();
      });

      // Should handle error gracefully
      await waitFor(() => {
        expect(savePersonality).toHaveBeenCalled();
      });

      // Personality should revert to original state
      expect(screen.getByTestId('personality-tone')).toHaveTextContent('friendly');
    });
  });

  describe('Cross-Context State Interactions', () => {
    it('should coordinate between conversation and personality contexts', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for personality to load
      await waitFor(() => {
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
      });

      // Add messages and update personality
      act(() => {
        screen.getByTestId('add-message').click();
        screen.getByTestId('add-nathan-message').click();
        screen.getByTestId('update-personality').click();
      });

      // Both contexts should maintain their state
      expect(screen.getByTestId('message-count')).toHaveTextContent('2');
      
      await waitFor(() => {
        expect(screen.getByTestId('personality-tone')).toHaveTextContent('enthusiastic');
      });
    });

    it('should handle context provider nesting correctly', async () => {
      // Test with different nesting order
      const AlternateWrapper = ({ children }: { children: React.ReactNode }) => (
        <ConversationProvider>
          <PersonalityProvider>
            {children}
          </PersonalityProvider>
        </ConversationProvider>
      );

      render(
        <AlternateWrapper>
          <TestComponent />
        </AlternateWrapper>
      );

      // Should still work with different nesting
      await waitFor(() => {
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
      });

      act(() => {
        screen.getByTestId('add-message').click();
      });

      expect(screen.getByTestId('message-count')).toHaveTextContent('1');
    });

    it('should handle rapid state updates', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
      });

      // Perform rapid state updates
      act(() => {
        for (let i = 0; i < 10; i++) {
          screen.getByTestId('add-message').click();
        }
        screen.getByTestId('toggle-mode').click();
        screen.getByTestId('toggle-visual-mode').click();
        screen.getByTestId('set-loading').click();
        screen.getByTestId('update-personality').click();
      });

      // All updates should be applied
      expect(screen.getByTestId('message-count')).toHaveTextContent('10');
      expect(screen.getByTestId('input-mode')).toHaveTextContent('voice');
      expect(screen.getByTestId('visual-mode')).toHaveTextContent('minimal');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('true');
      
      await waitFor(() => {
        expect(screen.getByTestId('personality-tone')).toHaveTextContent('enthusiastic');
      });
    });
  });

  describe('State Persistence', () => {
    it('should persist conversation state', async () => {
      // Mock localStorage
      const mockLocalStorage = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn()
      };
      
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Add some state
      act(() => {
        screen.getByTestId('add-message').click();
        screen.getByTestId('toggle-mode').click();
      });

      // Should attempt to persist state
      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalled();
      });
    });

    it('should restore conversation state from persistence', async () => {
      const mockConversationState = {
        messages: [
          { id: '1', content: 'Restored message', timestamp: new Date(), sender: 'user' }
        ],
        inputMode: 'voice',
        visualMode: 'minimal'
      };

      const mockLocalStorage = {
        getItem: vi.fn().mockReturnValue(JSON.stringify(mockConversationState)),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn()
      };
      
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Should restore state from localStorage
      await waitFor(() => {
        expect(screen.getByTestId('message-count')).toHaveTextContent('1');
        expect(screen.getByTestId('input-mode')).toHaveTextContent('voice');
        expect(screen.getByTestId('visual-mode')).toHaveTextContent('minimal');
      });
    });

    it('should handle corrupted persistence data gracefully', async () => {
      const mockLocalStorage = {
        getItem: vi.fn().mockReturnValue('invalid-json'),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn()
      };
      
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Should start with default state when persistence data is corrupted
      expect(screen.getByTestId('message-count')).toHaveTextContent('0');
      expect(screen.getByTestId('input-mode')).toHaveTextContent('text');
      expect(screen.getByTestId('visual-mode')).toHaveTextContent('visual');
    });
  });

  describe('Memory Management', () => {
    it('should clean up resources on unmount', async () => {
      const { unmount } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');
      });

      // Add some state
      act(() => {
        screen.getByTestId('add-message').click();
        screen.getByTestId('set-loading').click();
      });

      // Unmount should not throw errors
      expect(() => unmount()).not.toThrow();
    });

    it('should handle multiple provider instances', async () => {
      const Component1 = () => (
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const Component2 = () => (
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const { unmount: unmount1 } = render(<Component1 />);
      const { unmount: unmount2 } = render(<Component2 />);

      // Both should work independently
      await waitFor(() => {
        const personalityElements = screen.getAllByTestId('personality-ready');
        personalityElements.forEach(element => {
          expect(element).toHaveTextContent('true');
        });
      });

      // Cleanup should work for both
      expect(() => {
        unmount1();
        unmount2();
      }).not.toThrow();
    });
  });
});