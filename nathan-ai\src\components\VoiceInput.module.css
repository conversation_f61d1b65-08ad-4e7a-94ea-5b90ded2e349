.voiceInputContainer {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.controls {
  display: flex;
  justify-content: center;
}

.micButton {
  position: relative;
  width: 64px;
  height: 64px;
  border: none;
  border-radius: 50%;
  background: #4f46e5;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.micButton:hover:not(.disabled) {
  background: #4338ca;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
}

.micButton:active:not(.disabled) {
  transform: scale(0.95);
}

.micButton.listening {
  background: #ef4444;
  animation: pulse 2s infinite;
}

.micButton.listening:hover {
  background: #dc2626;
}

.micButton.disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.micIcon {
  width: 28px;
  height: 28px;
  z-index: 2;
}

.listeningIndicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulseRing {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulseRing 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.5);
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.transcriptDisplay {
  min-height: 40px;
  padding: 12px 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
}

.transcriptText {
  font-size: 16px;
  line-height: 1.5;
  color: #1e293b;
  font-style: italic;
}

.listeningStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 20px;
  color: #4f46e5;
  font-size: 14px;
  font-weight: 500;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.waveform {
  display: flex;
  align-items: center;
  gap: 2px;
}

.wave {
  width: 3px;
  height: 12px;
  background: #4f46e5;
  border-radius: 2px;
  animation: wave 1.5s infinite ease-in-out;
}

.wave:nth-child(2) {
  animation-delay: 0.1s;
}

.wave:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  max-width: 400px;
  width: 100%;
}

.errorIcon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.unsupportedMessage {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  color: #92400e;
  font-size: 14px;
  max-width: 400px;
  width: 100%;
}

.warningIcon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .voiceInputContainer {
    gap: 12px;
    padding: 0 8px;
  }
  
  .micButton {
    width: 64px; /* Keep standard size for touch */
    height: 64px;
    /* Touch feedback */
    -webkit-tap-highlight-color: transparent;
  }
  
  .micIcon {
    width: 28px;
    height: 28px;
  }
  
  .pulseRing {
    width: 80px;
    height: 80px;
  }
  
  .transcriptDisplay {
    font-size: 15px; /* Slightly larger for mobile readability */
    padding: 12px 16px;
    min-height: 48px;
    max-width: calc(100vw - 32px);
    /* Improve text rendering */
    -webkit-font-smoothing: antialiased;
  }
  
  .listeningStatus {
    font-size: 14px;
    padding: 8px 16px;
    max-width: calc(100vw - 32px);
  }
  
  .errorMessage,
  .unsupportedMessage {
    max-width: calc(100vw - 32px);
    font-size: 14px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .transcriptDisplay {
    background: #1e293b;
    border-color: #475569;
  }
  
  .transcriptText {
    color: #f1f5f9;
  }
  
  .listeningStatus {
    background: rgba(99, 102, 241, 0.2);
    color: #a5b4fc;
  }
  
  .wave {
    background: #a5b4fc;
  }
  
  .errorMessage {
    background: #1f2937;
    border-color: #374151;
    color: #f87171;
  }
  
  .unsupportedMessage {
    background: #1f2937;
    border-color: #374151;
    color: #fbbf24;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .micButton,
  .pulseRing,
  .wave {
    animation: none;
  }
  
  .micButton.listening {
    animation: none;
    background: #ef4444;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .micButton {
    border: 2px solid currentColor;
  }
  
  .transcriptDisplay {
    border-width: 3px;
  }
  
  .errorMessage,
  .unsupportedMessage {
    border-width: 2px;
  }
}@m
edia (max-width: 480px) {
  .voiceInputContainer {
    gap: 10px;
    padding: 0 4px;
  }
  
  .micButton {
    width: 56px;
    height: 56px;
  }
  
  .micIcon {
    width: 24px;
    height: 24px;
  }
  
  .pulseRing {
    width: 70px;
    height: 70px;
  }
  
  .transcriptDisplay {
    font-size: 14px;
    padding: 10px 12px;
    max-width: calc(100vw - 16px);
  }
  
  .listeningStatus {
    font-size: 13px;
    padding: 6px 12px;
    max-width: calc(100vw - 16px);
  }
  
  .errorMessage,
  .unsupportedMessage {
    max-width: calc(100vw - 16px);
    font-size: 13px;
    padding: 10px 12px;
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 500px) {
  .voiceInputContainer {
    gap: 8px;
  }
  
  .micButton {
    width: 48px;
    height: 48px;
  }
  
  .micIcon {
    width: 20px;
    height: 20px;
  }
  
  .pulseRing {
    width: 60px;
    height: 60px;
  }
  
  .transcriptDisplay {
    font-size: 13px;
    padding: 8px 12px;
    min-height: 36px;
  }
  
  .listeningStatus {
    font-size: 12px;
    padding: 4px 12px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .micButton {
    /* Remove hover effects */
    transition: background-color 0.2s ease, transform 0.1s ease;
  }
  
  .micButton:hover:not(.disabled) {
    transform: none;
  }
  
  .micButton:active:not(.disabled) {
    transform: scale(0.95);
  }
  
  /* Improve touch feedback */
  .micButton:not(.disabled):active {
    background: #3730a3;
  }
  
  .micButton.listening:active {
    background: #b91c1c;
  }
}

/* Accessibility Improvements */
.micButton:focus {
  outline: 3px solid rgba(79, 70, 229, 0.5);
  outline-offset: 2px;
}

.micButton.listening:focus {
  outline-color: rgba(239, 68, 68, 0.5);
}

/* Performance Optimizations */
@media (max-width: 768px) {
  .micButton,
  .pulseRing {
    /* Enable hardware acceleration */
    transform: translateZ(0);
    will-change: transform;
  }
  
  .wave {
    will-change: transform;
  }
}

/* Prevent text selection on mobile */
@media (max-width: 768px) {
  .listeningStatus,
  .errorMessage,
  .unsupportedMessage {
    -webkit-user-select: none;
    user-select: none;
  }
  
  .transcriptDisplay {
    -webkit-user-select: text;
    user-select: text;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .transcriptDisplay {
    border-width: 1px;
  }
  
  .errorMessage,
  .unsupportedMessage {
    border-width: 0.5px;
  }
  
  .micButton {
    box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  }
}