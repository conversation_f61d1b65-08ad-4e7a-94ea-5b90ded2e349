{"name": "<PERSON> - Conversational Assistant", "short_name": "<PERSON>", "description": "An emotionally intelligent conversational AI companion with voice and text interaction capabilities", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#007bff", "orientation": "portrait-primary", "scope": "/", "lang": "en-US", "dir": "ltr", "categories": ["productivity", "utilities", "social"], "icons": [{"src": "/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "/screenshot-mobile-1.png", "sizes": "375x812", "type": "image/png", "platform": "narrow", "label": "Nathan AI conversation interface on mobile"}, {"src": "/screenshot-desktop-1.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Nathan AI conversation interface on desktop"}], "shortcuts": [{"name": "Start Voice Chat", "short_name": "Voice Chat", "description": "Start a voice conversation with <PERSON>", "url": "/?mode=voice", "icons": [{"src": "/icon-voice-96x96.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Start Text Chat", "short_name": "Text Chat", "description": "Start a text conversation with <PERSON>", "url": "/?mode=text", "icons": [{"src": "/icon-text-96x96.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "focus-existing"}, "handle_links": "preferred", "protocol_handlers": [], "file_handlers": [], "share_target": {"action": "/share", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url"}}}