import React, { useState, useEffect, useCallback } from 'react';
import { ErrorBoundary } from './components/ErrorBoundary';
import { FeatureErrorBoundary } from './components/FeatureErrorBoundary';
import { AsyncErrorBoundary } from './components/AsyncErrorBoundary';
import { ApiErrorBoundary } from './components/ApiErrorBoundary';
import { 
  LazyNotificationSystem, 
  LazyCompatibilityWarning, 
  LazyAudioPlayer,
  LazyWrapper 
} from './components/LazyComponents';
import { ConversationProvider } from './context/ConversationContext';
import { PersonalityProvider, usePersonalityContext } from './context/PersonalityContext';
import { ChatInterface } from './components/ChatInterface';
import { InputController } from './components/InputController';
import { useAudioConversation } from './hooks/useAudioConversation';
import { useMobileOptimizations } from './hooks/useMobileResponsive';
import { registerServiceWorker, onNetworkChange } from './utils/serviceWorker';
import { startMemoryMonitoring, getBatteryOptimizedSettings } from './utils/mobileOptimizations';

import { validateEnvironment } from './utils/env';
import type { InputMode } from './types/conversation';
import styles from './App.module.css';
import './styles/responsive.css';

// Theme types
export type Theme = 'light' | 'dark' | 'auto';
export type VisualMode = 'visual' | 'minimal';

interface AppState {
  theme: Theme;
  visualMode: VisualMode;
  isInitialized: boolean;
  initializationError: string | null;
  isOffline: boolean;
  batteryOptimized: boolean;
}

// Main App Content Component (needs to be inside providers)
function AppContent() {
  const { personality, isLoading: personalityLoading, error: personalityError } = usePersonalityContext();
  const {
    inputMode,
    isListening,
    isLoading: conversationLoading,
    isPlaying,
    error: conversationError,
    audioQueue,
    isGeneratingAudio,
    setInputMode,
    setError,
    sendMessageWithAudio,

    handleAudioPlayingChange,
    handleAudioComplete,
    handleAudioError
  } = useAudioConversation();

  // Mobile optimizations
  const mobileState = useMobileOptimizations();

  const [appState, setAppState] = useState<AppState>({
    theme: 'auto',
    visualMode: 'visual',
    isInitialized: false,
    initializationError: null,
    isOffline: !navigator.onLine,
    batteryOptimized: false,
  });

  // Initialize application
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Register service worker for offline support
        if (import.meta.env.NODE_ENV === 'production') {
          await registerServiceWorker();
        }

        // Set up network monitoring
        const cleanupNetworkListener = onNetworkChange((online) => {
          setAppState(prev => ({ ...prev, isOffline: !online }));
        });

        // Set up memory monitoring
        const cleanupMemoryMonitor = startMemoryMonitoring(() => {
          console.log('Memory pressure detected, optimizing performance');
        });

        // Check battery optimization needs
        const batterySettings = await getBatteryOptimizedSettings();
        setAppState(prev => ({ ...prev, batteryOptimized: batterySettings.disableAnimations }));

        // Validate environment variables (skip in test environment)
        if (import.meta.env.NODE_ENV !== 'test') {
          const envValidation = validateEnvironment();
          if (!envValidation.isValid) {
            throw new Error(`Environment validation failed: ${envValidation.errors.join(', ')}`);
          }
        }

        // Load saved preferences from localStorage
        const savedTheme = localStorage.getItem('nathan-theme') as Theme;
        const savedVisualMode = localStorage.getItem('nathan-visual-mode') as VisualMode;
        const savedInputMode = localStorage.getItem('nathan-input-mode') as InputMode;

        if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
          setAppState(prev => ({ ...prev, theme: savedTheme }));
        }

        if (savedVisualMode && ['visual', 'minimal'].includes(savedVisualMode)) {
          setAppState(prev => ({ ...prev, visualMode: savedVisualMode }));
        }

        if (savedInputMode && ['voice', 'text'].includes(savedInputMode)) {
          setInputMode(savedInputMode);
        }

        // Apply theme to document
        applyTheme(savedTheme || 'auto');

        // Perform basic initialization checks
        try {
          // Check if required APIs are available
          if (typeof window !== 'undefined') {
            // Check for Web Speech API support
            const speechSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
            if (!speechSupported) {
              console.warn('Web Speech API not supported in this browser');
            }
            
            // Check for Audio API support
            const audioSupported = 'Audio' in window;
            if (!audioSupported) {
              console.warn('Audio API not supported in this browser');
            }
          }
        } catch (error) {
          console.warn('Browser compatibility check error:', error);
        }

        setAppState(prev => ({ 
          ...prev, 
          isInitialized: true,
          initializationError: null 
        }));

        // Cleanup function will be called on unmount
        return () => {
          cleanupNetworkListener?.();
          cleanupMemoryMonitor?.();
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize application';
        console.error('App initialization failed:', error);
        setAppState(prev => ({ 
          ...prev, 
          isInitialized: true,
          initializationError: errorMessage 
        }));
      }
    };

    initializeApp();
  }, [setInputMode]);

  // Apply theme to document
  const applyTheme = (theme: Theme) => {
    const root = document.documentElement;
    
    if (theme === 'auto') {
      // Use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    } else {
      root.setAttribute('data-theme', theme);
    }
  };

  // Theme change handler
  const handleThemeChange = (newTheme: Theme) => {
    setAppState(prev => ({ ...prev, theme: newTheme }));
    localStorage.setItem('nathan-theme', newTheme);
    applyTheme(newTheme);
  };

  // Visual mode change handler
  const handleVisualModeChange = (newMode: VisualMode) => {
    setAppState(prev => ({ ...prev, visualMode: newMode }));
    localStorage.setItem('nathan-visual-mode', newMode);
  };

  // Input mode change handler
  const handleInputModeChange = useCallback((newMode: InputMode) => {
    setInputMode(newMode);
    localStorage.setItem('nathan-input-mode', newMode);
    setError(null); // Clear any input-related errors
  }, [setInputMode, setError]);

  // Message send handler with improved error handling
  const handleMessageSend = useCallback(async (message: string) => {
    if (!message.trim()) {
      setError('Please enter a message');
      return;
    }

    if (!personality) {
      setError('Personality not loaded. Please refresh the page.');
      return;
    }

    try {
      // Clear any previous errors
      setError(null);
      await sendMessageWithAudio(message);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      console.error('Message send error:', error);
      
      // Provide more specific error messages
      if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        setError('Network error. Please check your connection and try again.');
      } else if (errorMessage.includes('API')) {
        setError('Service temporarily unavailable. Please try again in a moment.');
      } else {
        setError(errorMessage);
      }
    }
  }, [personality, sendMessageWithAudio, setError]);

  // Loading screen
  if (!appState.isInitialized || personalityLoading) {
    return (
      <div className={styles.loadingScreen}>
        <div className={styles.loadingContent}>
          <div className={styles.loadingSpinner} />
          <h2>Initializing Nathan...</h2>
          <p>Setting up your conversational AI experience</p>
        </div>
      </div>
    );
  }

  // Initialization error screen
  if (appState.initializationError || personalityError) {
    const errorMessage = appState.initializationError || personalityError;
    return (
      <div className={styles.errorScreen}>
        <div className={styles.errorContent}>
          <h2>Initialization Failed</h2>
          <p>{errorMessage}</p>
          <button 
            onClick={() => window.location.reload()}
            className={styles.retryButton}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Personality not loaded
  if (!personality) {
    return (
      <div className={styles.errorScreen}>
        <div className={styles.errorContent}>
          <h2>Personality Not Available</h2>
          <p>Unable to load Nathan's personality configuration</p>
          <button 
            onClick={() => window.location.reload()}
            className={styles.retryButton}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`${styles.app} ${styles[appState.visualMode]} ${mobileState.isMobile ? 'mobile-device' : ''} ${mobileState.isTouch ? 'touch-device' : ''} ${appState.batteryOptimized ? 'battery-optimized' : ''}`}
      data-theme={appState.theme}
      data-mobile={mobileState.isMobile}
      data-orientation={mobileState.orientation}
      data-offline={appState.isOffline}
      data-battery-optimized={appState.batteryOptimized}
    >
            <header className={styles.header}>
              <div className={styles.headerContent}>
                <h1 className={styles.title}>Nathan</h1>
                <div className={styles.controls}>
                  <div className={styles.controlGroup}>
                    <label htmlFor="theme-select">Theme:</label>
                    <select
                      id="theme-select"
                      value={appState.theme}
                      onChange={(e) => handleThemeChange(e.target.value as Theme)}
                      className={styles.select}
                    >
                      <option value="auto">Auto</option>
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                    </select>
                  </div>
                  <div className={styles.controlGroup}>
                    <label htmlFor="mode-select">Mode:</label>
                    <select
                      id="mode-select"
                      value={appState.visualMode}
                      onChange={(e) => handleVisualModeChange(e.target.value as VisualMode)}
                      className={styles.select}
                    >
                      <option value="visual">Visual</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>
                </div>
              </div>
            </header>
            
            <main className={styles.main}>
              <ApiErrorBoundary 
                serviceName="AI Services"
                onRetry={() => {
                  // Clear any errors and try to reinitialize
                  setError(null);
                  window.location.reload();
                }}
              >
                <div className={styles.conversationArea}>
                  <ChatInterface 
                    mode={appState.visualMode} 
                    personality={personality}
                  />
                </div>
                
                <div className={styles.inputArea}>
                  <InputController
                    inputMode={inputMode}
                    onModeChange={handleInputModeChange}
                    onMessageSend={handleMessageSend}
                    isListening={isListening}
                  />
                </div>
              </ApiErrorBoundary>

              {/* Audio Player - Lazy loaded and hidden but functional */}
              <FeatureErrorBoundary 
                featureName="Audio Player"
                onError={(error, errorInfo) => {
                  console.error('Audio player feature error:', error, errorInfo);
                  setError('Audio playback is temporarily unavailable. Text responses will still work.');
                }}
              >
                <LazyWrapper componentName="Audio Player">
                  <LazyAudioPlayer
                    audioQueue={audioQueue}
                    isPlaying={isPlaying}
                    onPlayingChange={handleAudioPlayingChange}
                    onAudioComplete={handleAudioComplete}
                    onError={handleAudioError}
                  />
                </LazyWrapper>
              </FeatureErrorBoundary>

              {/* Global error display */}
              {conversationError && (
                <div className={styles.globalError}>
                  <div className={styles.errorMessage}>
                    <span className={styles.errorIcon}>⚠️</span>
                    <span>{conversationError}</span>
                    <button
                      onClick={() => setError(null)}
                      className={styles.errorDismiss}
                      aria-label="Dismiss error"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              )}

              {/* Global loading overlay */}
              {(conversationLoading || isGeneratingAudio) && (
                <div className={styles.globalLoading}>
                  <div className={styles.loadingOverlay}>
                    <div className={styles.loadingSpinner} />
                    <span>
                      {isGeneratingAudio ? 'Generating audio response...' : 'Nathan is thinking...'}
                    </span>
                    {conversationLoading && (
                      <button
                        onClick={() => setError('Conversation interrupted by user')}
                        className={styles.cancelButton}
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </div>
              )}
            </main>

            {/* Browser Compatibility Warning - Lazy loaded */}
            <LazyWrapper componentName="Compatibility Warning">
              <LazyCompatibilityWarning />
            </LazyWrapper>

            {/* Notification System - Lazy loaded */}
            <LazyWrapper componentName="Notification System">
              <LazyNotificationSystem />
            </LazyWrapper>

            {/* Offline indicator */}
            {appState.isOffline && (
              <div className={styles.offlineIndicator}>
                <span>📡 You're offline. Some features may be limited.</span>
              </div>
            )}
    </div>
  );
}

// Main App Component with Providers
function App() {
  // Error handler for ErrorBoundary
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Application error caught by boundary:', error, errorInfo);
    // Here you could send error reports to a logging service
  };

  return (
    <ErrorBoundary onError={handleError} level="app">
      <AsyncErrorBoundary
        onAsyncError={(error) => {
          console.error('Unhandled async error in app:', error);
        }}
      >
        <PersonalityProvider>
          <ConversationProvider>
            <AppContent />
          </ConversationProvider>
        </PersonalityProvider>
      </AsyncErrorBoundary>
    </ErrorBoundary>
  );
}

export default App;
