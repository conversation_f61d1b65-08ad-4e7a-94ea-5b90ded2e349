/**
 * Performance optimization utilities
 */

// Performance monitoring
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  bundleSize: number;
  apiResponseTime: number;
  audioLatency: number;
}

// Cache for expensive computations
const computationCache = new Map<string, any>();

/**
 * Memoization utility for expensive computations
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string
): T {
  return ((...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    if (computationCache.has(key)) {
      return computationCache.get(key);
    }
    
    const result = fn(...args);
    computationCache.set(key, result);
    
    // Limit cache size to prevent memory leaks
    if (computationCache.size > 100) {
      const firstKey = computationCache.keys().next().value;
      computationCache.delete(firstKey);
    }
    
    return result;
  }) as T;
}

/**
 * Clear computation cache
 */
export function clearCache(): void {
  computationCache.clear();
}

/**
 * Debounce utility for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle utility for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Performance monitoring hook
 */
export function measurePerformance<T>(
  name: string,
  fn: () => T
): T {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  console.log(`Performance: ${name} took ${end - start} milliseconds`);
  
  return result;
}

/**
 * Memory usage monitoring
 */
export function getMemoryUsage(): number {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
  }
  return 0;
}

/**
 * Bundle size analyzer
 */
export function analyzeBundleSize(): Promise<number> {
  return new Promise((resolve) => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      resolve(connection.downlink || 0);
    } else {
      resolve(0);
    }
  });
}

/**
 * API response time tracker
 */
export class ApiPerformanceTracker {
  private static metrics: Map<string, number[]> = new Map();
  
  static startTracking(endpoint: string): () => void {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      
      if (!this.metrics.has(endpoint)) {
        this.metrics.set(endpoint, []);
      }
      
      const times = this.metrics.get(endpoint)!;
      times.push(duration);
      
      // Keep only last 10 measurements
      if (times.length > 10) {
        times.shift();
      }
    };
  }
  
  static getAverageTime(endpoint: string): number {
    const times = this.metrics.get(endpoint);
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
  
  static getAllMetrics(): Record<string, number> {
    const result: Record<string, number> = {};
    
    for (const [endpoint, times] of this.metrics.entries()) {
      result[endpoint] = this.getAverageTime(endpoint);
    }
    
    return result;
  }
}

/**
 * Audio latency tracker
 */
export class AudioLatencyTracker {
  private static latencies: number[] = [];
  
  static trackLatency(startTime: number): void {
    const latency = performance.now() - startTime;
    this.latencies.push(latency);
    
    // Keep only last 20 measurements
    if (this.latencies.length > 20) {
      this.latencies.shift();
    }
  }
  
  static getAverageLatency(): number {
    if (this.latencies.length === 0) return 0;
    
    return this.latencies.reduce((sum, latency) => sum + latency, 0) / this.latencies.length;
  }
}

/**
 * Image optimization utility
 */
export function optimizeImage(
  src: string,
  width?: number,
  height?: number,
  quality: number = 0.8
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }
      
      // Set canvas dimensions
      canvas.width = width || img.width;
      canvas.height = height || img.height;
      
      // Draw and compress image
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      // Convert to optimized data URL
      const optimizedSrc = canvas.toDataURL('image/webp', quality);
      resolve(optimizedSrc);
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = src;
  });
}

/**
 * Preload critical resources
 */
export function preloadCriticalResources(): void {
  // Preload critical CSS
  const criticalCSS = [
    '/src/styles/responsive.css',
    '/src/App.module.css'
  ];
  
  criticalCSS.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    document.head.appendChild(link);
  });
  
  // Preload critical JavaScript modules
  const criticalModules = [
    '/src/components/ChatInterface.tsx',
    '/src/components/InputController.tsx'
  ];
  
  criticalModules.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'modulepreload';
    link.href = href;
    document.head.appendChild(link);
  });
}

/**
 * Lazy loading intersection observer
 */
export function createLazyLoadObserver(
  callback: (entry: IntersectionObserverEntry) => void,
  options?: IntersectionObserverInit
): IntersectionObserver {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach(callback);
    },
    {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }
  );
}

/**
 * Performance budget checker
 */
export interface PerformanceBudget {
  maxBundleSize: number; // KB
  maxRenderTime: number; // ms
  maxMemoryUsage: number; // MB
  maxApiResponseTime: number; // ms
}

export function checkPerformanceBudget(
  metrics: PerformanceMetrics,
  budget: PerformanceBudget
): { passed: boolean; violations: string[] } {
  const violations: string[] = [];
  
  if (metrics.bundleSize > budget.maxBundleSize) {
    violations.push(`Bundle size exceeded: ${metrics.bundleSize}KB > ${budget.maxBundleSize}KB`);
  }
  
  if (metrics.renderTime > budget.maxRenderTime) {
    violations.push(`Render time exceeded: ${metrics.renderTime}ms > ${budget.maxRenderTime}ms`);
  }
  
  if (metrics.memoryUsage > budget.maxMemoryUsage) {
    violations.push(`Memory usage exceeded: ${metrics.memoryUsage}MB > ${budget.maxMemoryUsage}MB`);
  }
  
  if (metrics.apiResponseTime > budget.maxApiResponseTime) {
    violations.push(`API response time exceeded: ${metrics.apiResponseTime}ms > ${budget.maxApiResponseTime}ms`);
  }
  
  return {
    passed: violations.length === 0,
    violations
  };
}