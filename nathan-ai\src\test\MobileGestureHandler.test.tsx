import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MobileGestureHandler } from '../components/MobileGestureHandler';

// Mock the useMobileResponsive hook
vi.mock('../hooks/useMobileResponsive', () => ({
  useMobileResponsive: () => ({
    isMobile: true,
    isTablet: false,
    isDesktop: false,
    orientation: 'portrait' as const,
    screenSize: { width: 375, height: 667 }
  }),
  useSwipeGestures: () => ({
    onTouchStart: vi.fn(),
    onTouchMove: vi.fn(),
    onTouchEnd: vi.fn(),
    gestureState: {
      isActive: false,
      direction: null,
      distance: 0,
      velocity: 0
    }
  }),
  useViewportHeight: () => ({
    viewportHeight: 667,
    keyboardHeight: 0,
    isKeyboardVisible: false
  }),
  useKeyboardHeight: () => ({
    keyboardHeight: 0,
    isVisible: false
  }),
  useMobileOptimizations: () => ({
    isOptimized: true,
    performanceMode: 'balanced',
    reducedMotion: false
  })
}));

describe('MobileGestureHandler', () => {
  const mockOnSwipeLeft = vi.fn();
  const mockOnSwipeRight = vi.fn();
  const mockOnSwipeUp = vi.fn();
  const mockOnSwipeDown = vi.fn();
  const mockOnPinch = vi.fn();
  const mockOnDoubleTap = vi.fn();
  const mockOnLongPress = vi.fn();

  const defaultProps = {
    onSwipeLeft: mockOnSwipeLeft,
    onSwipeRight: mockOnSwipeRight,
    onSwipeUp: mockOnSwipeUp,
    onSwipeDown: mockOnSwipeDown,
    onPinch: mockOnPinch,
    onDoubleTap: mockOnDoubleTap,
    onLongPress: mockOnLongPress,
    children: <div data-testid="child-content">Test Content</div>
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders children correctly', () => {
    render(<MobileGestureHandler {...defaultProps} />);
    
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    const { container } = render(<MobileGestureHandler {...defaultProps} />);
    
    const gestureHandler = container.firstChild as HTMLElement;
    expect(gestureHandler.className).toContain('gestureHandler');
  });

  describe('Touch Events', () => {
    it('handles touchstart event', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      const touchEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });

      fireEvent(gestureHandler, touchEvent);

      // Should not throw and should prepare for gesture detection
      expect(gestureHandler).toBeInTheDocument();
    });

    it('handles touchmove event', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // Move touch
      const touchMoveEvent = new TouchEvent('touchmove', {
        touches: [{ clientX: 150, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchMoveEvent);

      expect(gestureHandler).toBeInTheDocument();
    });

    it('handles touchend event and detects swipe right', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // End touch with significant horizontal movement
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 200, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnSwipeRight).toHaveBeenCalledWith({
        direction: 'right',
        distance: 100,
        velocity: expect.any(Number),
        startPoint: { x: 100, y: 100 },
        endPoint: { x: 200, y: 100 }
      });
    });

    it('handles touchend event and detects swipe left', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 200, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // End touch with significant horizontal movement to the left
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnSwipeLeft).toHaveBeenCalledWith({
        direction: 'left',
        distance: 100,
        velocity: expect.any(Number),
        startPoint: { x: 200, y: 100 },
        endPoint: { x: 100, y: 100 }
      });
    });

    it('handles touchend event and detects swipe up', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 200 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // End touch with significant vertical movement up
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnSwipeUp).toHaveBeenCalledWith({
        direction: 'up',
        distance: 100,
        velocity: expect.any(Number),
        startPoint: { x: 100, y: 200 },
        endPoint: { x: 100, y: 100 }
      });
    });

    it('handles touchend event and detects swipe down', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // End touch with significant vertical movement down
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 200 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnSwipeDown).toHaveBeenCalledWith({
        direction: 'down',
        distance: 100,
        velocity: expect.any(Number),
        startPoint: { x: 100, y: 100 },
        endPoint: { x: 100, y: 200 }
      });
    });

    it('does not trigger swipe for small movements', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // End touch with small movement (below threshold)
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 110, clientY: 105 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnSwipeLeft).not.toHaveBeenCalled();
      expect(mockOnSwipeRight).not.toHaveBeenCalled();
      expect(mockOnSwipeUp).not.toHaveBeenCalled();
      expect(mockOnSwipeDown).not.toHaveBeenCalled();
    });
  });

  describe('Double Tap Detection', () => {
    it('detects double tap', () => {
      vi.useFakeTimers();
      
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // First tap
      const touchStartEvent1 = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      const touchEndEvent1 = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      
      fireEvent(gestureHandler, touchStartEvent1);
      fireEvent(gestureHandler, touchEndEvent1);

      // Second tap within double tap time window
      vi.advanceTimersByTime(200); // Within 300ms window
      
      const touchStartEvent2 = new TouchEvent('touchstart', {
        touches: [{ clientX: 105, clientY: 105 } as Touch]
      });
      const touchEndEvent2 = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 105, clientY: 105 } as Touch]
      });
      
      fireEvent(gestureHandler, touchStartEvent2);
      fireEvent(gestureHandler, touchEndEvent2);

      expect(mockOnDoubleTap).toHaveBeenCalledWith({
        point: { x: 105, y: 105 },
        timeBetweenTaps: expect.any(Number)
      });

      vi.useRealTimers();
    });

    it('does not detect double tap if taps are too far apart in time', () => {
      vi.useFakeTimers();
      
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // First tap
      const touchStartEvent1 = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      const touchEndEvent1 = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      
      fireEvent(gestureHandler, touchStartEvent1);
      fireEvent(gestureHandler, touchEndEvent1);

      // Second tap after double tap time window
      vi.advanceTimersByTime(400); // Beyond 300ms window
      
      const touchStartEvent2 = new TouchEvent('touchstart', {
        touches: [{ clientX: 105, clientY: 105 } as Touch]
      });
      const touchEndEvent2 = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 105, clientY: 105 } as Touch]
      });
      
      fireEvent(gestureHandler, touchStartEvent2);
      fireEvent(gestureHandler, touchEndEvent2);

      expect(mockOnDoubleTap).not.toHaveBeenCalled();

      vi.useRealTimers();
    });
  });

  describe('Long Press Detection', () => {
    it('detects long press', () => {
      vi.useFakeTimers();
      
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // Advance time to trigger long press
      vi.advanceTimersByTime(600); // Beyond 500ms threshold

      expect(mockOnLongPress).toHaveBeenCalledWith({
        point: { x: 100, y: 100 },
        duration: 600
      });

      vi.useRealTimers();
    });

    it('cancels long press on touchend before threshold', () => {
      vi.useFakeTimers();
      
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // End touch before long press threshold
      vi.advanceTimersByTime(300);
      
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      // Advance past threshold
      vi.advanceTimersByTime(300);

      expect(mockOnLongPress).not.toHaveBeenCalled();

      vi.useRealTimers();
    });

    it('cancels long press on touchmove beyond threshold', () => {
      vi.useFakeTimers();
      
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start touch
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // Move touch beyond movement threshold
      const touchMoveEvent = new TouchEvent('touchmove', {
        touches: [{ clientX: 120, clientY: 120 } as Touch]
      });
      fireEvent(gestureHandler, touchMoveEvent);

      // Advance past long press threshold
      vi.advanceTimersByTime(600);

      expect(mockOnLongPress).not.toHaveBeenCalled();

      vi.useRealTimers();
    });
  });

  describe('Pinch Detection', () => {
    it('detects pinch gesture with two fingers', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start with two fingers
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [
          { clientX: 100, clientY: 100 } as Touch,
          { clientX: 200, clientY: 200 } as Touch
        ]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // Move fingers closer together (pinch in)
      const touchMoveEvent = new TouchEvent('touchmove', {
        touches: [
          { clientX: 120, clientY: 120 } as Touch,
          { clientX: 180, clientY: 180 } as Touch
        ]
      });
      fireEvent(gestureHandler, touchMoveEvent);

      // End touch
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [
          { clientX: 120, clientY: 120 } as Touch,
          { clientX: 180, clientY: 180 } as Touch
        ]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnPinch).toHaveBeenCalledWith({
        scale: expect.any(Number),
        center: { x: 150, y: 150 },
        initialDistance: expect.any(Number),
        finalDistance: expect.any(Number)
      });
    });

    it('does not detect pinch with single finger', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Start with one finger
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      // Move finger
      const touchMoveEvent = new TouchEvent('touchmove', {
        touches: [{ clientX: 150, clientY: 150 } as Touch]
      });
      fireEvent(gestureHandler, touchMoveEvent);

      // End touch
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 150, clientY: 150 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      expect(mockOnPinch).not.toHaveBeenCalled();
    });
  });

  describe('Optional handlers', () => {
    it('works without optional handlers', () => {
      const minimalProps = {
        children: <div data-testid="child-content">Test Content</div>
      };

      render(<MobileGestureHandler {...minimalProps} />);
      
      expect(screen.getByTestId('child-content')).toBeInTheDocument();
    });

    it('only calls provided handlers', () => {
      const partialProps = {
        onSwipeLeft: mockOnSwipeLeft,
        onDoubleTap: mockOnDoubleTap,
        children: <div data-testid="child-content">Test Content</div>
      };

      const { container } = render(<MobileGestureHandler {...partialProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Trigger swipe right (handler not provided)
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchStartEvent);

      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 200, clientY: 100 } as Touch]
      });
      fireEvent(gestureHandler, touchEndEvent);

      // Should not throw error even though onSwipeRight is not provided
      expect(mockOnSwipeRight).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('maintains accessibility attributes', () => {
      const { container } = render(
        <MobileGestureHandler {...defaultProps}>
          <button aria-label="Test button">Click me</button>
        </MobileGestureHandler>
      );

      const button = screen.getByLabelText('Test button');
      expect(button).toBeInTheDocument();
    });

    it('does not interfere with keyboard navigation', () => {
      render(
        <MobileGestureHandler {...defaultProps}>
          <button>Click me</button>
        </MobileGestureHandler>
      );

      const button = screen.getByRole('button');
      
      // Should be able to focus with keyboard
      button.focus();
      expect(button).toHaveFocus();
    });
  });

  describe('Performance', () => {
    it('cleans up event listeners on unmount', () => {
      const { unmount } = render(<MobileGestureHandler {...defaultProps} />);
      
      // Should not throw on unmount
      expect(() => unmount()).not.toThrow();
    });

    it('handles rapid touch events without errors', () => {
      const { container } = render(<MobileGestureHandler {...defaultProps} />);
      const gestureHandler = container.firstChild as HTMLElement;

      // Rapid fire touch events
      for (let i = 0; i < 10; i++) {
        const touchStartEvent = new TouchEvent('touchstart', {
          touches: [{ clientX: 100 + i, clientY: 100 + i } as Touch]
        });
        const touchEndEvent = new TouchEvent('touchend', {
          changedTouches: [{ clientX: 100 + i, clientY: 100 + i } as Touch]
        });
        
        fireEvent(gestureHandler, touchStartEvent);
        fireEvent(gestureHandler, touchEndEvent);
      }

      // Should not throw errors
      expect(gestureHandler).toBeInTheDocument();
    });
  });
});