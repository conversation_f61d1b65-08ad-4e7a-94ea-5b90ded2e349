/**
 * Image optimization utilities
 */

export interface ImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  maintainAspectRatio?: boolean;
}

/**
 * Optimize image for web delivery
 */
export async function optimizeImage(
  src: string | File,
  options: ImageOptimizationOptions = {}
): Promise<string> {
  const {
    width,
    height,
    quality = 0.8,
    format = 'webp',
    maintainAspectRatio = true
  } = options;

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }
        
        // Calculate dimensions
        let targetWidth = width || img.width;
        let targetHeight = height || img.height;
        
        if (maintainAspectRatio && width && height) {
          const aspectRatio = img.width / img.height;
          const targetAspectRatio = width / height;
          
          if (aspectRatio > targetAspectRatio) {
            targetHeight = width / aspectRatio;
          } else {
            targetWidth = height * aspectRatio;
          }
        }
        
        // Set canvas dimensions
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        
        // Enable image smoothing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // Draw and optimize image
        ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
        
        // Convert to optimized format
        const mimeType = `image/${format}`;
        const optimizedSrc = canvas.toDataURL(mimeType, quality);
        
        resolve(optimizedSrc);
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    
    if (typeof src === 'string') {
      img.src = src;
    } else {
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(src);
    }
  });
}

/**
 * Create responsive image srcset
 */
export async function createResponsiveImages(
  src: string,
  sizes: number[] = [320, 640, 1024, 1920]
): Promise<{ srcset: string; sizes: string }> {
  const optimizedImages = await Promise.all(
    sizes.map(async (size) => {
      const optimized = await optimizeImage(src, { width: size });
      return `${optimized} ${size}w`;
    })
  );
  
  const srcset = optimizedImages.join(', ');
  const sizesAttr = sizes
    .map((size, index) => {
      if (index === sizes.length - 1) {
        return `${size}px`;
      }
      return `(max-width: ${size}px) ${size}px`;
    })
    .join(', ');
  
  return { srcset, sizes: sizesAttr };
}

/**
 * Lazy load images with intersection observer
 */
export class LazyImageLoader {
  private observer: IntersectionObserver;
  private loadedImages = new Set<HTMLImageElement>();
  
  constructor(options: IntersectionObserverInit = {}) {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options
      }
    );
  }
  
  observe(img: HTMLImageElement): void {
    if (this.loadedImages.has(img)) {
      return;
    }
    
    this.observer.observe(img);
  }
  
  unobserve(img: HTMLImageElement): void {
    this.observer.unobserve(img);
    this.loadedImages.delete(img);
  }
  
  disconnect(): void {
    this.observer.disconnect();
    this.loadedImages.clear();
  }
  
  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        this.loadImage(img);
        this.observer.unobserve(img);
      }
    });
  }
  
  private async loadImage(img: HTMLImageElement): Promise<void> {
    const dataSrc = img.dataset.src;
    const dataSrcset = img.dataset.srcset;
    
    if (!dataSrc) {
      return;
    }
    
    try {
      // Create a new image to preload
      const preloadImg = new Image();
      
      await new Promise((resolve, reject) => {
        preloadImg.onload = resolve;
        preloadImg.onerror = reject;
        preloadImg.src = dataSrc;
      });
      
      // Update the actual image
      img.src = dataSrc;
      if (dataSrcset) {
        img.srcset = dataSrcset;
      }
      
      // Remove data attributes
      delete img.dataset.src;
      delete img.dataset.srcset;
      
      // Add loaded class for CSS transitions
      img.classList.add('loaded');
      
      this.loadedImages.add(img);
    } catch (error) {
      console.warn('Failed to load lazy image:', error);
      img.classList.add('error');
    }
  }
}

/**
 * Preload critical images
 */
export function preloadCriticalImages(urls: string[]): Promise<void[]> {
  return Promise.all(
    urls.map((url) => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Failed to preload image: ${url}`));
        img.src = url;
      });
    })
  );
}

/**
 * Convert image to WebP format if supported
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Get optimal image format based on browser support
 */
export async function getOptimalImageFormat(): Promise<'webp' | 'jpeg'> {
  const webpSupported = await supportsWebP();
  return webpSupported ? 'webp' : 'jpeg';
}

/**
 * Compress image file for upload
 */
export async function compressImageFile(
  file: File,
  maxSizeKB: number = 500,
  quality: number = 0.8
): Promise<File> {
  if (file.size <= maxSizeKB * 1024) {
    return file;
  }
  
  const optimizedDataUrl = await optimizeImage(file, { quality });
  
  // Convert data URL back to File
  const response = await fetch(optimizedDataUrl);
  const blob = await response.blob();
  
  return new File([blob], file.name, {
    type: blob.type,
    lastModified: Date.now()
  });
}

/**
 * Generate placeholder image (blur effect)
 */
export async function generatePlaceholder(
  src: string,
  blurAmount: number = 10
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }
      
      // Create small placeholder (for blur effect)
      const scale = 0.1;
      canvas.width = img.width * scale;
      canvas.height = img.height * scale;
      
      // Apply blur filter
      ctx.filter = `blur(${blurAmount}px)`;
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      const placeholder = canvas.toDataURL('image/jpeg', 0.1);
      resolve(placeholder);
    };
    
    img.onerror = () => reject(new Error('Failed to load image for placeholder'));
    img.src = src;
  });
}

/**
 * Image optimization hook for React components
 */
export function useImageOptimization() {
  const lazyLoader = new LazyImageLoader();
  
  const optimizeAndLoad = async (
    element: HTMLImageElement,
    src: string,
    options?: ImageOptimizationOptions
  ) => {
    try {
      const optimized = await optimizeImage(src, options);
      element.src = optimized;
      element.classList.add('optimized');
    } catch (error) {
      console.warn('Image optimization failed, using original:', error);
      element.src = src;
    }
  };
  
  const enableLazyLoading = (element: HTMLImageElement) => {
    lazyLoader.observe(element);
  };
  
  const cleanup = () => {
    lazyLoader.disconnect();
  };
  
  return {
    optimizeAndLoad,
    enableLazyLoading,
    cleanup
  };
}