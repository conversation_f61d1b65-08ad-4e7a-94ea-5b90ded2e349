import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import React from 'react';
import { ConversationProvider, PersonalityProvider } from '../context';
import { useConversation, usePersonality } from '../hooks';

// Mock the personality utility
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn().mockResolvedValue({
    name: '<PERSON>',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['coding', 'music'],
      style: {
        speech: 'casual',
        humor: 'witty',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: ['politics'],
        safe_topics: ['technology', 'hobbies']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How are you?']
    },
    version: '1.0.0'
  }),
  savePersonality: vi.fn().mockResolvedValue(undefined)
}));

// Test component that uses both hooks
function TestComponent() {
  const conversation = useConversation();
  const personality = usePersonality();

  const handleAddMessage = () => {
    conversation.addUserMessage('Test message');
  };

  const handleToggleMode = () => {
    conversation.toggleInputMode();
  };

  return (
    <div>
      <div data-testid="message-count">{conversation.messages.length}</div>
      <div data-testid="input-mode">{conversation.inputMode}</div>
      <div data-testid="visual-mode">{conversation.visualMode}</div>
      <div data-testid="personality-name">{personality.personality?.name || 'Loading...'}</div>
      <div data-testid="personality-ready">{personality.isPersonalityReady.toString()}</div>
      <button onClick={handleAddMessage} data-testid="add-message">Add Message</button>
      <button onClick={handleToggleMode} data-testid="toggle-mode">Toggle Mode</button>
    </div>
  );
}

// Provider wrapper
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <PersonalityProvider>
      <ConversationProvider>
        {children}
      </ConversationProvider>
    </PersonalityProvider>
  );
}

describe('State Management Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should integrate conversation and personality state management', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Initial state
    expect(screen.getByTestId('message-count')).toHaveTextContent('0');
    expect(screen.getByTestId('input-mode')).toHaveTextContent('text');
    expect(screen.getByTestId('visual-mode')).toHaveTextContent('visual');

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
    expect(screen.getByTestId('personality-ready')).toHaveTextContent('true');

    // Test conversation actions
    act(() => {
      screen.getByTestId('add-message').click();
    });

    expect(screen.getByTestId('message-count')).toHaveTextContent('1');

    // Test mode toggle
    act(() => {
      screen.getByTestId('toggle-mode').click();
    });

    expect(screen.getByTestId('input-mode')).toHaveTextContent('voice');
  });

  it('should handle multiple message additions', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Add multiple messages
    act(() => {
      screen.getByTestId('add-message').click();
      screen.getByTestId('add-message').click();
      screen.getByTestId('add-message').click();
    });

    expect(screen.getByTestId('message-count')).toHaveTextContent('3');
  });

  it('should maintain state consistency across re-renders', async () => {
    const { rerender } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Add a message
    act(() => {
      screen.getByTestId('add-message').click();
    });

    expect(screen.getByTestId('message-count')).toHaveTextContent('1');

    // Re-render the component
    rerender(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // State should be maintained
    expect(screen.getByTestId('message-count')).toHaveTextContent('1');
  });
});