import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import React from 'react';
import { useConversation } from '../hooks/useConversation';
import { usePersonality } from '../hooks/usePersonality';
import { ConversationProvider } from '../context/ConversationContext';
import { PersonalityProvider } from '../context/PersonalityContext';
import type { Message } from '../types/message';

// Mock the personality utility
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn().mockResolvedValue({
    name: '<PERSON>',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['coding', 'music'],
      style: {
        speech: 'casual',
        humor: 'witty',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: ['politics'],
        safe_topics: ['technology', 'hobbies']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How are you?']
    },
    version: '1.0.0'
  }),
  savePersonality: vi.fn().mockResolvedValue(undefined)
}));

describe('useConversation', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    React.createElement(ConversationProvider, {}, children)
  );

  it('should provide conversation state and actions', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    expect(result.current.messages).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.inputMode).toBe('text');
    expect(result.current.visualMode).toBe('visual');
    expect(result.current.isListening).toBe(false);
    expect(result.current.isPlaying).toBe(false);
    expect(result.current.error).toBeNull();

    // Check action functions exist
    expect(typeof result.current.addMessage).toBe('function');
    expect(typeof result.current.setLoading).toBe('function');
    expect(typeof result.current.setInputMode).toBe('function');
    expect(typeof result.current.setVisualMode).toBe('function');
    expect(typeof result.current.setListening).toBe('function');
    expect(typeof result.current.setPlaying).toBe('function');
    expect(typeof result.current.setError).toBe('function');
    expect(typeof result.current.clearMessages).toBe('function');
  });

  it('should add messages correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    const testMessage: Message = {
      id: 'test-1',
      content: 'Hello',
      timestamp: new Date(),
      sender: 'user',
    };

    act(() => {
      result.current.addMessage(testMessage);
    });

    expect(result.current.messages).toHaveLength(1);
    expect(result.current.messages[0]).toEqual(testMessage);
  });

  it('should create user messages correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    act(() => {
      result.current.addUserMessage('Hello Nathan!');
    });

    expect(result.current.messages).toHaveLength(1);
    expect(result.current.messages[0].content).toBe('Hello Nathan!');
    expect(result.current.messages[0].sender).toBe('user');
    expect(result.current.messages[0].id).toMatch(/^user-/);
  });

  it('should create Nathan messages correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    act(() => {
      result.current.addNathanMessage('Hello there!', 'happy', 'audio-url');
    });

    expect(result.current.messages).toHaveLength(1);
    expect(result.current.messages[0].content).toBe('Hello there!');
    expect(result.current.messages[0].sender).toBe('nathan');
    expect(result.current.messages[0].emotion).toBe('happy');
    expect(result.current.messages[0].audioUrl).toBe('audio-url');
    expect(result.current.messages[0].id).toMatch(/^nathan-/);
  });

  it('should toggle input mode correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    expect(result.current.inputMode).toBe('text');

    act(() => {
      result.current.toggleInputMode();
    });

    expect(result.current.inputMode).toBe('voice');

    act(() => {
      result.current.toggleInputMode();
    });

    expect(result.current.inputMode).toBe('text');
  });

  it('should toggle visual mode correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    expect(result.current.visualMode).toBe('visual');

    act(() => {
      result.current.toggleVisualMode();
    });

    expect(result.current.visualMode).toBe('minimal');

    act(() => {
      result.current.toggleVisualMode();
    });

    expect(result.current.visualMode).toBe('visual');
  });

  it('should get last message correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    expect(result.current.getLastMessage()).toBeNull();

    act(() => {
      result.current.addUserMessage('First message');
      result.current.addNathanMessage('Second message');
    });

    const lastMessage = result.current.getLastMessage();
    expect(lastMessage?.content).toBe('Second message');
    expect(lastMessage?.sender).toBe('nathan');
  });

  it('should filter messages by sender correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    act(() => {
      result.current.addUserMessage('User message 1');
      result.current.addNathanMessage('Nathan message 1');
      result.current.addUserMessage('User message 2');
    });

    const userMessages = result.current.getMessagesBySender('user');
    const nathanMessages = result.current.getMessagesBySender('nathan');

    expect(userMessages).toHaveLength(2);
    expect(nathanMessages).toHaveLength(1);
    expect(userMessages[0].content).toBe('User message 1');
    expect(nathanMessages[0].content).toBe('Nathan message 1');
  });

  it('should clear messages correctly', () => {
    const { result } = renderHook(() => useConversation(), { wrapper });

    act(() => {
      result.current.addUserMessage('Test message');
    });

    expect(result.current.messages).toHaveLength(1);

    act(() => {
      result.current.clearMessages();
    });

    expect(result.current.messages).toHaveLength(0);
  });
});

describe('usePersonality', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    React.createElement(PersonalityProvider, {}, children)
  );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should provide personality state and actions', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.personality).toBeDefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.isPersonalityReady).toBe(true);

    // Check action functions exist
    expect(typeof result.current.updatePersonality).toBe('function');
    expect(typeof result.current.resetPersonality).toBe('function');
    expect(typeof result.current.updatePersonalityTraits).toBe('function');
    expect(typeof result.current.addHobby).toBe('function');
    expect(typeof result.current.removeHobby).toBe('function');
  });

  it('should generate personality prompt correctly', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const prompt = result.current.getPersonalityPrompt();
    
    expect(prompt).toContain('Nathan');
    expect(prompt).toContain('he/him');
    expect(prompt).toContain('friendly');
    expect(prompt).toContain('AI companion');
    expect(prompt).toContain('coding, music');
  });

  it('should add hobby correctly', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      await result.current.addHobby('reading');
    });

    expect(result.current.personality?.personality.hobbies).toContain('reading');
  });

  it('should not add duplicate hobby', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const initialHobbiesLength = result.current.personality?.personality.hobbies.length || 0;

    await act(async () => {
      await result.current.addHobby('coding'); // Already exists
    });

    expect(result.current.personality?.personality.hobbies).toHaveLength(initialHobbiesLength);
  });

  it('should remove hobby correctly', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      await result.current.removeHobby('coding');
    });

    expect(result.current.personality?.personality.hobbies).not.toContain('coding');
  });

  it('should add topic to avoid correctly', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      await result.current.addTopicToAvoid('religion');
    });

    expect(result.current.personality?.personality.boundaries.avoid).toContain('religion');
  });

  it('should add safe topic correctly', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      await result.current.addSafeTopic('science');
    });

    expect(result.current.personality?.personality.boundaries.safe_topics).toContain('science');
  });

  it('should update dynamic traits correctly', async () => {
    const { result } = renderHook(() => usePersonality(), { wrapper });

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      await result.current.updateDynamicTraits({
        adaptive_empathy: false,
      });
    });

    expect(result.current.personality?.personality.dynamic_traits.adaptive_empathy).toBe(false);
  });
});