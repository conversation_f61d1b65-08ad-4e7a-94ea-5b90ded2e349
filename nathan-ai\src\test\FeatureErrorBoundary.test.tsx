import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { FeatureErrorBoundary } from '../components/FeatureErrorBoundary';

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Feature error');
  }
  return <div>Feature working</div>;
};

// Mock window.location.reload
const reloadMock = vi.fn();
Object.defineProperty(window, 'location', {
  value: { reload: reloadMock },
  writable: true,
});

describe('FeatureErrorBoundary Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('renders children when there is no error', () => {
    render(
      <FeatureErrorBoundary featureName="Voice Input">
        <ThrowError shouldThrow={false} />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText('Feature working')).toBeInTheDocument();
  });

  it('renders feature-specific error UI when child component throws', () => {
    render(
      <FeatureErrorBoundary featureName="Voice Input">
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText('Voice Input Temporarily Unavailable')).toBeInTheDocument();
    expect(screen.getByText(/feature encountered an error/)).toBeInTheDocument();
  });

  it('uses custom fallback component when provided', () => {
    const customFallback = <div>Custom feature error message</div>;

    render(
      <FeatureErrorBoundary featureName="Voice Input" fallbackComponent={customFallback}>
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText('Custom feature error message')).toBeInTheDocument();
    expect(screen.queryByText('Voice Input Temporarily Unavailable')).not.toBeInTheDocument();
  });

  it('calls custom error handler when provided', () => {
    const onErrorMock = vi.fn();

    render(
      <FeatureErrorBoundary featureName="Voice Input" onError={onErrorMock}>
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    expect(onErrorMock).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    );
  });

  it('refreshes page when refresh button is clicked', () => {
    render(
      <FeatureErrorBoundary featureName="Voice Input">
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    fireEvent.click(screen.getByText('Refresh to Try Again'));
    expect(reloadMock).toHaveBeenCalled();
  });

  it('logs feature-specific error information', () => {
    const consoleSpy = vi.spyOn(console, 'error');

    render(
      <FeatureErrorBoundary featureName="Voice Input">
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      'Feature Error in Voice Input:',
      expect.objectContaining({
        feature: 'Voice Input',
        error: 'Feature error',
      })
    );
  });

  it('applies correct styling to default fallback UI', () => {
    render(
      <FeatureErrorBoundary featureName="Voice Input">
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    const errorContainer = screen.getByText('Voice Input Temporarily Unavailable').closest('div');
    expect(errorContainer).toHaveStyle({
      backgroundColor: '#f8f9fa',
      border: '1px solid #dee2e6',
    });
  });

  it('handles feature names with special characters', () => {
    render(
      <FeatureErrorBoundary featureName="AI Chat & Voice">
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText('AI Chat & Voice Temporarily Unavailable')).toBeInTheDocument();
  });

  it('provides appropriate error message for users', () => {
    render(
      <FeatureErrorBoundary featureName="Text-to-Speech">
        <ThrowError shouldThrow={true} />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText(/temporarily disabled/)).toBeInTheDocument();
    expect(screen.getByText('Refresh to Try Again')).toBeInTheDocument();
  });
});