# Design Document

## Overview

Nathan is a React-based conversational AI system that provides emotionally intelligent interactions through multiple input/output modes. The system integrates with Hugging Face for natural language processing and Eleven Labs for text-to-speech synthesis. The architecture follows a modular component-based design with clear separation between UI, state management, API integration, and personality configuration.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[React UI Components] --> SM[State Management]
    SM --> API[API Integration Layer]
    API --> HF[Hugging Face API]
    API --> EL[Eleven Labs API]
    SM --> PS[Personality System]
    PS --> PJ[personality.json]
    UI --> WSA[Web Speech API]
    UI --> AA[Audio API]
```

### Technology Stack

- **Frontend Framework**: React 18+ with TypeScript
- **State Management**: React Context API with useReducer
- **Styling**: CSS Modules or Styled Components for component isolation
- **Build Tool**: Vite for fast development and optimized builds
- **Voice Recognition**: Web Speech API (SpeechRecognition)
- **Audio Playback**: Web Audio API
- **HTTP Client**: Axios for API requests
- **Environment Management**: dotenv for secure API key handling

## Components and Interfaces

### Core Components

#### 1. App Component
- **Purpose**: Root component managing global state and routing
- **Responsibilities**: 
  - Initialize personality system
  - Provide global context
  - Handle error boundaries
  - Manage theme and mode switching

#### 2. ChatInterface Component
- **Purpose**: Main conversation interface
- **Props**: 
  ```typescript
  interface ChatInterfaceProps {
    mode: 'visual' | 'minimal';
    personality: PersonalityConfig;
  }
  ```
- **Responsibilities**:
  - Render message history
  - Handle input mode switching
  - Manage conversation state

#### 3. InputController Component
- **Purpose**: Manages voice and text input modes
- **Props**:
  ```typescript
  interface InputControllerProps {
    inputMode: 'voice' | 'text';
    onModeChange: (mode: 'voice' | 'text') => void;
    onMessageSend: (message: string) => void;
    isListening: boolean;
  }
  ```
- **Responsibilities**:
  - Voice recognition control
  - Text input handling
  - Mode switching UI

#### 4. VoiceInput Component
- **Purpose**: Handles speech recognition and microphone control
- **State**:
  ```typescript
  interface VoiceInputState {
    isListening: boolean;
    transcript: string;
    error: string | null;
    isSupported: boolean;
  }
  ```

#### 5. TextInput Component
- **Purpose**: Traditional text input with send functionality
- **Features**:
  - Auto-resize textarea
  - Enter key handling
  - Character count
  - Send button state management

#### 6. MessageBubble Component
- **Purpose**: Individual message display
- **Props**:
  ```typescript
  interface MessageBubbleProps {
    message: Message;
    isUser: boolean;
    showAvatar: boolean;
    personality: PersonalityConfig;
  }
  ```

#### 7. Avatar Component
- **Purpose**: Dynamic avatar display for visual mode
- **Props**:
  ```typescript
  interface AvatarProps {
    emotion: 'neutral' | 'happy' | 'thoughtful' | 'empathetic';
    isAnimated: boolean;
    size: 'small' | 'medium' | 'large';
  }
  ```

#### 8. AudioPlayer Component
- **Purpose**: Manages TTS audio playback
- **Responsibilities**:
  - Queue audio responses
  - Control playback state
  - Handle audio interruption

### Data Models

#### Message Interface
```typescript
interface Message {
  id: string;
  content: string;
  timestamp: Date;
  sender: 'user' | 'nathan';
  audioUrl?: string;
  emotion?: EmotionType;
}
```

#### PersonalityConfig Interface
```typescript
interface PersonalityConfig {
  name: string;
  pronouns: string;
  personality: {
    tone: string;
    role: string;
    hobbies: string[];
    style: {
      speech: string;
      humor: string;
      depth: string;
    };
    boundaries: {
      avoid: string[];
      safe_topics: string[];
    };
    dynamic_traits: {
      adaptive_empathy: boolean;
      mirroring_style: boolean;
      emotionally_available: boolean;
    };
  };
  conversation_tips: {
    starter_prompts: string[];
  };
  version: string;
}
```

#### ConversationState Interface
```typescript
interface ConversationState {
  messages: Message[];
  isLoading: boolean;
  inputMode: 'voice' | 'text';
  visualMode: 'visual' | 'minimal';
  isListening: boolean;
  isPlaying: boolean;
  error: string | null;
}
```

### API Integration Layer

#### AIService Class
```typescript
class AIService {
  private huggingFaceClient: HuggingFaceClient;
  private elevenLabsClient: ElevenLabsClient;
  
  async generateResponse(message: string, personality: PersonalityConfig): Promise<string>;
  async synthesizeSpeech(text: string): Promise<AudioBuffer>;
  private formatPersonalityPrompt(personality: PersonalityConfig): string;
}
```

#### HuggingFaceClient Class
```typescript
class HuggingFaceClient {
  private apiKey: string;
  private baseUrl: string;
  
  async generateText(prompt: string, options?: GenerationOptions): Promise<string>;
  private handleApiError(error: any): never;
}
```

#### ElevenLabsClient Class
```typescript
class ElevenLabsClient {
  private apiKey: string;
  private voiceId: string;
  
  async textToSpeech(text: string, options?: TTSOptions): Promise<ArrayBuffer>;
  private optimizeForStreaming(text: string): string[];
}
```

### State Management

#### Context Structure
```typescript
interface AppContextType {
  state: ConversationState;
  dispatch: React.Dispatch<ConversationAction>;
  personality: PersonalityConfig;
  updatePersonality: (config: Partial<PersonalityConfig>) => void;
}
```

#### Action Types
```typescript
type ConversationAction =
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_INPUT_MODE'; payload: 'voice' | 'text' }
  | { type: 'SET_VISUAL_MODE'; payload: 'visual' | 'minimal' }
  | { type: 'SET_LISTENING'; payload: boolean }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_MESSAGES' };
```

## Error Handling

### Error Categories

1. **API Errors**
   - Hugging Face API failures
   - Eleven Labs API failures
   - Network connectivity issues
   - Rate limiting

2. **Browser Compatibility**
   - Web Speech API not supported
   - Audio API limitations
   - Mobile browser restrictions

3. **User Input Errors**
   - Microphone access denied
   - Empty message validation
   - Audio playback failures

### Error Handling Strategy

#### Global Error Boundary
```typescript
class ErrorBoundary extends React.Component {
  // Catches React component errors
  // Provides fallback UI
  // Logs errors for debugging
}
```

#### API Error Handling
```typescript
interface ApiErrorHandler {
  handleHuggingFaceError(error: HFError): void;
  handleElevenLabsError(error: ELError): void;
  handleNetworkError(error: NetworkError): void;
  showUserFriendlyMessage(errorType: ErrorType): void;
}
```

#### Graceful Degradation
- Voice input fails → Auto-switch to text input
- TTS fails → Show text response with notification
- API unavailable → Show offline message with retry option
- Microphone denied → Hide voice controls, show text input

## Testing Strategy

### Unit Testing
- **Components**: React Testing Library for component behavior
- **Services**: Jest for API service logic
- **Utilities**: Jest for helper functions and personality parsing
- **Hooks**: React Hooks Testing Library for custom hooks

### Integration Testing
- **API Integration**: Mock API responses for different scenarios
- **Voice Recognition**: Mock Web Speech API for consistent testing
- **Audio Playback**: Mock Audio API for playback testing
- **State Management**: Test context and reducer interactions

### End-to-End Testing
- **Conversation Flow**: Complete user interaction scenarios
- **Mode Switching**: Voice ↔ text ↔ visual mode transitions
- **Error Recovery**: API failure and recovery scenarios
- **Mobile Responsiveness**: Touch interactions and responsive design

### Performance Testing
- **Response Times**: Measure API response and TTS generation times
- **Memory Usage**: Monitor memory consumption during long conversations
- **Audio Quality**: Test TTS output quality and playback performance
- **Bundle Size**: Optimize JavaScript bundle size for fast loading

### Accessibility Testing
- **Screen Reader**: Ensure compatibility with screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **Voice Commands**: Alternative input methods for accessibility
- **Visual Indicators**: Clear visual feedback for all interactions

## Security Considerations

### API Key Management
- Environment variables for all API keys
- No hardcoded credentials in source code
- Separate development and production configurations
- Key rotation support

### Data Privacy
- No persistent storage of conversation data
- Optional local storage for personality preferences
- Clear data retention policies
- User control over data deletion

### Input Validation
- Sanitize all user inputs before API calls
- Validate personality configuration structure
- Prevent XSS through proper escaping
- Rate limiting for API calls

## Performance Optimizations

### Code Splitting
- Lazy load components by mode (visual/minimal)
- Dynamic imports for heavy dependencies
- Route-based code splitting if expanded

### Caching Strategy
- Cache personality configurations
- Memoize expensive computations
- Cache TTS audio for repeated phrases
- Optimize re-renders with React.memo

### Audio Optimization
- Stream TTS audio when possible
- Preload common responses
- Compress audio for faster delivery
- Queue management for multiple audio requests

### Bundle Optimization
- Tree shaking for unused code
- Minimize third-party dependencies
- Optimize images and assets
- Use production builds for deployment