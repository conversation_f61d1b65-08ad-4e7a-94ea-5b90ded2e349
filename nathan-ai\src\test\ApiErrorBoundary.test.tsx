import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ApiErrorBoundary } from '../components/ApiErrorBoundary';

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('API connection failed');
  }
  return <div>API working</div>;
};

// Mock window.location.reload
const reloadMock = vi.fn();
Object.defineProperty(window, 'location', {
  value: { reload: reloadMock },
  writable: true,
});

describe('ApiErrorBoundary Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('renders children when there is no error', () => {
    render(
      <ApiErrorBoundary serviceName="Test API">
        <ThrowError shouldThrow={false} />
      </ApiErrorBoundary>
    );

    expect(screen.getByText('API working')).toBeInTheDocument();
  });

  it('renders API-specific error UI when child component throws', () => {
    render(
      <ApiErrorBoundary serviceName="Hugging Face API">
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    expect(screen.getByText('Hugging Face API Unavailable')).toBeInTheDocument();
    expect(screen.getByText(/having trouble connecting to Hugging Face API/)).toBeInTheDocument();
  });

  it('shows retry button when onRetry is provided', () => {
    const onRetryMock = vi.fn();

    render(
      <ApiErrorBoundary serviceName="Test API" onRetry={onRetryMock}>
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    const retryButton = screen.getByText('Retry Connection');
    expect(retryButton).toBeInTheDocument();

    fireEvent.click(retryButton);
    expect(onRetryMock).toHaveBeenCalled();
  });

  it('does not show retry button when onRetry is not provided', () => {
    render(
      <ApiErrorBoundary serviceName="Test API">
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    expect(screen.queryByText('Retry Connection')).not.toBeInTheDocument();
    expect(screen.getByText('Refresh Page')).toBeInTheDocument();
  });

  it('refreshes page when refresh button is clicked', () => {
    render(
      <ApiErrorBoundary serviceName="Test API">
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    fireEvent.click(screen.getByText('Refresh Page'));
    expect(reloadMock).toHaveBeenCalled();
  });

  it('logs API-specific error information', () => {
    const consoleSpy = vi.spyOn(console, 'error');

    render(
      <ApiErrorBoundary serviceName="Test API">
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      'API Error in Test API:',
      expect.objectContaining({
        service: 'Test API',
        error: 'API connection failed',
      })
    );
  });

  it('uses default service name when not provided', () => {
    render(
      <ApiErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    expect(screen.getByText('API service Unavailable')).toBeInTheDocument();
  });

  it('applies correct styling to error UI', () => {
    render(
      <ApiErrorBoundary serviceName="Test API">
        <ThrowError shouldThrow={true} />
      </ApiErrorBoundary>
    );

    const errorContainer = screen.getByText('Test API Unavailable').closest('div');
    expect(errorContainer).toHaveStyle({
      backgroundColor: '#fff3cd',
      border: '1px solid #ffeaa7',
    });
  });
});