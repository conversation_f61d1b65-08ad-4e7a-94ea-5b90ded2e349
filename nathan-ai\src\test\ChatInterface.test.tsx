import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ChatInterface } from '../components/ChatInterface';
import { ConversationProvider } from '../context/ConversationContext';
import { PersonalityProvider } from '../context/PersonalityContext';
import type { PersonalityConfig } from '../types/personality';
import type { Message } from '../types/message';

// Mock environment variables
vi.mock('../utils/env', () => ({
  validateEnvironment: () => ({ isValid: true, errors: [] }),
  getEnvVar: (key: string, defaultValue?: string) => defaultValue || 'mock-value'
}));

// Mock scrollIntoView
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  value: vi.fn(),
  writable: true,
});

// Mock the hooks
const mockUseConversation = {
  messages: [] as Message[],
  isLoading: false,
  visualMode: 'visual' as const,
  setVisualMode: vi.fn(),
  getLastMessage: vi.fn(() => null),
};

const mockUsePersonality = {
  personality: {
    name: 'Nathan',
    pronouns: 'he/him',
    personality: {
      role: 'AI Assistant',
    },
    conversation_tips: {
      starter_prompts: ['Hello! How can I help you today?'],
    },
  } as PersonalityConfig,
};

vi.mock('../hooks/useConversation', () => ({
  useConversation: () => mockUseConversation,
}));

vi.mock('../hooks/usePersonality', () => ({
  usePersonality: () => mockUsePersonality,
}));

vi.mock('../hooks/useAudioConversation', () => ({
  useAudioConversation: () => ({
    ...mockUseConversation,
    audioQueue: [],
    isGeneratingAudio: false,
    handleAudioPlayingChange: vi.fn(),
    handleAudioComplete: vi.fn(),
    handleAudioError: vi.fn(),
  }),
}));

// Mock components
vi.mock('../components/MessageBubble', () => ({
  MessageBubble: ({ message }: { message: Message }) => (
    <div data-testid="message-bubble">
      {message.content} - {message.sender}
    </div>
  ),
}));

vi.mock('../components/Avatar', () => ({
  Avatar: ({ emotion, size, isAnimated }: { emotion: string; size: string; isAnimated: boolean }) => (
    <div data-testid="avatar" data-emotion={emotion} data-size={size} data-animated={isAnimated}>
      Avatar
    </div>
  ),
}));

vi.mock('../components/AudioPlayer', () => ({
  AudioPlayer: () => <div data-testid="audio-player">Audio Player</div>,
}));

const defaultPersonality: PersonalityConfig = {
  name: 'Nathan',
  pronouns: 'he/him',
  personality: {
    tone: 'friendly',
    role: 'AI Assistant',
    hobbies: ['coding', 'learning'],
    style: {
      speech: 'casual',
      humor: 'light',
      depth: 'thoughtful',
    },
    boundaries: {
      avoid: ['harmful content'],
      safe_topics: ['technology', 'learning'],
    },
    dynamic_traits: {
      adaptive_empathy: true,
      mirroring_style: true,
      emotionally_available: true,
    },
  },
  conversation_tips: {
    starter_prompts: ['Hello! How can I help you today?'],
  },
  version: '1.0.0',
};

const renderChatInterface = (props = {}) => {
  const defaultProps = {
    mode: 'visual' as const,
    personality: defaultPersonality,
    ...props,
  };

  return render(
    <ConversationProvider>
      <PersonalityProvider>
        <ChatInterface {...defaultProps} />
      </PersonalityProvider>
    </ConversationProvider>
  );
};

describe('ChatInterface', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseConversation.messages = [];
    mockUseConversation.isLoading = false;
    mockUseConversation.visualMode = 'visual';
  });

  describe('Rendering', () => {
    it('renders the chat interface with header', () => {
      renderChatInterface();
      
      expect(screen.getByText('Nathan')).toBeInTheDocument();
      expect(screen.getByText('he/him • AI Assistant')).toBeInTheDocument();
    });

    it('renders avatar in visual mode', () => {
      renderChatInterface({ mode: 'visual' });
      
      const avatars = screen.getAllByTestId('avatar');
      expect(avatars.length).toBeGreaterThan(0);
    });

    it('does not render subtitle in minimal mode', () => {
      mockUseConversation.visualMode = 'minimal';
      renderChatInterface({ mode: 'minimal' });
      
      expect(screen.queryByText('he/him • AI Assistant')).not.toBeInTheDocument();
    });

    it('renders mode toggle button', () => {
      renderChatInterface();
      
      const toggleButton = screen.getByRole('button', { name: /switch to minimal mode/i });
      expect(toggleButton).toBeInTheDocument();
    });

    it('shows correct toggle icon for visual mode', () => {
      renderChatInterface({ mode: 'visual' });
      
      const toggleButton = screen.getByRole('button', { name: /switch to minimal mode/i });
      expect(toggleButton).toHaveTextContent('🔍');
    });

    it('shows correct toggle icon for minimal mode', () => {
      mockUseConversation.visualMode = 'minimal';
      renderChatInterface({ mode: 'minimal' });
      
      const toggleButton = screen.getByRole('button', { name: /switch to visual mode/i });
      expect(toggleButton).toHaveTextContent('🎨');
    });
  });

  describe('Empty State', () => {
    it('renders empty state when no messages', () => {
      renderChatInterface();
      
      expect(screen.getByText('Start a conversation with Nathan')).toBeInTheDocument();
      expect(screen.getByText('Hello! How can I help you today?')).toBeInTheDocument();
    });

    it('renders avatar in empty state for visual mode', () => {
      renderChatInterface({ mode: 'visual' });
      
      const avatars = screen.getAllByTestId('avatar');
      const largeAvatar = avatars.find(avatar => 
        avatar.getAttribute('data-size') === 'large'
      );
      expect(largeAvatar).toBeInTheDocument();
    });

    it('uses fallback text when no starter prompts available', () => {
      const personalityWithoutPrompts = {
        ...defaultPersonality,
        conversation_tips: { starter_prompts: [] },
      };
      
      renderChatInterface({ personality: personalityWithoutPrompts });
      
      expect(screen.getByText('Say hello or ask me anything!')).toBeInTheDocument();
    });
  });

  describe('Messages Display', () => {
    it('renders messages when available', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'Hello',
          timestamp: new Date(),
          sender: 'user',
        },
        {
          id: '2',
          content: 'Hi there!',
          timestamp: new Date(),
          sender: 'nathan',
          emotion: 'happy',
        },
      ];
      
      mockUseConversation.messages = messages;
      renderChatInterface();
      
      expect(screen.getByText('Hello - user')).toBeInTheDocument();
      expect(screen.getByText('Hi there! - nathan')).toBeInTheDocument();
    });

    it('passes correct props to MessageBubble components', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'Test message',
          timestamp: new Date(),
          sender: 'user',
        },
      ];
      
      mockUseConversation.messages = messages;
      renderChatInterface({ mode: 'visual' });
      
      const messageBubble = screen.getByTestId('message-bubble');
      expect(messageBubble).toBeInTheDocument();
    });

    it('shows loading indicator when isLoading is true', () => {
      mockUseConversation.isLoading = true;
      renderChatInterface();
      
      expect(screen.getByText('Nathan is thinking...')).toBeInTheDocument();
    });

    it('renders loading avatar with thoughtful emotion', () => {
      mockUseConversation.isLoading = true;
      renderChatInterface();
      
      const avatars = screen.getAllByTestId('avatar');
      const loadingAvatar = avatars.find(avatar => 
        avatar.getAttribute('data-emotion') === 'thoughtful' &&
        avatar.getAttribute('data-size') === 'small'
      );
      expect(loadingAvatar).toBeInTheDocument();
    });
  });

  describe('Mode Switching', () => {
    it('calls setVisualMode when mode toggle is clicked', () => {
      renderChatInterface();
      
      const toggleButton = screen.getByRole('button', { name: /switch to minimal mode/i });
      fireEvent.click(toggleButton);
      
      expect(mockUseConversation.setVisualMode).toHaveBeenCalledWith('minimal');
    });

    it('syncs mode prop with internal state', () => {
      renderChatInterface({ mode: 'minimal' });
      
      expect(mockUseConversation.setVisualMode).toHaveBeenCalledWith('minimal');
    });

    it('applies correct CSS class for visual mode', () => {
      const { container } = renderChatInterface({ mode: 'visual' });
      
      const chatInterface = container.firstChild as HTMLElement;
      expect(chatInterface.className).toContain('visual');
    });

    it('applies correct CSS class for minimal mode', () => {
      mockUseConversation.visualMode = 'minimal';
      const { container } = renderChatInterface({ mode: 'minimal' });
      
      const chatInterface = container.firstChild as HTMLElement;
      expect(chatInterface.className).toContain('minimal');
    });
  });

  describe('Emotion Detection', () => {
    it('displays avatar with emotion from last Nathan message', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'I am happy!',
          timestamp: new Date(),
          sender: 'nathan',
          emotion: 'happy',
        },
      ];
      
      mockUseConversation.messages = messages;
      mockUseConversation.getLastMessage = vi.fn(() => messages[0]);
      
      renderChatInterface();
      
      const avatars = screen.getAllByTestId('avatar');
      const headerAvatar = avatars.find(avatar => 
        avatar.getAttribute('data-emotion') === 'happy' &&
        avatar.getAttribute('data-size') === 'medium'
      );
      expect(headerAvatar).toBeInTheDocument();
    });

    it('defaults to neutral emotion when no emotion in last message', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'Hello',
          timestamp: new Date(),
          sender: 'user',
        },
      ];
      
      mockUseConversation.messages = messages;
      mockUseConversation.getLastMessage = vi.fn(() => messages[0]);
      
      renderChatInterface();
      
      const avatars = screen.getAllByTestId('avatar');
      const headerAvatar = avatars.find(avatar => 
        avatar.getAttribute('data-size') === 'medium'
      );
      expect(headerAvatar?.getAttribute('data-emotion')).toBe('neutral');
    });
  });

  describe('Footer', () => {
    it('shows message count in minimal mode when messages exist', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'Test',
          timestamp: new Date(),
          sender: 'user',
        },
        {
          id: '2',
          content: 'Response',
          timestamp: new Date(),
          sender: 'nathan',
        },
      ];
      
      mockUseConversation.messages = messages;
      mockUseConversation.visualMode = 'minimal';
      renderChatInterface({ mode: 'minimal' });
      
      expect(screen.getByText('2 messages')).toBeInTheDocument();
    });

    it('shows singular message count correctly', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'Test',
          timestamp: new Date(),
          sender: 'user',
        },
      ];
      
      mockUseConversation.messages = messages;
      mockUseConversation.visualMode = 'minimal';
      renderChatInterface({ mode: 'minimal' });
      
      expect(screen.getByText('1 message')).toBeInTheDocument();
    });

    it('does not show footer in visual mode', () => {
      const messages: Message[] = [
        {
          id: '1',
          content: 'Test',
          timestamp: new Date(),
          sender: 'user',
        },
      ];
      
      mockUseConversation.messages = messages;
      renderChatInterface({ mode: 'visual' });
      
      expect(screen.queryByText('1 message')).not.toBeInTheDocument();
    });

    it('does not show footer when no messages', () => {
      mockUseConversation.visualMode = 'minimal';
      renderChatInterface({ mode: 'minimal' });
      
      expect(screen.queryByText(/message/)).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels on messages container', () => {
      renderChatInterface();
      
      const messagesContainer = screen.getByRole('log');
      expect(messagesContainer).toHaveAttribute('aria-label', 'Conversation messages');
      expect(messagesContainer).toHaveAttribute('aria-live', 'polite');
    });

    it('has proper ARIA label on mode toggle button', () => {
      renderChatInterface();
      
      const toggleButton = screen.getByRole('button', { name: /switch to minimal mode/i });
      expect(toggleButton).toHaveAttribute('title', 'Switch to minimal mode');
    });

    it('updates ARIA label when mode changes', () => {
      mockUseConversation.visualMode = 'minimal';
      renderChatInterface({ mode: 'minimal' });
      
      const toggleButton = screen.getByRole('button', { name: /switch to visual mode/i });
      expect(toggleButton).toHaveAttribute('title', 'Switch to visual mode');
    });
  });

  describe('Scrolling Behavior', () => {
    it('has scroll anchor element for auto-scrolling', () => {
      const { container } = renderChatInterface();
      
      // The scroll anchor is a div at the end of messages
      const scrollAnchor = container.querySelector('[class*="messagesContainer"] > div:last-child');
      expect(scrollAnchor).toBeInTheDocument();
    });

    it('messages container has proper scroll styling', () => {
      const { container } = renderChatInterface();
      
      const messagesContainer = container.querySelector('[class*="messagesContainer"]');
      expect(messagesContainer).toBeInTheDocument();
      expect(messagesContainer?.className).toContain('messagesContainer');
    });
  });

  describe('Responsive Design', () => {
    it('applies base chat interface class', () => {
      const { container } = renderChatInterface();
      
      const chatInterface = container.firstChild as HTMLElement;
      expect(chatInterface.className).toContain('chatInterface');
    });

    it('maintains structure across different modes', () => {
      const { container: visualContainer } = renderChatInterface({ mode: 'visual' });
      const { container: minimalContainer } = renderChatInterface({ mode: 'minimal' });
      
      // Both should have the same basic structure
      expect(visualContainer.querySelector('[class*="header"]')).toBeInTheDocument();
      expect(visualContainer.querySelector('[class*="messagesContainer"]')).toBeInTheDocument();
      expect(minimalContainer.querySelector('[class*="header"]')).toBeInTheDocument();
      expect(minimalContainer.querySelector('[class*="messagesContainer"]')).toBeInTheDocument();
    });
  });
});