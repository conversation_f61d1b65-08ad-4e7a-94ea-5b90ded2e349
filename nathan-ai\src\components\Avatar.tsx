import React, { useMemo } from 'react';
import type { AvatarProps } from '../types/components';
import { memoize } from '../utils/performance';
import styles from './Avatar.module.css';

// Memoized avatar content function
const getAvatarContent = memoize((emotion: string): string => {
  // Simple avatar content based on emotion
  const emotionContent = {
    neutral: '😐',
    happy: '😊',
    thoughtful: '🤔',
    empathetic: '💙'
  };
  
  return emotionContent[emotion as keyof typeof emotionContent] || '😐';
});

// Memoized animation class function
const getAnimationClass = memoize((emotion: string, isAnimated: boolean): string => {
  if (!isAnimated) return '';
  
  const animationClasses = {
    neutral: styles.neutralAnimation,
    happy: styles.happyAnimation,
    thoughtful: styles.thoughtfulAnimation,
    empathetic: styles.empatheticAnimation
  };
  
  return animationClasses[emotion as keyof typeof animationClasses] || '';
});

export const Avatar: React.FC<AvatarProps> = React.memo(({
  emotion,
  isAnimated,
  size
}) => {
  // Memoize expensive computations
  const avatarContent = useMemo(() => 
    getAvatarContent(emotion), 
    [emotion]
  );
  
  const animationClass = useMemo(() => 
    getAnimationClass(emotion, isAnimated), 
    [emotion, isAnimated]
  );
  
  const avatarClasses = useMemo(() => 
    `${styles.avatar} ${styles[size]} ${styles[emotion]} ${animationClass}`.trim(),
    [size, emotion, animationClass]
  );
  
  const ariaLabel = useMemo(() => 
    `Avatar showing ${emotion} emotion`,
    [emotion]
  );

  return (
    <div 
      className={avatarClasses}
      role="img"
      aria-label={ariaLabel}
    >
      <div className={styles.avatarContent}>
        {avatarContent}
      </div>
      
      {/* Animated background elements for enhanced visual feedback */}
      {isAnimated && (
        <div className={styles.animationOverlay}>
          <div className={styles.pulseRing}></div>
          <div className={styles.glowEffect}></div>
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  return (
    prevProps.emotion === nextProps.emotion &&
    prevProps.isAnimated === nextProps.isAnimated &&
    prevProps.size === nextProps.size
  );
});

export default Avatar;