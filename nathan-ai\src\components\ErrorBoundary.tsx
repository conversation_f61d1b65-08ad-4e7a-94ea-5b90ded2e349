import React, { Component, ReactNode } from 'react';
import styles from './ErrorBoundary.module.css';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string | null;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  level?: 'app' | 'component' | 'feature';
  resetKeys?: Array<string | number>;
  resetOnPropsChange?: boolean;
}

type ErrorType = 'api' | 'render' | 'async' | 'unknown';

interface ErrorDetails {
  type: ErrorType;
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: Date;
  userAgent: string;
  url: string;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetKeys, resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    // Reset error state if resetKeys have changed
    if (hasError && resetKeys && prevProps.resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== prevProps.resetKeys![index]
      );
      
      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }

    // Reset on any prop change if enabled
    if (hasError && resetOnPropsChange && prevProps !== this.props) {
      this.resetErrorBoundary();
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const errorDetails = this.createErrorDetails(error, errorInfo);
    
    // Log error details with structured data
    console.error('ErrorBoundary caught an error:', {
      error,
      errorInfo,
      errorDetails,
      level: this.props.level || 'component',
    });
    
    this.setState({
      error,
      errorInfo,
    });

    // Send error to logging service (if available)
    this.logError(errorDetails);

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private createErrorDetails(error: Error, errorInfo: React.ErrorInfo): ErrorDetails {
    const errorType = this.determineErrorType(error);
    
    return {
      type: errorType,
      message: error.message || 'Unknown error occurred',
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
  }

  private determineErrorType(error: Error): ErrorType {
    const message = error.message?.toLowerCase() || '';
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('fetch') || message.includes('network') || message.includes('api')) {
      return 'api';
    }
    
    if (message.includes('promise') || stack.includes('async')) {
      return 'async';
    }
    
    if (stack.includes('render') || message.includes('render')) {
      return 'render';
    }
    
    return 'unknown';
  }

  private logError(errorDetails: ErrorDetails) {
    // In a real application, this would send to a logging service
    // For now, we'll use structured console logging
    console.group(`🚨 Error Boundary - ${errorDetails.type.toUpperCase()}`);
    console.error('Error Details:', errorDetails);
    console.error('Error ID:', this.state.errorId);
    console.error('Boundary Level:', this.props.level || 'component');
    console.groupEnd();
  }

  private resetErrorBoundary = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleReset = () => {
    this.resetErrorBoundary();
  };

  handleRetryWithDelay = () => {
    // Show loading state briefly before retry
    this.resetTimeoutId = window.setTimeout(() => {
      this.resetErrorBoundary();
    }, 1000);
  };

  private renderErrorMessage() {
    const { error } = this.state;
    const errorType = error ? this.determineErrorType(error) : 'unknown';
    
    const messages = {
      api: 'Nathan is having trouble connecting to the AI services. This might be a temporary network issue.',
      render: 'Nathan encountered a display error. The interface had trouble rendering this component.',
      async: 'Nathan encountered an error while processing your request. This might be due to a timing issue.',
      unknown: 'Nathan encountered an unexpected error. This is likely a temporary issue.',
    };

    const suggestions = {
      api: 'Please check your internet connection and try again. If the problem persists, the AI services might be temporarily unavailable.',
      render: 'Try refreshing the page or switching to a different mode. If the problem continues, please report this issue.',
      async: 'Please wait a moment and try again. If this keeps happening, try refreshing the page.',
      unknown: 'Please try again or refresh the page. If the problem persists, contact support.',
    };

    return {
      message: messages[errorType],
      suggestion: suggestions[errorType],
    };
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { message, suggestion } = this.renderErrorMessage();
      const { level = 'component' } = this.props;

      // Default fallback UI
      return (
        <div className={styles.errorBoundary}>
          <div className={styles.content}>
            <div className={styles.icon}>
              {level === 'app' ? '🚨' : '⚠️'}
            </div>
            <h2 className={styles.title}>
              {level === 'app' ? 'Application Error' : 'Something went wrong'}
            </h2>
            <p className={styles.message}>{message}</p>
            <p className={styles.suggestion}>{suggestion}</p>
            
            <details className={styles.details}>
              <summary>Technical Details</summary>
              <div className={styles.errorInfo}>
                <div className={styles.errorMeta}>
                  <span>Error ID: {this.state.errorId}</span>
                  <span>Time: {new Date().toLocaleString()}</span>
                  <span>Level: {level}</span>
                </div>
                <pre className={styles.error}>
                  {this.state.error?.toString()}
                </pre>
                {this.state.errorInfo && (
                  <pre className={styles.stack}>
                    {this.state.errorInfo.componentStack}
                  </pre>
                )}
              </div>
            </details>
            
            <div className={styles.actions}>
              <button 
                onClick={this.handleReset}
                className={`${styles.button} ${styles.primary}`}
              >
                Try Again
              </button>
              <button 
                onClick={this.handleRetryWithDelay}
                className={`${styles.button} ${styles.secondary}`}
              >
                Retry in 1s
              </button>
              {level === 'app' && (
                <button 
                  onClick={() => window.location.reload()}
                  className={`${styles.button} ${styles.tertiary}`}
                >
                  Refresh Page
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}