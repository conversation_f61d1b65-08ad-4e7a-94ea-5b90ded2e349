/**
 * Mobile performance optimization tests
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  getMobileAudioConfig,
  isLowEndDevice,
  createOptimizedAudioContext,
  createOptimizedSpeechRecognition,
  compressAudioForMobile,
  isBatteryOptimizationNeeded,
  getBatteryOptimizedSettings,
  isMemoryPressureHigh,
  cleanupResourcesForMemory,
  startMemoryMonitoring
} from '../utils/mobileOptimizations';

// Mock browser APIs
const mockNavigator = {
  userAgent: '',
  deviceMemory: 4,
  hardwareConcurrency: 4,
  connection: {
    effectiveType: '4g',
    type: 'wifi'
  },
  getBattery: vi.fn()
};

const mockPerformance = {
  memory: {
    usedJSHeapSize: 50000000,
    jsHeapSizeLimit: 100000000
  },
  now: vi.fn(() => Date.now())
};

const mockWindow = {
  AudioContext: vi.fn().mockImplementation(() => ({
    sampleRate: 44100,
    close: vi.fn()
  })),
  webkitAudioContext: vi.fn().mockImplementation(() => ({
    sampleRate: 44100,
    close: vi.fn()
  })),
  SpeechRecognition: vi.fn().mockImplementation(() => ({
    start: vi.fn(),
    stop: vi.fn(),
    continuous: false,
    interimResults: false
  })),
  webkitSpeechRecognition: vi.fn().mockImplementation(() => ({
    start: vi.fn(),
    stop: vi.fn(),
    continuous: false,
    interimResults: false
  }))
};

describe('Mobile Performance Optimizations', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Setup global mocks with proper structure
    Object.defineProperty(globalThis, 'navigator', {
      value: { ...mockNavigator },
      writable: true,
      configurable: true
    });
    
    Object.defineProperty(globalThis, 'performance', {
      value: { ...mockPerformance },
      writable: true,
      configurable: true
    });
    
    Object.defineProperty(globalThis, 'window', {
      value: { ...mockWindow },
      writable: true,
      configurable: true
    });
    
    // Also define AudioContext globally
    Object.defineProperty(globalThis, 'AudioContext', {
      value: mockWindow.AudioContext,
      writable: true,
      configurable: true
    });
    
    Object.defineProperty(globalThis, 'webkitAudioContext', {
      value: mockWindow.webkitAudioContext,
      writable: true,
      configurable: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getMobileAudioConfig', () => {
    it('should return optimized config for mobile devices', () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      
      const config = getMobileAudioConfig();
      
      expect(config.sampleRate).toBe(22050); // Standard mobile sample rate
      expect(config.channels).toBe(1); // Mono for mobile
      expect(config.enableCompression).toBe(true);
      expect(config.enableEchoCancellation).toBe(true);
    });

    it('should return high-quality config for desktop', () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
      
      const config = getMobileAudioConfig();
      
      expect(config.sampleRate).toBe(44100);
      expect(config.channels).toBe(2); // Stereo for desktop
      expect(config.enableCompression).toBe(false);
    });

    it('should return aggressive optimization for low-end devices', () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (Linux; Android 8.0; SM-G950F) AppleWebKit/537.36';
      mockNavigator.deviceMemory = 1; // Low memory device
      
      const config = getMobileAudioConfig();
      
      expect(config.sampleRate).toBe(16000);
      expect(config.bitRate).toBe(64000);
      expect(config.bufferSize).toBe(4096);
    });
  });

  describe('isLowEndDevice', () => {
    it('should detect low-end device by memory', () => {
      mockNavigator.deviceMemory = 1;
      
      expect(isLowEndDevice()).toBe(true);
    });

    it('should detect low-end device by CPU cores', () => {
      mockNavigator.deviceMemory = undefined;
      mockNavigator.hardwareConcurrency = 2;
      
      expect(isLowEndDevice()).toBe(true);
    });

    it('should detect low-end device by connection', () => {
      mockNavigator.deviceMemory = undefined;
      mockNavigator.hardwareConcurrency = 4;
      mockNavigator.connection.effectiveType = '2g';
      
      expect(isLowEndDevice()).toBe(true);
    });

    it('should detect high-end device', () => {
      mockNavigator.deviceMemory = 8;
      mockNavigator.hardwareConcurrency = 8;
      mockNavigator.connection.effectiveType = '4g';
      
      expect(isLowEndDevice()).toBe(false);
    });
  });

  describe('createOptimizedAudioContext', () => {
    it('should create audio context with optimized settings', () => {
      const mockAudioContext = vi.fn();
      mockWindow.AudioContext = mockAudioContext;
      
      createOptimizedAudioContext();
      
      expect(mockAudioContext).toHaveBeenCalledWith({
        sampleRate: expect.any(Number),
        latencyHint: 'interactive'
      });
    });

    it('should fallback to webkit audio context', () => {
      mockWindow.AudioContext = undefined;
      const mockWebkitAudioContext = vi.fn();
      mockWindow.webkitAudioContext = mockWebkitAudioContext;
      
      createOptimizedAudioContext();
      
      expect(mockWebkitAudioContext).toHaveBeenCalled();
    });

    it('should return null if no audio context available', () => {
      mockWindow.AudioContext = undefined;
      mockWindow.webkitAudioContext = undefined;
      
      const result = createOptimizedAudioContext();
      
      expect(result).toBeNull();
    });
  });

  describe('createOptimizedSpeechRecognition', () => {
    it('should create speech recognition with mobile optimizations', () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      const mockSpeechRecognition = vi.fn(() => ({
        continuous: true,
        interimResults: true,
        maxAlternatives: 3,
        lang: ''
      }));
      mockWindow.webkitSpeechRecognition = mockSpeechRecognition;
      
      const recognition = createOptimizedSpeechRecognition();
      
      expect(mockSpeechRecognition).toHaveBeenCalled();
      expect(recognition?.continuous).toBe(false); // Disabled for mobile
      expect(recognition?.interimResults).toBe(false); // Disabled for mobile
      expect(recognition?.maxAlternatives).toBe(1); // Reduced for mobile
    });

    it('should create speech recognition with desktop settings', () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
      const mockSpeechRecognition = vi.fn(() => ({
        continuous: false,
        interimResults: false,
        maxAlternatives: 1,
        lang: ''
      }));
      mockWindow.SpeechRecognition = mockSpeechRecognition;
      
      const recognition = createOptimizedSpeechRecognition();
      
      expect(recognition?.continuous).toBe(true); // Enabled for desktop
      expect(recognition?.interimResults).toBe(true); // Enabled for desktop
      expect(recognition?.maxAlternatives).toBe(3); // More alternatives for desktop
    });
  });

  describe('compressAudioForMobile', () => {
    it('should compress audio when compression is enabled', async () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      const originalBuffer = new ArrayBuffer(1000);
      
      const compressedBuffer = await compressAudioForMobile(originalBuffer);
      
      expect(compressedBuffer.byteLength).toBeLessThan(originalBuffer.byteLength);
    });

    it('should not compress audio when compression is disabled', async () => {
      mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
      const originalBuffer = new ArrayBuffer(1000);
      
      const result = await compressAudioForMobile(originalBuffer);
      
      expect(result).toBe(originalBuffer);
    });
  });

  describe('Battery Optimization', () => {
    it('should detect when battery optimization is needed', async () => {
      const mockBattery = {
        charging: false,
        level: 0.15 // Low battery
      };
      mockNavigator.getBattery.mockResolvedValue(mockBattery);
      
      const needsOptimization = await isBatteryOptimizationNeeded();
      
      expect(needsOptimization).toBe(true);
    });

    it('should not need optimization when battery is charging', async () => {
      const mockBattery = {
        charging: true,
        level: 0.15
      };
      mockNavigator.getBattery.mockResolvedValue(mockBattery);
      
      const needsOptimization = await isBatteryOptimizationNeeded();
      
      expect(needsOptimization).toBe(false);
    });

    it('should return optimized settings when battery is low', async () => {
      const mockBattery = {
        charging: false,
        level: 0.1
      };
      mockNavigator.getBattery.mockResolvedValue(mockBattery);
      
      const settings = await getBatteryOptimizedSettings();
      
      expect(settings.disableAnimations).toBe(true);
      expect(settings.reducedAudioQuality).toBe(true);
      expect(settings.disableBackgroundProcessing).toBe(true);
    });
  });

  describe('Memory Management', () => {
    it('should detect high memory pressure', () => {
      mockPerformance.memory.usedJSHeapSize = 90000000; // 90% usage
      
      const isHigh = isMemoryPressureHigh();
      
      expect(isHigh).toBe(true);
    });

    it('should detect normal memory usage', () => {
      mockPerformance.memory.usedJSHeapSize = 50000000; // 50% usage
      
      const isHigh = isMemoryPressureHigh();
      
      expect(isHigh).toBe(false);
    });

    it('should cleanup resources when memory pressure is high', () => {
      // Simple test to verify the function runs without error
      expect(() => cleanupResourcesForMemory()).not.toThrow();
    });

    it('should start memory monitoring with callback', () => {
      const callback = vi.fn();
      const cleanup = startMemoryMonitoring(callback);
      
      expect(typeof cleanup).toBe('function');
      
      // Cleanup should remove event listeners
      cleanup();
    });
  });

  describe('Performance Metrics', () => {
    it('should measure bundle loading time', () => {
      const startTime = performance.now();
      
      // Simulate component loading
      setTimeout(() => {
        const loadTime = performance.now() - startTime;
        expect(loadTime).toBeGreaterThan(0);
      }, 10);
    });

    it('should track memory usage over time', () => {
      const initialMemory = mockPerformance.memory.usedJSHeapSize;
      
      // Simulate memory usage
      mockPerformance.memory.usedJSHeapSize += 1000000;
      
      const memoryIncrease = mockPerformance.memory.usedJSHeapSize - initialMemory;
      expect(memoryIncrease).toBe(1000000);
    });
  });

  describe('Network Optimization', () => {
    it('should detect slow network conditions', () => {
      mockNavigator.connection.effectiveType = '2g';
      mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      mockNavigator.deviceMemory = 1; // Low memory to trigger aggressive optimization
      
      const config = getMobileAudioConfig();
      
      // Should use aggressive optimization for slow networks and low-end devices
      expect(config.sampleRate).toBe(16000);
      expect(config.enableCompression).toBe(true);
    });

    it('should optimize for fast networks', () => {
      mockNavigator.connection.effectiveType = '4g';
      mockNavigator.connection.type = 'wifi';
      mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      mockNavigator.deviceMemory = 4; // Normal memory
      
      const config = getMobileAudioConfig();
      
      // Should allow higher quality for fast networks
      expect(config.sampleRate).toBe(22050); // Standard mobile optimization
    });
  });
});