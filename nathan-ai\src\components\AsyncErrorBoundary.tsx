import React, { ReactNode, useEffect, useState } from 'react';
import { ErrorBoundary } from './ErrorBoundary';

interface AsyncErrorBoundaryProps {
  children: ReactNode;
  onAsyncError?: (error: Error) => void;
}

interface AsyncErrorBoundaryState {
  asyncError: Error | null;
}

export const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({
  children,
  onAsyncError,
}) => {
  const [asyncError, setAsyncError] = useState<Error | null>(null);

  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));

      console.error('Unhandled promise rejection:', error);
      
      setAsyncError(error);
      
      if (onAsyncError) {
        onAsyncError(error);
      }

      // Prevent the default browser behavior
      event.preventDefault();
    };

    const handleError = (event: ErrorEvent) => {
      const error = event.error || new Error(event.message);
      console.error('Unhandled error:', error);
      
      setAsyncError(error);
      
      if (onAsyncError) {
        onAsyncError(error);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, [onAsyncError]);

  // If we caught an async error, throw it to be caught by ErrorBoundary
  if (asyncError) {
    throw asyncError;
  }

  const handleReset = () => {
    setAsyncError(null);
  };

  return (
    <ErrorBoundary
      level="component"
      resetKeys={[asyncError]}
      onError={(error, errorInfo) => {
        console.error('AsyncErrorBoundary caught error:', {
          error,
          errorInfo,
          isAsync: true,
        });
      }}
    >
      {children}
    </ErrorBoundary>
  );
};