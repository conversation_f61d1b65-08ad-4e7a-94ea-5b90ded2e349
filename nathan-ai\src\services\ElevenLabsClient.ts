import { config } from '../utils/env';
import { 
  checkApiRateLimit, 
  sanitizeInput, 
  validateInput, 
  secureLog, 
  sanitizeError 
} from '../utils/security';
import type { 
  TTSOptions, 
  ElevenLabsRequest, 
  ELError 
} from '../types/api';
import { cacheManager } from '../utils/cache';
import { ApiPerformanceTracker, AudioLatencyTracker } from '../utils/performance';

export class ElevenLabsClient {
  private readonly apiKey: string;
  private readonly baseUrl: string = 'https://api.elevenlabs.io/v1';
  private readonly voiceId: string;
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 1000; // 1 second

  constructor() {
    this.apiKey = config.elevenLabs.apiKey;
    this.voiceId = config.elevenLabs.voiceId;
    
    if (!this.apiKey) {
      throw new Error('Eleven Labs API key is required');
    }
    
    if (!this.voiceId) {
      throw new Error('Eleven Labs voice ID is required');
    }
  }

  /**
   * Convert text to speech using Eleven Labs API
   */
  async textToSpeech(
    text: string, 
    options: TTSOptions = {}
  ): Promise<ArrayBuffer> {
    const voiceSettings = {
      stability: Math.max(0, Math.min(options.voice_settings?.stability || 0.5, 1)),
      similarity_boost: Math.max(0, Math.min(options.voice_settings?.similarity_boost || 0.75, 1)),
      ...options.voice_settings
    };

    // Check audio cache first
    const cachedAudio = cacheManager.audioCache.get(text, voiceSettings);
    if (cachedAudio) {
      secureLog('ElevenLabs cache hit', { textLength: text.length });
      return cachedAudio;
    }

    // Rate limiting check
    if (!checkApiRateLimit('elevenlabs')) {
      throw new ELError('Rate limit exceeded. Please wait before making another request.', 429);
    }

    // Input validation and sanitization
    const validation = validateInput(text);
    if (!validation.isValid) {
      throw new ELError(`Invalid input: ${validation.errors.join(', ')}`, 400);
    }

    const sanitizedText = sanitizeInput(text);
    if (!sanitizedText) {
      throw new ELError('Text cannot be empty after sanitization', 400);
    }

    secureLog('ElevenLabs API request initiated', { 
      textLength: sanitizedText.length,
      voiceId: options.voice_id || this.voiceId
    });

    const requestBody: ElevenLabsRequest = {
      text: this.optimizeTextForTTS(sanitizedText),
      model_id: options.model_id || 'eleven_monolingual_v1',
      voice_settings: voiceSettings
    };

    // Start performance tracking
    const startTime = performance.now();
    const endTracking = ApiPerformanceTracker.startTracking('elevenlabs');

    try {
      const result = await this.makeRequestWithRetry(requestBody, options.voice_id || this.voiceId);
      
      // Track audio latency
      AudioLatencyTracker.trackLatency(startTime);
      
      // Cache the successful response
      cacheManager.audioCache.set(text, voiceSettings, result);
      
      return result;
    } finally {
      endTracking();
    }
  }

  /**
   * Convert text to speech with streaming optimization
   */
  async textToSpeechStream(
    text: string, 
    options: TTSOptions = {}
  ): Promise<ArrayBuffer> {
    // For now, use the same method as regular TTS
    // In the future, this could be optimized for streaming
    return this.textToSpeech(text, options);
  }

  /**
   * Optimize text for better TTS output
   */
  private optimizeTextForTTS(text: string): string {
    // Remove excessive whitespace
    let optimized = text.replace(/\s+/g, ' ').trim();
    
    // Add pauses for better speech flow
    optimized = optimized.replace(/\. /g, '. ');
    optimized = optimized.replace(/\? /g, '? ');
    optimized = optimized.replace(/! /g, '! ');
    
    // Handle common abbreviations
    optimized = optimized.replace(/\bDr\./g, 'Doctor');
    optimized = optimized.replace(/\bMr\./g, 'Mister');
    optimized = optimized.replace(/\bMrs\./g, 'Missus');
    optimized = optimized.replace(/\bMs\./g, 'Miss');
    
    // Ensure text isn't too long (EL has limits)
    if (optimized.length > 5000) {
      optimized = optimized.substring(0, 4997) + '...';
    }
    
    return optimized;
  }

  /**
   * Split long text into chunks for better processing
   */
  private splitTextIntoChunks(text: string, maxLength: number = 1000): string[] {
    if (text.length <= maxLength) {
      return [text];
    }

    const chunks: string[] = [];
    const sentences = text.split(/(?<=[.!?])\s+/);
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length <= maxLength) {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk);
        }
        currentChunk = sentence;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  /**
   * Make API request with retry logic
   */
  private async makeRequestWithRetry(
    requestBody: ElevenLabsRequest,
    voiceId: string,
    attempt: number = 1
  ): Promise<ArrayBuffer> {
    try {
      const response = await fetch(`${this.baseUrl}/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': this.apiKey,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createELError(response.status, errorData);
      }

      const audioBuffer = await response.arrayBuffer();
      
      if (!audioBuffer || audioBuffer.byteLength === 0) {
        throw this.createELError(500, { 
          status: 'error', 
          message: 'Empty audio response' 
        });
      }

      return audioBuffer;

    } catch (error) {
      if (attempt < this.maxRetries && this.isRetryableError(error)) {
        await this.delay(this.retryDelay * attempt);
        return this.makeRequestWithRetry(requestBody, voiceId, attempt + 1);
      }
      
      throw this.handleApiError(error);
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (error instanceof ELError) {
      // Retry on server errors and rate limiting
      return error.status === 503 || error.status === 429 || error.status === 500;
    }
    
    // Retry on network errors
    return error.name === 'TypeError' || error.code === 'NETWORK_ERROR';
  }

  /**
   * Create standardized EL error
   */
  private createELError(status: number, errorData: any): ELError {
    const message = errorData.detail?.message || errorData.message || `HTTP ${status} error`;
    return new ELError(message, status, errorData.detail);
  }

  /**
   * Handle API errors with proper error types
   */
  private handleApiError(error: any): never {
    if (error instanceof ELError) {
      secureLog('ElevenLabs API error', { 
        status: error.status, 
        message: sanitizeError(error) 
      });
      throw error;
    }

    // Handle network errors
    if (error.name === 'TypeError' || !navigator.onLine) {
      const networkError = new ELError('Network connection failed', 0);
      secureLog('ElevenLabs network error', { error: sanitizeError(networkError) });
      throw networkError;
    }

    // Handle unknown errors
    const unknownError = new ELError(sanitizeError(error), 500);
    secureLog('ElevenLabs unknown error', { error: sanitizeError(unknownError) });
    throw unknownError;
  }

  /**
   * Utility delay function for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get available voices (for future use)
   */
  async getVoices(): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/voices`, {
        headers: {
          'xi-api-key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch voices');
      }

      const data = await response.json();
      return data.voices || [];
    } catch (error) {
      console.warn('Failed to fetch voices:', error);
      return [];
    }
  }

  /**
   * Get voice settings for a specific voice (for future use)
   */
  async getVoiceSettings(voiceId?: string): Promise<any> {
    const targetVoiceId = voiceId || this.voiceId;
    
    try {
      const response = await fetch(`${this.baseUrl}/voices/${targetVoiceId}/settings`, {
        headers: {
          'xi-api-key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch voice settings');
      }

      return await response.json();
    } catch (error) {
      console.warn('Failed to fetch voice settings:', error);
      return {
        stability: 0.5,
        similarity_boost: 0.75
      };
    }
  }
}