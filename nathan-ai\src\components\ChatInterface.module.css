/* Chat Interface Container */
.chatInterface {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Visual mode styling */
.visual {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Minimal mode styling */
.minimal {
  background: #ffffff;
  border: 1px solid #e1e5e9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.minimal .header {
  background: #ffffff;
  border-bottom: 1px solid #e1e5e9;
  backdrop-filter: none;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.headerInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
}

.minimal .title {
  font-size: 16px;
  color: #1a1a1a;
}

.subtitle {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.2;
}

.modeToggle {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.modeToggle:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.modeToggle:active {
  transform: scale(0.95);
}

.minimal .modeToggle {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
}

.minimal .modeToggle:hover {
  background: #e9ecef;
}

/* Messages Container */
.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.messagesContainer::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer::-webkit-scrollbar-track {
  background: transparent;
}

.messagesContainer::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Empty State */
.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.emptyStateContent {
  text-align: center;
  max-width: 300px;
}

.emptyStateTitle {
  margin: 16px 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.minimal .emptyStateTitle {
  font-size: 18px;
  color: #1a1a1a;
}

.emptyStateDescription {
  margin: 0;
  font-size: 14px;
  color: #7f8c8d;
  line-height: 1.4;
}

/* Messages List */
.messagesList {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.minimal .messagesList {
  padding: 16px;
  gap: 12px;
}

/* Loading Message */
.loadingMessage {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
}

.loadingBubble {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 18px;
  padding: 12px 16px;
  max-width: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.minimal .loadingBubble {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  box-shadow: none;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.typingIndicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typingIndicator span {
  width: 6px;
  height: 6px;
  background: #3498db;
  border-radius: 50%;
  animation: typingPulse 1.4s ease-in-out infinite both;
}

.typingIndicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typingIndicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.typingIndicator span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loadingText {
  font-size: 12px;
  color: #7f8c8d;
  font-style: italic;
}

/* Footer */
.footer {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}

.messageCount {
  font-size: 12px;
  color: #7f8c8d;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .chatInterface {
    /* Use full viewport height on mobile */
    height: calc(var(--viewport-height, 100vh) - 120px);
    border-radius: 8px;
    margin: 0;
  }
  
  .header {
    padding: 12px 16px;
    /* Safe area support */
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
  
  .title {
    font-size: 16px;
  }
  
  .minimal .title {
    font-size: 14px;
  }
  
  .subtitle {
    font-size: 11px;
  }
  
  .modeToggle {
    min-width: 44px; /* Touch target size */
    height: 44px;
    font-size: 14px;
    /* Touch-friendly interaction */
    -webkit-tap-highlight-color: transparent;
  }
  
  .messagesContainer {
    /* Improve scroll performance on mobile */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  .messagesList {
    padding: 16px;
    gap: 12px;
    /* Safe area support */
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
  
  .minimal .messagesList {
    padding: 12px;
    gap: 8px;
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
  }
  
  .emptyStateContent {
    max-width: 280px;
    padding: 0 16px;
  }
  
  .emptyStateTitle {
    font-size: 18px;
  }
  
  .minimal .emptyStateTitle {
    font-size: 16px;
  }
  
  .emptyStateDescription {
    font-size: 13px;
    line-height: 1.5;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 10px 12px;
    /* Safe area support for small screens */
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
  }
  
  .headerContent {
    gap: 8px;
  }
  
  .title {
    font-size: 14px;
  }
  
  .minimal .title {
    font-size: 13px;
  }
  
  .subtitle {
    font-size: 10px;
  }
  
  .messagesList {
    padding: 12px;
    gap: 10px;
    /* Safe area support for small screens */
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
  }
  
  .minimal .messagesList {
    padding: 8px;
    gap: 6px;
    padding-left: max(8px, env(safe-area-inset-left));
    padding-right: max(8px, env(safe-area-inset-right));
  }
  
  .loadingBubble {
    padding: 10px 12px;
    max-width: calc(100vw - 80px); /* Prevent overflow */
  }
  
  .emptyStateContent {
    max-width: calc(100vw - 48px);
    padding: 0 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .chatInterface {
    background: #1a1a1a;
    color: #ffffff;
  }
  
  .visual {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .minimal {
    background: #1a1a1a;
    border-color: #333333;
  }
  
  .header {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .minimal .header {
    background: #1a1a1a;
    border-bottom-color: #333333;
  }
  
  .title {
    color: #ffffff;
  }
  
  .subtitle {
    color: #bdc3c7;
  }
  
  .modeToggle {
    background: rgba(26, 26, 26, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }
  
  .modeToggle:hover {
    background: rgba(26, 26, 26, 1);
  }
  
  .minimal .modeToggle {
    background: #2c2c2c;
    border-color: #333333;
  }
  
  .minimal .modeToggle:hover {
    background: #3c3c3c;
  }
  
  .emptyStateTitle {
    color: #ffffff;
  }
  
  .emptyStateDescription {
    color: #bdc3c7;
  }
  
  .loadingBubble {
    background: rgba(26, 26, 26, 0.9);
  }
  
  .minimal .loadingBubble {
    background: #2c2c2c;
    border-color: #333333;
  }
  
  .footer {
    background: rgba(26, 26, 26, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .messageCount {
    color: #bdc3c7;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chatInterface,
  .modeToggle,
  .messagesContainer {
    transition: none;
  }
  
  .typingIndicator span {
    animation: none;
  }
  
  .messagesContainer {
    scroll-behavior: auto;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .chatInterface {
    border: 2px solid #000000;
  }
  
  .header {
    border-bottom: 2px solid #000000;
  }
  
  .modeToggle {
    border: 2px solid #000000;
  }
  
  .loadingBubble {
    border: 1px solid #000000;
  }
}

/* Landscape Orientation Optimizations */
@media (orientation: landscape) and (max-height: 500px) {
  .chatInterface {
    height: calc(var(--viewport-height, 100vh) - 60px);
  }
  
  .header {
    padding: 8px 16px;
    min-height: 48px;
  }
  
  .headerContent {
    gap: 12px;
  }
  
  .title {
    font-size: 14px;
  }
  
  .subtitle {
    display: none; /* Hide subtitle in compact landscape */
  }
  
  .modeToggle {
    min-width: 36px;
    height: 36px;
    font-size: 12px;
  }
  
  .messagesList {
    padding: 12px 16px;
    gap: 8px;
  }
  
  .emptyState {
    padding: 20px;
  }
  
  .emptyStateTitle {
    font-size: 16px;
    margin: 8px 0 4px 0;
  }
  
  .emptyStateDescription {
    font-size: 12px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .modeToggle {
    /* Ensure touch targets are large enough */
    min-width: 44px;
    min-height: 44px;
    /* Remove hover effects */
    transition: background-color 0.2s ease;
  }
  
  .modeToggle:hover {
    transform: none;
  }
  
  .modeToggle:active {
    transform: scale(0.95);
    background: rgba(0, 0, 0, 0.1);
  }
  
  /* Improve scrolling on touch devices */
  .messagesContainer {
    scroll-behavior: smooth;
    overscroll-behavior-y: contain;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chatInterface {
    border-width: 0.5px;
  }
  
  .header {
    border-bottom-width: 0.5px;
  }
  
  .minimal .chatInterface {
    border-width: 0.5px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .chatInterface,
  .modeToggle,
  .messagesContainer {
    transition: none;
  }
  
  .typingIndicator span {
    animation: none;
  }
  
  .messagesContainer {
    scroll-behavior: auto;
  }
}

/* Focus Management for Accessibility */
.chatInterface:focus-within .header {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Mobile-specific animations */
@media (max-width: 768px) {
  @keyframes mobileSlideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .messagesList > * {
    animation: mobileSlideIn 0.3s ease-out;
  }
  
  @media (prefers-reduced-motion: reduce) {
    .messagesList > * {
      animation: none;
    }
  }
}