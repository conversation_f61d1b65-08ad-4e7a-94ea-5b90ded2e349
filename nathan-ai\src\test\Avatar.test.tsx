import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Avatar } from '../components/Avatar';
import type { EmotionType } from '../types/message';

describe('Avatar', () => {
  it('renders with neutral emotion by default', () => {
    render(
      <Avatar
        emotion="neutral"
        isAnimated={false}
        size="medium"
      />
    );
    
    const avatar = screen.getByRole('img');
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveAttribute('aria-label', 'Avatar showing neutral emotion');
  });

  it('displays correct emoji for happy emotion', () => {
    render(
      <Avatar
        emotion="happy"
        isAnimated={false}
        size="medium"
      />
    );
    
    expect(screen.getByText('😊')).toBeInTheDocument();
  });

  it('displays correct emoji for thoughtful emotion', () => {
    render(
      <Avatar
        emotion="thoughtful"
        isAnimated={false}
        size="medium"
      />
    );
    
    expect(screen.getByText('🤔')).toBeInTheDocument();
  });

  it('displays correct emoji for empathetic emotion', () => {
    render(
      <Avatar
        emotion="empathetic"
        isAnimated={false}
        size="medium"
      />
    );
    
    expect(screen.getByText('💙')).toBeInTheDocument();
  });

  it('displays correct emoji for neutral emotion', () => {
    render(
      <Avatar
        emotion="neutral"
        isAnimated={false}
        size="medium"
      />
    );
    
    expect(screen.getByText('😐')).toBeInTheDocument();
  });

  it('applies small size class correctly', () => {
    const { container } = render(
      <Avatar
        emotion="neutral"
        isAnimated={false}
        size="small"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('small');
  });

  it('applies medium size class correctly', () => {
    const { container } = render(
      <Avatar
        emotion="neutral"
        isAnimated={false}
        size="medium"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('medium');
  });

  it('applies large size class correctly', () => {
    const { container } = render(
      <Avatar
        emotion="neutral"
        isAnimated={false}
        size="large"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('large');
  });

  it('applies emotion-specific CSS class', () => {
    const { container } = render(
      <Avatar
        emotion="happy"
        isAnimated={false}
        size="medium"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('happy');
  });

  it('includes animation overlay when isAnimated is true', () => {
    const { container } = render(
      <Avatar
        emotion="happy"
        isAnimated={true}
        size="medium"
      />
    );
    
    // Look for the animation overlay by its structure rather than CSS class
    const animationOverlay = container.querySelector('[class*="animationOverlay"]');
    expect(animationOverlay).toBeInTheDocument();
  });

  it('does not include animation overlay when isAnimated is false', () => {
    const { container } = render(
      <Avatar
        emotion="happy"
        isAnimated={false}
        size="medium"
      />
    );
    
    // Look for the animation overlay by its structure rather than CSS class
    const animationOverlay = container.querySelector('[class*="animationOverlay"]');
    expect(animationOverlay).not.toBeInTheDocument();
  });

  it('applies animation class when isAnimated is true', () => {
    const { container } = render(
      <Avatar
        emotion="happy"
        isAnimated={true}
        size="medium"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('happyAnimation');
  });

  it('does not apply animation class when isAnimated is false', () => {
    const { container } = render(
      <Avatar
        emotion="happy"
        isAnimated={false}
        size="medium"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).not.toContain('happyAnimation');
  });

  it('includes pulse ring and glow effect when animated', () => {
    const { container } = render(
      <Avatar
        emotion="empathetic"
        isAnimated={true}
        size="medium"
      />
    );
    
    const pulseRing = container.querySelector('[class*="pulseRing"]');
    const glowEffect = container.querySelector('[class*="glowEffect"]');
    
    expect(pulseRing).toBeInTheDocument();
    expect(glowEffect).toBeInTheDocument();
  });

  it('handles all emotion types correctly', () => {
    const emotions: EmotionType[] = ['neutral', 'happy', 'thoughtful', 'empathetic'];
    
    emotions.forEach(emotion => {
      const { container, unmount } = render(
        <Avatar
          emotion={emotion}
          isAnimated={true}
          size="medium"
        />
      );
      
      const avatar = container.firstChild as HTMLElement;
      expect(avatar.className).toContain(emotion);
      expect(avatar.className).toContain(`${emotion}Animation`);
      
      unmount();
    });
  });

  it('handles all size variants correctly', () => {
    const sizes: Array<'small' | 'medium' | 'large'> = ['small', 'medium', 'large'];
    
    sizes.forEach(size => {
      const { container, unmount } = render(
        <Avatar
          emotion="neutral"
          isAnimated={false}
          size={size}
        />
      );
      
      const avatar = container.firstChild as HTMLElement;
      expect(avatar.className).toContain(size);
      
      unmount();
    });
  });

  it('maintains accessibility attributes', () => {
    render(
      <Avatar
        emotion="thoughtful"
        isAnimated={true}
        size="large"
      />
    );
    
    const avatar = screen.getByRole('img');
    expect(avatar).toHaveAttribute('aria-label', 'Avatar showing thoughtful emotion');
  });

  it('renders avatar content in correct container', () => {
    const { container } = render(
      <Avatar
        emotion="happy"
        isAnimated={false}
        size="medium"
      />
    );
    
    const avatarContent = container.querySelector('[class*="avatarContent"]');
    expect(avatarContent).toBeInTheDocument();
    expect(avatarContent).toContainHTML('😊');
  });

  it('applies base avatar class to all instances', () => {
    const { container } = render(
      <Avatar
        emotion="neutral"
        isAnimated={false}
        size="small"
      />
    );
    
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('avatar');
  });

  it('handles unknown emotion gracefully', () => {
    const { container } = render(
      <Avatar
        emotion={'unknown' as EmotionType}
        isAnimated={false}
        size="medium"
      />
    );
    
    // Should fallback to neutral emoji
    expect(screen.getByText('😐')).toBeInTheDocument();
    
    // Should still apply the unknown emotion class if provided
    const avatar = container.firstChild as HTMLElement;
    expect(avatar.className).toContain('unknown');
  });
});