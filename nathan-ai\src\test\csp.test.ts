import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  generateCSPDirectives,
  generateCSPHeader,
  validateCSPCompliance,
  validateExternalURL,
  getSecurityHeaders,
  applySecurityHeaders,
} from '../utils/csp';

// Mock the config
vi.mock('../utils/env', () => ({
  config: {
    isDevelopment: false,
    isProduction: true,
  },
}));

describe('CSP Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateCSPDirectives', () => {
    it('should generate production CSP directives', () => {
      const directives = generateCSPDirectives();
      
      expect(directives['default-src']).toEqual(["'self'"]);
      expect(directives['script-src']).toEqual(["'self'"]);
      expect(directives['style-src']).toEqual(["'self'", "'unsafe-inline'"]);
      expect(directives['connect-src']).toContain("'self'");
      expect(directives['connect-src']).toContain('https://api-inference.huggingface.co');
      expect(directives['connect-src']).toContain('https://api.elevenlabs.io');
      expect(directives['frame-src']).toEqual(["'none'"]);
      expect(directives['object-src']).toEqual(["'none'"]);
    });

    it('should include development-specific directives in development', () => {
      // Mock development environment
      vi.doMock('../utils/env', () => ({
        config: {
          isDevelopment: true,
          isProduction: false,
        },
      }));

      const { generateCSPDirectives: devGenerateCSPDirectives } = require('../utils/csp');
      const directives = devGenerateCSPDirectives();
      
      expect(directives['script-src']).toContain("'unsafe-inline'");
      expect(directives['script-src']).toContain("'unsafe-eval'");
      expect(directives['connect-src']).toContain('ws:');
      expect(directives['connect-src']).toContain('wss:');
    });
  });

  describe('generateCSPHeader', () => {
    it('should generate valid CSP header string', () => {
      const header = generateCSPHeader();
      
      expect(header).toContain("default-src 'self'");
      expect(header).toContain("script-src 'self'");
      expect(header).toContain("connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io");
      expect(header).toContain("frame-src 'none'");
      expect(header).toContain("object-src 'none'");
    });

    it('should separate directives with semicolons', () => {
      const header = generateCSPHeader();
      const parts = header.split('; ');
      
      expect(parts.length).toBeGreaterThan(5);
      expect(parts[0]).toContain('default-src');
    });
  });

  describe('validateCSPCompliance', () => {
    it('should pass compliant content', () => {
      const content = '<div>Hello world</div>';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(true);
      expect(result.violations).toHaveLength(0);
    });

    it('should detect inline scripts', () => {
      const content = '<script>alert("xss")</script>';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('Inline scripts are not allowed by CSP');
    });

    it('should detect javascript: URLs', () => {
      const content = '<a href="javascript:alert(1)">Click me</a>';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('javascript: URLs are not allowed by CSP');
    });

    it('should detect data: URLs in scripts', () => {
      const content = '<script src="data:text/javascript,alert(1)"></script>';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('data: URLs in script sources are not allowed by CSP');
    });

    it('should detect eval usage', () => {
      const content = 'eval("alert(1)")';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('eval-like functions are not allowed by CSP');
    });

    it('should detect Function constructor', () => {
      const content = 'new Function("alert(1)")';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('eval-like functions are not allowed by CSP');
    });

    it('should detect setTimeout with string', () => {
      const content = 'setTimeout("alert(1)", 1000)';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('eval-like functions are not allowed by CSP');
    });

    it('should detect inline event handlers', () => {
      const content = '<button onclick="alert(1)">Click</button>';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations).toContain('Inline event handlers are not allowed by CSP');
    });

    it('should handle multiple violations', () => {
      const content = '<script>eval("alert(1)")</script><button onclick="test()">Click</button>';
      const result = validateCSPCompliance(content);
      
      expect(result.isCompliant).toBe(false);
      expect(result.violations.length).toBeGreaterThan(1);
    });
  });

  describe('validateExternalURL', () => {
    it('should allow Hugging Face API URLs', () => {
      const result = validateExternalURL('https://api-inference.huggingface.co/models/test');
      expect(result.isAllowed).toBe(true);
    });

    it('should allow Eleven Labs API URLs', () => {
      const result = validateExternalURL('https://api.elevenlabs.io/v1/text-to-speech');
      expect(result.isAllowed).toBe(true);
    });

    it('should reject unauthorized URLs', () => {
      const result = validateExternalURL('https://malicious-site.com/api');
      expect(result.isAllowed).toBe(false);
      expect(result.reason).toContain('not allowed by CSP');
    });

    it('should handle invalid URLs', () => {
      const result = validateExternalURL('not-a-url');
      expect(result.isAllowed).toBe(false);
      expect(result.reason).toBe('Invalid URL format');
    });

    it('should allow localhost in development', () => {
      // Mock development environment
      vi.doMock('../utils/env', () => ({
        config: {
          isDevelopment: true,
          isProduction: false,
        },
      }));

      const { validateExternalURL: devValidateExternalURL } = require('../utils/csp');
      const result = devValidateExternalURL('http://localhost:3000/api');
      expect(result.isAllowed).toBe(true);
    });
  });

  describe('getSecurityHeaders', () => {
    it('should include all required security headers', () => {
      const headers = getSecurityHeaders();
      
      expect(headers).toHaveProperty('Content-Security-Policy');
      expect(headers).toHaveProperty('X-Content-Type-Options', 'nosniff');
      expect(headers).toHaveProperty('X-Frame-Options', 'DENY');
      expect(headers).toHaveProperty('X-XSS-Protection', '1; mode=block');
      expect(headers).toHaveProperty('Referrer-Policy', 'strict-origin-when-cross-origin');
      expect(headers).toHaveProperty('Permissions-Policy');
    });

    it('should include HSTS header in production', () => {
      const headers = getSecurityHeaders();
      expect(headers).toHaveProperty('Strict-Transport-Security');
    });

    it('should not include HSTS header in development', () => {
      // Mock development environment
      vi.doMock('../utils/env', () => ({
        config: {
          isDevelopment: true,
          isProduction: false,
        },
      }));

      const { getSecurityHeaders: devGetSecurityHeaders } = require('../utils/csp');
      const headers = devGetSecurityHeaders();
      expect(headers).not.toHaveProperty('Strict-Transport-Security');
    });

    it('should have proper Permissions-Policy format', () => {
      const headers = getSecurityHeaders();
      const permissionsPolicy = headers['Permissions-Policy'];
      
      expect(permissionsPolicy).toContain('camera=()');
      expect(permissionsPolicy).toContain('microphone=(self)');
      expect(permissionsPolicy).toContain('geolocation=()');
    });
  });

  describe('applySecurityHeaders', () => {
    it('should merge security headers with existing headers', () => {
      const existingHeaders = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token',
      };
      
      const result = applySecurityHeaders(existingHeaders);
      
      expect(result).toHaveProperty('Content-Type', 'application/json');
      expect(result).toHaveProperty('Authorization', 'Bearer token');
      expect(result).toHaveProperty('Content-Security-Policy');
      expect(result).toHaveProperty('X-Frame-Options', 'DENY');
    });

    it('should work with empty headers', () => {
      const result = applySecurityHeaders();
      
      expect(result).toHaveProperty('Content-Security-Policy');
      expect(result).toHaveProperty('X-Frame-Options', 'DENY');
    });

    it('should override existing security headers', () => {
      const existingHeaders = {
        'X-Frame-Options': 'SAMEORIGIN',
      };
      
      const result = applySecurityHeaders(existingHeaders);
      
      expect(result).toHaveProperty('X-Frame-Options', 'DENY');
    });
  });
});