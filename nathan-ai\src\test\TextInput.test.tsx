import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TextInput } from '../components/TextInput';

describe('TextInput', () => {
  const mockOnMessageSend = vi.fn();
  const defaultProps = {
    onMessageSend: mockOnMessageSend,
    isLoading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders with default placeholder', () => {
      render(<TextInput {...defaultProps} />);
      expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
    });

    it('renders with custom placeholder', () => {
      render(<TextInput {...defaultProps} placeholder="Custom placeholder" />);
      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
    });

    it('renders send button', () => {
      render(<TextInput {...defaultProps} />);
      expect(screen.getByRole('button', { name: 'Send message' })).toBeInTheDocument();
    });

    it('renders character count', () => {
      render(<TextInput {...defaultProps} />);
      expect(screen.getByText('0/1000')).toBeInTheDocument();
    });

    it('renders with custom max length', () => {
      render(<TextInput {...defaultProps} maxLength={500} />);
      expect(screen.getByText('0/500')).toBeInTheDocument();
    });
  });

  describe('Text Input Behavior', () => {
    it('updates textarea value when typing', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello world');
      
      expect(textarea).toHaveValue('Hello world');
    });

    it('updates character count when typing', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello');
      
      expect(screen.getByText('5/1000')).toBeInTheDocument();
    });

    it('prevents input beyond max length', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} maxLength={5} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello world');
      
      expect(textarea).toHaveValue('Hello');
      expect(screen.getByText('5/5')).toBeInTheDocument();
    });

    it('shows warning style when approaching max length', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} maxLength={10} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello wor'); // 9 characters (90% of 10)
      
      const charCount = screen.getByText('9/10');
      expect(charCount.className).toContain('warning');
    });
  });

  describe('Send Button Behavior', () => {
    it('send button is disabled when textarea is empty', () => {
      render(<TextInput {...defaultProps} />);
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      expect(sendButton).toBeDisabled();
    });

    it('send button is enabled when textarea has content', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      
      await user.type(textarea, 'Hello');
      expect(sendButton).not.toBeDisabled();
    });

    it('send button is disabled when only whitespace is entered', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      
      await user.type(textarea, '   ');
      expect(sendButton).toBeDisabled();
    });

    it('calls onMessageSend when send button is clicked', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      
      await user.type(textarea, 'Hello world');
      await user.click(sendButton);
      
      expect(mockOnMessageSend).toHaveBeenCalledWith('Hello world');
    });

    it('trims whitespace before sending', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      
      await user.type(textarea, '  Hello world  ');
      await user.click(sendButton);
      
      expect(mockOnMessageSend).toHaveBeenCalledWith('Hello world');
    });

    it('clears textarea after sending', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      
      await user.type(textarea, 'Hello world');
      await user.click(sendButton);
      
      expect(textarea).toHaveValue('');
      expect(screen.getByText('0/1000')).toBeInTheDocument();
    });
  });

  describe('Enter Key Handling', () => {
    it('sends message when Enter is pressed', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello world');
      await user.keyboard('{Enter}');
      
      expect(mockOnMessageSend).toHaveBeenCalledWith('Hello world');
    });

    it('does not send message when Shift+Enter is pressed', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello world');
      await user.keyboard('{Shift>}{Enter}{/Shift}');
      
      expect(mockOnMessageSend).not.toHaveBeenCalled();
      expect(textarea).toHaveValue('Hello world\n');
    });

    it('does not send empty message when Enter is pressed', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.click(textarea);
      await user.keyboard('{Enter}');
      
      expect(mockOnMessageSend).not.toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    it('disables textarea when loading', () => {
      render(<TextInput {...defaultProps} isLoading={true} />);
      const textarea = screen.getByRole('textbox');
      expect(textarea).toBeDisabled();
    });

    it('disables send button when loading', () => {
      render(<TextInput {...defaultProps} isLoading={true} />);
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      expect(sendButton).toBeDisabled();
    });

    it('shows loading spinner when loading', () => {
      render(<TextInput {...defaultProps} isLoading={true} />);
      const spinner = document.querySelector('[class*="loadingSpinner"]');
      expect(spinner).toBeInTheDocument();
    });

    it('does not send message when loading', async () => {
      const user = userEvent.setup();
      const { rerender } = render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello world');
      
      // Re-render with loading state
      rerender(<TextInput {...defaultProps} isLoading={true} />);
      
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      await user.click(sendButton);
      
      expect(mockOnMessageSend).not.toHaveBeenCalled();
    });
  });

  describe('Auto-resize Functionality', () => {
    it('auto-resizes textarea height based on content', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox') as HTMLTextAreaElement;
      
      // Mock scrollHeight to simulate content height change
      Object.defineProperty(textarea, 'scrollHeight', {
        value: 100,
        writable: true
      });
      
      // Add multiple lines of text
      await user.type(textarea, 'Line 1\nLine 2\nLine 3\nLine 4');
      
      // Height should be set to scrollHeight
      expect(textarea.style.height).toBe('100px');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<TextInput {...defaultProps} />);
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      expect(sendButton).toHaveAttribute('aria-label', 'Send message');
    });

    it('textarea is focusable', () => {
      render(<TextInput {...defaultProps} />);
      const textarea = screen.getByRole('textbox');
      textarea.focus();
      expect(textarea).toHaveFocus();
    });

    it('send button is focusable when enabled', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello');
      
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      sendButton.focus();
      expect(sendButton).toHaveFocus();
    });
  });

  describe('Edge Cases', () => {
    it('handles rapid typing without issues', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      
      // Rapidly type multiple characters
      for (let i = 0; i < 10; i++) {
        await user.type(textarea, `${i}`);
      }
      
      expect(textarea).toHaveValue('0123456789');
      expect(screen.getByText('10/1000')).toBeInTheDocument();
    });

    it('handles paste operations correctly', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} maxLength={10} />);
      
      const textarea = screen.getByRole('textbox');
      
      // Simulate pasting text that exceeds max length
      await user.click(textarea);
      await user.paste('This is a very long text that exceeds the limit');
      
      expect(textarea).toHaveValue('This is a '); // Should be truncated to 10 chars
    });

    it('maintains focus after sending message', async () => {
      const user = userEvent.setup();
      render(<TextInput {...defaultProps} />);
      
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Hello');
      
      const sendButton = screen.getByRole('button', { name: 'Send message' });
      await user.click(sendButton);
      
      // Textarea should still be focusable after sending
      textarea.focus();
      expect(textarea).toHaveFocus();
    });
  });
});