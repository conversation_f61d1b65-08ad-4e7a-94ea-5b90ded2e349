import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import App from '../App';
import { AIService } from '../services/AIService';
import { HFError, ELError } from '../types/api';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  }
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock notification system
const mockAddNotification = vi.fn();
Object.defineProperty(window, '__addNotification', {
  value: mockAddNotification,
  writable: true
});

// Mock Web Speech API
const mockSpeechRecognition = {
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: 'en-US',
  onstart: null,
  onend: null,
  onresult: null,
  onerror: null
};

Object.defineProperty(window, 'SpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

// Mock Audio API
const mockAudio = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 10,
  paused: true,
  ended: false,
  volume: 1,
  src: ''
};

Object.defineProperty(window, 'Audio', {
  value: vi.fn(() => mockAudio),
  writable: true
});

describe('Error Recovery Integration Tests', () => {
  let mockAIService: any;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAddNotification.mockClear();
    user = userEvent.setup();
    
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
      healthCheck: vi.fn(),
      getOfflineQueueStatus: vi.fn().mockReturnValue({ count: 0, items: [] }),
      clearOfflineQueue: vi.fn(),
      getErrorHandler: vi.fn().mockReturnValue({
        addToOfflineQueue: vi.fn(),
        clearOfflineQueue: vi.fn()
      })
    };
    
    vi.mocked(AIService).mockImplementation(() => mockAIService);
  });

  afterEach(() => {
    vi.clearAllMocks();
    if ('__addNotification' in window) {
      (window as any).__addNotification = undefined;
    }
  });

  describe('API Error Recovery', () => {
    it('should recover from temporary HuggingFace API failures', async () => {
      // First call fails, second succeeds
      mockAIService.generateResponse
        .mockRejectedValueOnce(new HFError('Service temporarily unavailable', 503))
        .mockResolvedValueOnce({
          text: 'Recovery successful!',
          emotion: 'happy',
          audio: new ArrayBuffer(512)
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Send message that will initially fail
      await user.type(textInput, 'Test recovery');
      await user.click(sendButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/service temporarily unavailable/i)).toBeInTheDocument();
      });

      // Should show retry button
      const retryButton = screen.getByTestId('retry-button');
      expect(retryButton).toBeInTheDocument();

      // Click retry
      await user.click(retryButton);

      // Should succeed on retry
      await waitFor(() => {
        expect(screen.getByText('Recovery successful!')).toBeInTheDocument();
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
    });

    it('should handle authentication errors with clear guidance', async () => {
      mockAIService.generateResponse.mockRejectedValue(
        new HFError('Unauthorized', 401)
      );

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test auth error');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/authentication error/i)).toBeInTheDocument();
        expect(screen.getByText(/check your api key/i)).toBeInTheDocument();
      });

      // Should show settings link
      const settingsLink = screen.getByTestId('settings-link');
      expect(settingsLink).toBeInTheDocument();

      // Should not show retry button for auth errors
      expect(screen.queryByTestId('retry-button')).not.toBeInTheDocument();
    });

    it('should handle rate limiting with backoff strategy', async () => {
      mockAIService.generateResponse
        .mockRejectedValueOnce(new HFError('Rate limited', 429))
        .mockRejectedValueOnce(new HFError('Rate limited', 429))
        .mockResolvedValueOnce({
          text: 'Rate limit recovered',
          emotion: 'neutral',
          audio: new ArrayBuffer(512)
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Rate limit test');
      await user.click(sendButton);

      // Should show rate limit message
      await waitFor(() => {
        expect(screen.getByText(/rate limited/i)).toBeInTheDocument();
      });

      // Should show countdown timer
      expect(screen.getByTestId('retry-countdown')).toBeInTheDocument();

      // Wait for automatic retry
      await waitFor(() => {
        expect(screen.getByText('Rate limit recovered')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Should have retried multiple times
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);
    });

    it('should gracefully degrade when TTS service fails', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Text response without audio',
        emotion: 'neutral',
        audio: null // TTS failed
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'TTS failure test');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Text response without audio')).toBeInTheDocument();
      });

      // Should show TTS unavailable notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Voice Service Unavailable',
          message: expect.stringContaining('Voice synthesis temporarily unavailable')
        })
      );

      // Should offer option to retry audio generation
      const retryAudioButton = screen.getByTestId('retry-audio-button');
      expect(retryAudioButton).toBeInTheDocument();
    });
  });

  describe('Network Connectivity Recovery', () => {
    it('should handle offline/online transitions', async () => {
      // Start online
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Go offline
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      act(() => {
        window.dispatchEvent(new Event('offline'));
      });

      // Should show offline indicator
      await waitFor(() => {
        expect(screen.getByTestId('offline-indicator')).toBeInTheDocument();
      });

      // Try to send message while offline
      await user.type(textInput, 'Offline message');
      await user.click(sendButton);

      // Should queue message
      await waitFor(() => {
        expect(screen.getByText(/message queued/i)).toBeInTheDocument();
      });

      // Come back online
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });

      mockAIService.generateResponse.mockResolvedValue({
        text: 'Online response',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      act(() => {
        window.dispatchEvent(new Event('online'));
      });

      // Should process queued messages
      await waitFor(() => {
        expect(screen.getByText('Online response')).toBeInTheDocument();
      });

      // Offline indicator should disappear
      expect(screen.queryByTestId('offline-indicator')).not.toBeInTheDocument();
    });

    it('should show queue status when offline', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      mockAIService.getOfflineQueueStatus.mockReturnValue({
        count: 2,
        items: [
          { type: 'text', data: { message: 'First queued message' } },
          { type: 'text', data: { message: 'Second queued message' } }
        ]
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should show queue status
      expect(screen.getByTestId('queue-status')).toBeInTheDocument();
      expect(screen.getByText('2 messages queued')).toBeInTheDocument();

      // Should show clear queue option
      const clearQueueButton = screen.getByTestId('clear-queue-button');
      expect(clearQueueButton).toBeInTheDocument();

      await user.click(clearQueueButton);

      expect(mockAIService.clearOfflineQueue).toHaveBeenCalled();
    });

    it('should handle intermittent connectivity', async () => {
      mockAIService.generateResponse
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          text: 'Connection restored',
          emotion: 'happy',
          audio: new ArrayBuffer(512)
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Intermittent connectivity test');
      await user.click(sendButton);

      // Should show network error
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });

      // Should automatically retry
      await waitFor(() => {
        expect(screen.getByText('Connection restored')).toBeInTheDocument();
      }, { timeout: 10000 });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);
    });
  });

  describe('Browser Compatibility Recovery', () => {
    it('should handle Web Speech API unavailability', async () => {
      // Remove Speech API support
      (window as any).SpeechRecognition = undefined;
      (window as any).webkitSpeechRecognition = undefined;

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Voice toggle should be disabled
      const voiceToggle = screen.getByTestId('voice-toggle');
      expect(voiceToggle).toBeDisabled();

      // Should show compatibility warning
      expect(screen.getByText(/voice input not supported/i)).toBeInTheDocument();

      // Should suggest alternative browsers
      expect(screen.getByText(/try chrome or firefox/i)).toBeInTheDocument();
    });

    it('should handle microphone permission denial', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Start listening
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Simulate permission denied
      act(() => {
        const mockError = { error: 'not-allowed' };
        mockSpeechRecognition.onerror?.(mockError);
      });

      // Should show permission error
      await waitFor(() => {
        expect(screen.getByText(/microphone access denied/i)).toBeInTheDocument();
      });

      // Should provide instructions
      expect(screen.getByText(/allow microphone access/i)).toBeInTheDocument();

      // Should automatically switch to text mode
      expect(screen.getByTestId('text-input')).toBeInTheDocument();
    });

    it('should handle audio playback failures', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Response with audio',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      // Mock audio play failure
      mockAudio.play.mockRejectedValue(new Error('Audio playback not supported'));

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Audio test');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Response with audio')).toBeInTheDocument();
      });

      // Should show audio error notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Audio Playback Failed',
          message: expect.stringContaining('Audio playback not supported')
        })
      );

      // Should continue with text-only mode
      expect(screen.getByText('Response with audio')).toBeInTheDocument();
    });
  });

  describe('State Recovery', () => {
    it('should recover from corrupted conversation state', async () => {
      // Mock corrupted localStorage
      const mockGetItem = vi.fn().mockReturnValue('invalid-json');
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: mockGetItem,
          setItem: vi.fn(),
          removeItem: vi.fn(),
          clear: vi.fn()
        },
        writable: true
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should show state recovery notification
      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'info',
          title: 'State Recovered',
          message: expect.stringContaining('conversation state has been reset')
        })
      );

      // Should start with clean state
      expect(screen.queryByText(/previous message/)).not.toBeInTheDocument();
    });

    it('should recover from personality loading failures', async () => {
      // Mock personality loading failure
      vi.doMock('../utils/personality', () => ({
        loadPersonality: vi.fn()
          .mockRejectedValueOnce(new Error('Personality file corrupted'))
          .mockResolvedValueOnce({
            name: 'Nathan',
            pronouns: 'he/him',
            personality: {
              tone: 'friendly',
              role: 'companion',
              hobbies: ['coding'],
              style: {
                speech: 'casual',
                humor: 'light',
                depth: 'thoughtful'
              },
              boundaries: {
                avoid: [],
                safe_topics: []
              },
              dynamic_traits: {
                adaptive_empathy: true,
                mirroring_style: true,
                emotionally_available: true
              }
            },
            conversation_tips: {
              starter_prompts: []
            },
            version: '1.0.0'
          }),
        savePersonality: vi.fn().mockResolvedValue(undefined)
      }));

      render(<App />);

      // Should show personality error
      await waitFor(() => {
        expect(screen.getByText(/failed to load personality/i)).toBeInTheDocument();
      });

      // Should show retry button
      const retryButton = screen.getByTestId('retry-personality-button');
      await user.click(retryButton);

      // Should recover successfully
      await waitFor(() => {
        expect(screen.getByText('Nathan')).toBeInTheDocument();
      });

      expect(mockAddNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'success',
          title: 'Personality Loaded',
          message: expect.stringContaining('personality has been restored')
        })
      );
    });

    it('should handle component error boundaries', async () => {
      // Mock a component that will throw an error
      const ThrowingComponent = () => {
        throw new Error('Component crashed');
      };

      // This would be caught by error boundary in real app
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should show error boundary fallback
      // (This would be implemented in the actual error boundary component)

      consoleSpy.mockRestore();
    });
  });

  describe('User Guidance and Feedback', () => {
    it('should provide clear error messages and recovery steps', async () => {
      mockAIService.generateResponse.mockRejectedValue(
        new Error('Service unavailable')
      );

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Error test');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/service unavailable/i)).toBeInTheDocument();
      });

      // Should show helpful recovery steps
      expect(screen.getByText(/try again in a few moments/i)).toBeInTheDocument();
      expect(screen.getByText(/check your internet connection/i)).toBeInTheDocument();

      // Should show retry button
      expect(screen.getByTestId('retry-button')).toBeInTheDocument();

      // Should show help link
      expect(screen.getByTestId('help-link')).toBeInTheDocument();
    });

    it('should show progress indicators during recovery', async () => {
      let resolvePromise: (value: any) => void;
      const delayedPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });

      mockAIService.generateResponse.mockReturnValue(delayedPromise);

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Loading test');
      await user.click(sendButton);

      // Should show loading indicator
      expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
      expect(screen.getByText(/generating response/i)).toBeInTheDocument();

      // Resolve the promise
      act(() => {
        resolvePromise!({
          text: 'Delayed response',
          emotion: 'neutral',
          audio: new ArrayBuffer(512)
        });
      });

      await waitFor(() => {
        expect(screen.getByText('Delayed response')).toBeInTheDocument();
      });

      // Loading indicator should disappear
      expect(screen.queryByTestId('loading-indicator')).not.toBeInTheDocument();
    });

    it('should provide contextual help based on error type', async () => {
      // Test different error types
      const errorScenarios = [
        {
          error: new HFError('Unauthorized', 401),
          expectedHelp: /check your hugging face api key/i
        },
        {
          error: new ELError('Unauthorized', 401),
          expectedHelp: /check your eleven labs api key/i
        },
        {
          error: new Error('Network error'),
          expectedHelp: /check your internet connection/i
        }
      ];

      for (const scenario of errorScenarios) {
        mockAIService.generateResponse.mockRejectedValueOnce(scenario.error);

        render(<App />);

        await waitFor(() => {
          expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
        });

        const textInput = screen.getByTestId('text-input');
        const sendButton = screen.getByTestId('send-button');

        await user.type(textInput, 'Error test');
        await user.click(sendButton);

        await waitFor(() => {
          expect(screen.getByText(scenario.expectedHelp)).toBeInTheDocument();
        });

        // Clean up for next iteration
        screen.unmount();
        vi.clearAllMocks();
      }
    });
  });
});