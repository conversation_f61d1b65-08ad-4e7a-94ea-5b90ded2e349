import { useState, useEffect, useCallback } from 'react';
import { BrowserCompatibility, BrowserFeatures, BrowserInfo, CompatibilityIssue } from '../services/BrowserCompatibility';

interface UseBrowserCompatibilityReturn {
  browserInfo: BrowserInfo;
  features: BrowserFeatures;
  issues: CompatibilityIssue[];
  isCompatible: boolean;
  compatibilityScore: number;
  isLoading: boolean;
  requestPermissions: () => Promise<{ microphone: boolean; notifications: boolean }>;
  createSpeechRecognition: () => any | null;
  createAudioContext: () => AudioContext | null;
  getRecommendedBrowserMessage: () => string | null;
  getMobileOptimizations: () => string[];
  refreshCompatibility: () => void;
}

export function useBrowserCompatibility(): UseBrowserCompatibilityReturn {
  const [compatibility, setCompatibility] = useState<BrowserCompatibility | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize compatibility checker
  useEffect(() => {
    const initCompatibility = () => {
      try {
        const compat = new BrowserCompatibility();
        setCompatibility(compat);
      } catch (error) {
        console.error('Failed to initialize browser compatibility:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initCompatibility();
  }, []);

  // Request permissions
  const requestPermissions = useCallback(async () => {
    if (!compatibility) {
      return { microphone: false, notifications: false };
    }
    
    try {
      return await compatibility.requestPermissions();
    } catch (error) {
      console.error('Failed to request permissions:', error);
      return { microphone: false, notifications: false };
    }
  }, [compatibility]);

  // Create speech recognition with fallbacks
  const createSpeechRecognition = useCallback(() => {
    if (!compatibility) {
      return null;
    }
    
    try {
      return compatibility.createSpeechRecognition();
    } catch (error) {
      console.error('Failed to create speech recognition:', error);
      return null;
    }
  }, [compatibility]);

  // Create audio context with fallbacks
  const createAudioContext = useCallback(() => {
    if (!compatibility) {
      return null;
    }
    
    try {
      return compatibility.createAudioContext();
    } catch (error) {
      console.error('Failed to create audio context:', error);
      return null;
    }
  }, [compatibility]);

  // Get recommended browser message
  const getRecommendedBrowserMessage = useCallback(() => {
    if (!compatibility) {
      return null;
    }
    
    return compatibility.getRecommendedBrowserMessage();
  }, [compatibility]);

  // Get mobile optimizations
  const getMobileOptimizations = useCallback(() => {
    if (!compatibility) {
      return [];
    }
    
    return compatibility.getMobileOptimizations();
  }, [compatibility]);

  // Refresh compatibility (useful after permission changes)
  const refreshCompatibility = useCallback(() => {
    setIsLoading(true);
    try {
      const compat = new BrowserCompatibility();
      setCompatibility(compat);
    } catch (error) {
      console.error('Failed to refresh browser compatibility:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Return default values while loading
  if (isLoading || !compatibility) {
    return {
      browserInfo: {
        name: 'Unknown',
        version: 'Unknown',
        isMobile: false,
        isIOS: false,
        isAndroid: false,
        isChrome: false,
        isFirefox: false,
        isSafari: false,
        isEdge: false
      },
      features: {
        webSpeechAPI: false,
        audioAPI: false,
        mediaDevices: false,
        notifications: false,
        localStorage: false,
        webAudio: false,
        getUserMedia: false
      },
      issues: [],
      isCompatible: false,
      compatibilityScore: 0,
      isLoading,
      requestPermissions,
      createSpeechRecognition,
      createAudioContext,
      getRecommendedBrowserMessage,
      getMobileOptimizations,
      refreshCompatibility
    };
  }

  return {
    browserInfo: compatibility.getBrowserInfo(),
    features: compatibility.getFeatures(),
    issues: compatibility.getIssues(),
    isCompatible: compatibility.isCompatible(),
    compatibilityScore: compatibility.getCompatibilityScore(),
    isLoading,
    requestPermissions,
    createSpeechRecognition,
    createAudioContext,
    getRecommendedBrowserMessage,
    getMobileOptimizations,
    refreshCompatibility
  };
}