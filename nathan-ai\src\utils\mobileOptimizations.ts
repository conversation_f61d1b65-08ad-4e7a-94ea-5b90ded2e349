/**
 * Mobile-specific optimizations for audio and voice functionality
 */

// Audio optimization settings for mobile
export interface MobileAudioConfig {
  sampleRate: number;
  bitRate: number;
  channels: number;
  bufferSize: number;
  enableCompression: boolean;
  enableEchoCancellation: boolean;
}

// Get optimal audio configuration for mobile device
export const getMobileAudioConfig = (): MobileAudioConfig => {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const isLowEnd = isLowEndDevice();
  
  if (isMobile && isLowEnd) {
    // Aggressive optimization for low-end mobile devices
    return {
      sampleRate: 16000, // Lower sample rate
      bitRate: 64000,    // Lower bit rate
      channels: 1,       // Mono audio
      bufferSize: 4096,  // Larger buffer for stability
      enableCompression: true,
      enableEchoCancellation: true
    };
  } else if (isMobile) {
    // Standard mobile optimization
    return {
      sampleRate: 22050, // Moderate sample rate
      bitRate: 128000,   // Standard bit rate
      channels: 1,       // Mono audio for efficiency
      bufferSize: 2048,  // Moderate buffer size
      enableCompression: true,
      enableEchoCancellation: true
    };
  } else {
    // Desktop configuration
    return {
      sampleRate: 44100, // High quality
      bitRate: 192000,   // High bit rate
      channels: 2,       // Stereo
      bufferSize: 1024,  // Small buffer for low latency
      enableCompression: false,
      enableEchoCancellation: false
    };
  }
};

// Detect low-end device based on available indicators
export const isLowEndDevice = (): boolean => {
  // Check device memory (if available)
  if ('deviceMemory' in navigator) {
    const deviceMemory = (navigator as any).deviceMemory;
    if (deviceMemory && deviceMemory <= 2) {
      return true;
    }
  }
  
  // Check hardware concurrency (CPU cores)
  if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
    return true;
  }
  
  // Check connection type
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    if (connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g')) {
      return true;
    }
  }
  
  // Check user agent for known low-end devices
  const lowEndPatterns = [
    /Android.*(?:Go|Lite)/i,
    /iPhone.*(?:5|5c|5s)/i,
    /iPad.*(?:2|3|4)/i
  ];
  
  return lowEndPatterns.some(pattern => pattern.test(navigator.userAgent));
};

// Optimize audio context for mobile
export const createOptimizedAudioContext = (): AudioContext | null => {
  if (typeof AudioContext === 'undefined' && typeof (window as any).webkitAudioContext === 'undefined') {
    return null;
  }
  
  const AudioContextClass = AudioContext || (window as any).webkitAudioContext;
  const config = getMobileAudioConfig();
  
  try {
    const audioContext = new AudioContextClass({
      sampleRate: config.sampleRate,
      latencyHint: 'interactive'
    });
    
    return audioContext;
  } catch (error) {
    console.error('Failed to create optimized audio context:', error);
    return null;
  }
};

// Optimize speech recognition for mobile
export const createOptimizedSpeechRecognition = (): SpeechRecognition | null => {
  const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
  
  if (!SpeechRecognition) {
    return null;
  }
  
  try {
    const recognition = new SpeechRecognition();
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    // Mobile-specific optimizations
    if (isMobile) {
      recognition.continuous = false; // Disable continuous mode on mobile for battery
      recognition.interimResults = false; // Disable interim results for performance
      recognition.maxAlternatives = 1; // Reduce alternatives for performance
    } else {
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.maxAlternatives = 3;
    }
    
    // Common settings
    recognition.lang = 'en-US';
    
    return recognition;
  } catch (error) {
    console.error('Failed to create optimized speech recognition:', error);
    return null;
  }
};

// Audio compression utility for mobile
export const compressAudioForMobile = async (audioBuffer: ArrayBuffer): Promise<ArrayBuffer> => {
  const config = getMobileAudioConfig();
  
  if (!config.enableCompression) {
    return audioBuffer;
  }
  
  try {
    // Simple compression by reducing sample rate and bit depth
    // In a real implementation, you might use a library like lamejs for MP3 compression
    const view = new DataView(audioBuffer);
    const compressed = new ArrayBuffer(Math.floor(audioBuffer.byteLength * 0.7)); // 30% compression
    const compressedView = new DataView(compressed);
    
    // Simple downsampling (this is a placeholder - real implementation would be more sophisticated)
    for (let i = 0; i < compressed.byteLength; i += 2) {
      const originalIndex = Math.floor(i * 1.43); // Approximate upsampling ratio
      if (originalIndex < audioBuffer.byteLength - 1) {
        compressedView.setInt16(i, view.getInt16(originalIndex, true), true);
      }
    }
    
    return compressed;
  } catch (error) {
    console.error('Audio compression failed:', error);
    return audioBuffer;
  }
};

// Preload audio for mobile optimization
export const preloadAudioForMobile = (audioUrls: string[]): Promise<HTMLAudioElement[]> => {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  
  if (!isMobile) {
    // On desktop, preload all audio
    return Promise.all(
      audioUrls.map(url => {
        return new Promise<HTMLAudioElement>((resolve, reject) => {
          const audio = new Audio(url);
          audio.preload = 'auto';
          audio.oncanplaythrough = () => resolve(audio);
          audio.onerror = reject;
        });
      })
    );
  } else {
    // On mobile, only preload metadata to save bandwidth
    return Promise.all(
      audioUrls.map(url => {
        return new Promise<HTMLAudioElement>((resolve, reject) => {
          const audio = new Audio(url);
          audio.preload = 'metadata';
          audio.onloadedmetadata = () => resolve(audio);
          audio.onerror = reject;
        });
      })
    );
  }
};

// Battery-aware optimization
export const isBatteryOptimizationNeeded = async (): Promise<boolean> => {
  if ('getBattery' in navigator) {
    try {
      const battery = await (navigator as any).getBattery();
      return !battery.charging && battery.level < 0.2; // Low battery and not charging
    } catch (error) {
      console.warn('Battery API not available:', error);
    }
  }
  return false;
};

// Reduce functionality based on battery level
export const getBatteryOptimizedSettings = async () => {
  const needsOptimization = await isBatteryOptimizationNeeded();
  
  return {
    disableAnimations: needsOptimization,
    reducedAudioQuality: needsOptimization,
    disableBackgroundProcessing: needsOptimization,
    increasedCacheDuration: needsOptimization
  };
};

// Memory pressure detection
export const isMemoryPressureHigh = (): boolean => {
  if ('memory' in performance) {
    const memInfo = (performance as any).memory;
    const usedRatio = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
    return usedRatio > 0.8; // High memory usage
  }
  return false;
};

// Cleanup resources when memory pressure is high
export const cleanupResourcesForMemory = () => {
  // Force garbage collection if available
  if ('gc' in window) {
    (window as any).gc();
  }
  
  // Clear unused caches
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      // Keep only essential caches
      const essentialCaches = ['nathan-static-v1'];
      cacheNames.forEach(cacheName => {
        if (!essentialCaches.includes(cacheName)) {
          caches.delete(cacheName);
        }
      });
    });
  }
  
  // Clear old localStorage entries
  try {
    const keysToCheck = Object.keys(localStorage);
    keysToCheck.forEach(key => {
      if (key.startsWith('nathan-temp-') || key.startsWith('nathan-cache-')) {
        const item = localStorage.getItem(key);
        if (item) {
          try {
            const parsed = JSON.parse(item);
            if (parsed.timestamp && Date.now() - parsed.timestamp > 24 * 60 * 60 * 1000) {
              localStorage.removeItem(key);
            }
          } catch {
            // Remove invalid entries
            localStorage.removeItem(key);
          }
        }
      }
    });
  } catch (error) {
    console.warn('Failed to cleanup localStorage:', error);
  }
};

// Monitor and respond to memory pressure
export const startMemoryMonitoring = (callback?: () => void) => {
  const checkMemory = () => {
    if (isMemoryPressureHigh()) {
      console.warn('High memory pressure detected, cleaning up resources');
      cleanupResourcesForMemory();
      callback?.();
    }
  };
  
  // Check memory every 30 seconds
  const interval = setInterval(checkMemory, 30000);
  
  // Also check on visibility change (when app becomes active)
  const handleVisibilityChange = () => {
    if (!document.hidden) {
      checkMemory();
    }
  };
  
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // Return cleanup function
  return () => {
    clearInterval(interval);
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
};