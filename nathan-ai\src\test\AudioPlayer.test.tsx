import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import AudioPlayer from '../components/AudioPlayer';
import type { AudioQueueItem } from '../types/components';

// Mock HTMLAudioElement
class MockAudio {
  public src = '';
  public volume = 1;
  public currentTime = 0;
  public paused = true;
  public preload = 'auto';
  public ended = false;
  
  private listeners: { [key: string]: EventListener[] } = {};
  private loadingTimeout: NodeJS.Timeout | null = null;
  private canPlayTimeout: NodeJS.Timeout | null = null;
  
  constructor() {
    // Simulate audio loading with proper cleanup
    this.loadingTimeout = setTimeout(() => {
      this.dispatchEvent(new Event('loadstart'));
      this.canPlayTimeout = setTimeout(() => {
        this.dispatchEvent(new Event('canplay'));
      }, 50);
    }, 10);
  }
  
  addEventListener(event: string, listener: EventListener) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }
  
  removeEventListener(event: string, listener: EventListener) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(l => l !== listener);
    }
  }
  
  dispatchEvent(event: Event) {
    if (this.listeners[event.type]) {
      this.listeners[event.type].forEach(listener => listener(event));
    }
    return true;
  }
  
  play() {
    this.paused = false;
    return Promise.resolve();
  }
  
  pause() {
    this.paused = true;
  }
  
  // Cleanup method
  cleanup() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
      this.loadingTimeout = null;
    }
    if (this.canPlayTimeout) {
      clearTimeout(this.canPlayTimeout);
      this.canPlayTimeout = null;
    }
  }
  
  // Helper methods for testing
  simulateEnd() {
    this.ended = true;
    this.paused = true;
    this.dispatchEvent(new Event('ended'));
  }
  
  simulateError() {
    this.dispatchEvent(new Event('error'));
  }
}

// Mock global Audio constructor
const mockAudioInstances: MockAudio[] = [];
global.Audio = vi.fn().mockImplementation(() => {
  const instance = new MockAudio();
  mockAudioInstances.push(instance);
  return instance;
}) as any;

describe('AudioPlayer', () => {
  const mockOnPlayingChange = vi.fn();
  const mockOnAudioComplete = vi.fn();
  const mockOnError = vi.fn();
  
  const defaultProps = {
    audioQueue: [],
    isPlaying: false,
    onPlayingChange: mockOnPlayingChange,
    onAudioComplete: mockOnAudioComplete,
    onError: mockOnError,
  };
  
  const sampleAudioItem: AudioQueueItem = {
    id: 'test-1',
    audioUrl: 'https://example.com/audio1.mp3',
    text: 'Hello, this is a test message',
    priority: 1
  };
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockAudioInstances.length = 0;
  });
  
  afterEach(() => {
    // Clean up any remaining audio instances
    mockAudioInstances.forEach(audio => {
      audio.pause();
      audio.cleanup();
    });
  });
  
  it('renders without crashing', () => {
    const { container } = render(<AudioPlayer {...defaultProps} />);
    expect(container.firstChild).toBeInTheDocument();
  });
  
  it('displays loading indicator when audio is loading', async () => {
    const audioQueue = [sampleAudioItem];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(screen.getByText('Loading audio...')).toBeInTheDocument();
    });
  });
  
  it('plays audio when item is added to queue', async () => {
    const audioQueue = [sampleAudioItem];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
      expect(mockAudioInstances[0].src).toBe(sampleAudioItem.audioUrl);
    });
    
    await waitFor(() => {
      expect(mockOnPlayingChange).toHaveBeenCalledWith(true);
    });
  });
  
  it('displays now playing information', async () => {
    const audioQueue = [sampleAudioItem];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Playing: Hello, this is a test message/)).toBeInTheDocument();
    });
  });
  
  it('truncates long text in now playing display', async () => {
    const longTextItem: AudioQueueItem = {
      id: 'test-long',
      audioUrl: 'https://example.com/audio.mp3',
      text: 'This is a very long message that should be truncated when displayed in the now playing section',
      priority: 1
    };
    
    const audioQueue = [longTextItem];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Playing: This is a very long message that should be trunca.../)).toBeInTheDocument();
    });
  });
  
  it('handles audio completion correctly', async () => {
    const audioQueue = [sampleAudioItem];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
    });
    
    // Wait for audio to start playing
    await waitFor(() => {
      expect(mockOnPlayingChange).toHaveBeenCalledWith(true);
    });
    
    // Simulate audio ending
    act(() => {
      mockAudioInstances[0].simulateEnd();
    });
    
    await waitFor(() => {
      expect(mockOnAudioComplete).toHaveBeenCalledWith(sampleAudioItem.id);
      expect(mockOnPlayingChange).toHaveBeenCalledWith(false);
    }, { timeout: 2000 });
  });
  
  it('handles audio errors gracefully', async () => {
    const audioQueue = [sampleAudioItem];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
    });
    
    // Simulate audio error
    act(() => {
      mockAudioInstances[0].simulateError();
    });
    
    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith(expect.stringContaining('Audio playback failed'));
      expect(mockOnPlayingChange).toHaveBeenCalledWith(false);
    });
    
    expect(screen.getByText(/Audio playback failed/)).toBeInTheDocument();
  });
  
  it('processes queue in priority order', async () => {
    const highPriorityItem: AudioQueueItem = {
      id: 'high-priority',
      audioUrl: 'https://example.com/high.mp3',
      text: 'High priority message',
      priority: 10
    };
    
    const lowPriorityItem: AudioQueueItem = {
      id: 'low-priority',
      audioUrl: 'https://example.com/low.mp3',
      text: 'Low priority message',
      priority: 1
    };
    
    const audioQueue = [lowPriorityItem, highPriorityItem]; // Add in reverse priority order
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
      expect(mockAudioInstances[0].src).toBe(highPriorityItem.audioUrl);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/Playing: High priority message/)).toBeInTheDocument();
    });
  });
  
  it('processes next item in queue after current completes', async () => {
    const firstItem: AudioQueueItem = {
      id: 'first',
      audioUrl: 'https://example.com/first.mp3',
      text: 'First message',
      priority: 1
    };
    
    const secondItem: AudioQueueItem = {
      id: 'second',
      audioUrl: 'https://example.com/second.mp3',
      text: 'Second message',
      priority: 1
    };
    
    // Start with both items in queue
    render(<AudioPlayer {...defaultProps} audioQueue={[firstItem, secondItem]} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
      expect(mockAudioInstances[0].src).toBe(firstItem.audioUrl);
    });
    
    // Complete first audio
    act(() => {
      mockAudioInstances[0].simulateEnd();
    });
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(2);
      expect(mockAudioInstances[1].src).toBe(secondItem.audioUrl);
    }, { timeout: 2000 });
  });
  
  it('displays queue count when items are queued', async () => {
    const audioQueue = [
      { id: '1', audioUrl: 'url1.mp3', text: 'Message 1', priority: 1 },
      { id: '2', audioUrl: 'url2.mp3', text: 'Message 2', priority: 1 },
      { id: '3', audioUrl: 'url3.mp3', text: 'Message 3', priority: 1 }
    ];
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(screen.getByText('2 audio items queued')).toBeInTheDocument(); // 3 total - 1 playing = 2 queued
    });
  });
  
  it('respects volume setting', async () => {
    const audioQueue = [sampleAudioItem];
    const customVolume = 0.5;
    
    render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} volume={customVolume} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
      expect(mockAudioInstances[0].volume).toBe(customVolume);
    });
  });
  
  it('updates volume when prop changes', async () => {
    const audioQueue = [sampleAudioItem];
    const initialVolume = 0.5;
    const newVolume = 0.8;
    
    const { rerender } = render(
      <AudioPlayer {...defaultProps} audioQueue={audioQueue} volume={initialVolume} />
    );
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
      expect(mockAudioInstances[0].volume).toBe(initialVolume);
    });
    
    rerender(<AudioPlayer {...defaultProps} audioQueue={audioQueue} volume={newVolume} />);
    
    // Give time for the volume update effect to run
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });
    
    expect(mockAudioInstances[0].volume).toBe(newVolume);
  });
  
  it('pauses audio when isPlaying becomes false', async () => {
    const audioQueue = [sampleAudioItem];
    
    const { rerender } = render(
      <AudioPlayer {...defaultProps} audioQueue={audioQueue} />
    );
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
    });
    
    // Wait for audio to start playing
    await waitFor(() => {
      expect(mockOnPlayingChange).toHaveBeenCalledWith(true);
    });
    
    // Change isPlaying to false
    rerender(<AudioPlayer {...defaultProps} audioQueue={audioQueue} isPlaying={false} />);
    
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });
    
    expect(mockAudioInstances[0].paused).toBe(true);
  });
  
  it('handles empty queue gracefully', () => {
    render(<AudioPlayer {...defaultProps} audioQueue={[]} />);
    
    expect(screen.queryByText(/Loading audio/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Playing:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/audio items queued/)).not.toBeInTheDocument();
  });
  
  it('cleans up audio on unmount', async () => {
    const audioQueue = [sampleAudioItem];
    
    const { unmount } = render(<AudioPlayer {...defaultProps} audioQueue={audioQueue} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
    });
    
    const audioInstance = mockAudioInstances[0];
    const pauseSpy = vi.spyOn(audioInstance, 'pause');
    
    act(() => {
      unmount();
    });
    
    expect(pauseSpy).toHaveBeenCalled();
    expect(audioInstance.src).toBe('');
  });
  
  it('handles multiple rapid queue updates', async () => {
    const { rerender } = render(<AudioPlayer {...defaultProps} audioQueue={[]} />);
    
    const items = [
      { id: '1', audioUrl: 'url1.mp3', text: 'Message 1', priority: 1 },
      { id: '2', audioUrl: 'url2.mp3', text: 'Message 2', priority: 2 },
      { id: '3', audioUrl: 'url3.mp3', text: 'Message 3', priority: 1 }
    ];
    
    // Rapidly update queue
    rerender(<AudioPlayer {...defaultProps} audioQueue={[items[0]]} />);
    rerender(<AudioPlayer {...defaultProps} audioQueue={[items[0], items[1]]} />);
    rerender(<AudioPlayer {...defaultProps} audioQueue={items} />);
    
    await waitFor(() => {
      expect(mockAudioInstances).toHaveLength(1);
      // Should play highest priority item first
      expect(mockAudioInstances[0].src).toBe(items[1].audioUrl);
    });
  });
});