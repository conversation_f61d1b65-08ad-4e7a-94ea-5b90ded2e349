.inputController {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

/* Mode Toggle */
.modeToggle {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.toggleButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border: none;
  border-radius: 24px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

.toggleButton:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.toggleButton.voice {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
}

.toggleButton.text {
  background: linear-gradient(135deg, #059669, #0d9488);
  color: white;
}

.toggleTrack {
  position: relative;
  width: 40px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.toggleThumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.voice .toggleThumb {
  transform: translateX(20px);
}

.toggleIcon {
  width: 10px;
  height: 10px;
  color: #64748b;
}

.toggleLabel {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Input Area */
.inputArea {
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
  transition: all 0.3s ease;
}

.inputArea:focus-within {
  border-color: #4f46e5;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Error Display */
.errorDisplay {
  margin-top: 8px;
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.errorIcon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.errorDismiss {
  margin-left: auto;
  padding: 4px;
  border: none;
  background: transparent;
  color: #dc2626;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.errorDismiss:hover {
  background: rgba(220, 38, 38, 0.1);
}

.errorDismiss svg {
  width: 16px;
  height: 16px;
}

/* Status Bar */
.statusBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #e2e8f0;
  margin-top: 8px;
}

.statusIndicators {
  display: flex;
  align-items: center;
  gap: 16px;
}

.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4f46e5;
  font-size: 14px;
  font-weight: 500;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.listeningIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.pulseIndicator {
  width: 12px;
  height: 12px;
  background: currentColor;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.modeIndicator {
  display: flex;
  align-items: center;
}

.modeLabel {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .inputController {
    padding: 16px;
    margin: 0;
    border-radius: 12px 12px 0 0;
    /* Safe area support */
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
  
  .toggleButton {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 44px; /* Touch target size */
    border-radius: 22px;
    /* Touch feedback */
    -webkit-tap-highlight-color: transparent;
  }
  
  .toggleTrack {
    width: 40px;
    height: 20px;
  }
  
  .toggleThumb {
    width: 16px;
    height: 16px;
  }
  
  .voice .toggleThumb {
    transform: translateX(20px);
  }
  
  .inputArea {
    min-height: 100px;
    padding: 12px;
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
  }
  
  .statusBar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .statusIndicators {
    order: 2;
    width: 100%;
    justify-content: space-between;
  }
  
  .modeIndicator {
    order: 1;
    align-self: flex-end;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .inputController {
    background: #1e293b;
    border-color: #475569;
  }
  
  .toggleButton {
    background: #334155;
    color: #cbd5e1;
  }
  
  .toggleButton:hover {
    background: #475569;
  }
  
  .inputArea {
    background: #0f172a;
    border-color: #475569;
  }
  
  .inputArea:focus-within {
    background: #1e293b;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  .errorMessage {
    background: #1f2937;
    border-color: #374151;
    color: #f87171;
  }
  
  .statusBar {
    border-color: #475569;
  }
  
  .modeLabel {
    color: #94a3b8;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .inputController {
    border-width: 2px;
  }
  
  .toggleButton {
    border: 2px solid currentColor;
  }
  
  .inputArea {
    border-width: 3px;
  }
  
  .errorMessage {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .toggleButton,
  .toggleTrack,
  .toggleThumb,
  .inputArea,
  .spinner,
  .pulseIndicator {
    animation: none;
    transition: none;
  }
  
  .errorMessage {
    animation: none;
  }
}@media (
max-width: 480px) {
  .inputController {
    padding: 12px;
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }
  
  .toggleButton {
    padding: 6px 12px;
    font-size: 13px;
    min-height: 44px;
  }
  
  .toggleTrack {
    width: 36px;
    height: 18px;
  }
  
  .toggleThumb {
    width: 14px;
    height: 14px;
  }
  
  .voice .toggleThumb {
    transform: translateX(18px);
  }
  
  .inputArea {
    min-height: 80px;
    padding: 10px;
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 500px) {
  .inputController {
    padding: 8px 16px;
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }
  
  .modeToggle {
    margin-bottom: 4px;
  }
  
  .toggleButton {
    padding: 4px 12px;
    font-size: 12px;
    min-height: 36px;
  }
  
  .inputArea {
    min-height: 60px;
    padding: 8px;
  }
  
  .statusBar {
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }
  
  .statusIndicators {
    order: 1;
    width: auto;
  }
  
  .modeIndicator {
    order: 2;
    align-self: auto;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .toggleButton {
    /* Remove hover effects */
    transition: background-color 0.2s ease, transform 0.1s ease;
  }
  
  .toggleButton:hover {
    transform: none;
  }
  
  .toggleButton:active {
    transform: scale(0.98);
  }
  
  .inputArea {
    /* Improve touch interaction */
    touch-action: manipulation;
  }
  
  .errorDismiss {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Keyboard Adaptation */
@media (max-width: 768px) {
  .inputController {
    /* Adjust for virtual keyboard */
    transition: padding-bottom 0.3s ease;
  }
  
  /* When keyboard is open (detected via CSS custom property) */
  .inputController[data-keyboard-open="true"] {
    padding-bottom: max(16px, calc(env(safe-area-inset-bottom) + var(--keyboard-height, 0px) * 0.1));
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .inputController {
    border-width: 0.5px;
  }
  
  .inputArea {
    border-width: 1px;
  }
  
  .errorMessage {
    border-width: 0.5px;
  }
}

/* Accessibility Improvements */
.toggleButton:focus {
  outline: 2px solid var(--primary-color, #4f46e5);
  outline-offset: 2px;
}

.inputArea:focus-within {
  outline: 2px solid var(--primary-color, #4f46e5);
  outline-offset: -2px;
}

/* Performance Optimizations for Mobile */
@media (max-width: 768px) {
  .spinner,
  .pulseIndicator {
    /* Use transform instead of other properties for better performance */
    will-change: transform;
  }
  
  .toggleButton,
  .inputArea {
    /* Enable hardware acceleration */
    transform: translateZ(0);
  }
}

/* Prevent zoom on input focus (iOS) */
@media (max-width: 768px) {
  .inputArea input,
  .inputArea textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}