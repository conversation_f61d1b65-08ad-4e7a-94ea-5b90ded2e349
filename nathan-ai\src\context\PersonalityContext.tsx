import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import type { PersonalityConfig } from '../types/personality';
import { loadPersonality, savePersonality } from '../utils/personality';

// Context type definition
interface PersonalityContextType {
  personality: PersonalityConfig | null;
  isLoading: boolean;
  error: string | null;
  updatePersonality: (config: Partial<PersonalityConfig>) => Promise<void>;
  resetPersonality: () => Promise<void>;
}

// Create the context
const PersonalityContext = createContext<PersonalityContextType | undefined>(undefined);

// Provider component props
interface PersonalityProviderProps {
  children: ReactNode;
}

// Provider component
export function PersonalityProvider({ children }: PersonalityProviderProps) {
  const [personality, setPersonality] = useState<PersonalityConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load personality on mount
  useEffect(() => {
    const initializePersonality = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const loadedPersonality = await loadPersonality();
        setPersonality(loadedPersonality);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load personality';
        setError(errorMessage);
        console.error('Failed to load personality:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializePersonality();
  }, []);

  // Update personality function
  const updatePersonality = async (config: Partial<PersonalityConfig>) => {
    if (!personality) {
      throw new Error('No personality loaded to update');
    }

    try {
      setError(null);
      const updatedPersonality = { ...personality, ...config };
      await savePersonality(updatedPersonality);
      setPersonality(updatedPersonality);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update personality';
      setError(errorMessage);
      throw err;
    }
  };

  // Reset personality to default
  const resetPersonality = async () => {
    try {
      setError(null);
      setIsLoading(true);
      // Load the default personality by clearing any saved preferences
      localStorage.removeItem('nathan-personality-preferences');
      const defaultPersonality = await loadPersonality();
      setPersonality(defaultPersonality);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset personality';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const value: PersonalityContextType = {
    personality,
    isLoading,
    error,
    updatePersonality,
    resetPersonality,
  };

  return (
    <PersonalityContext.Provider value={value}>
      {children}
    </PersonalityContext.Provider>
  );
}

// Hook to use the personality context
export function usePersonalityContext(): PersonalityContextType {
  const context = useContext(PersonalityContext);
  if (context === undefined) {
    throw new Error('usePersonalityContext must be used within a PersonalityProvider');
  }
  return context;
}