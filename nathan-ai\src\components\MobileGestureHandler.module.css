.gestureHandler {
  position: relative;
  width: 100%;
  height: 100%;
}

.gestureHandler.enabled {
  /* Enable touch interactions */
  touch-action: pan-x pan-y;
  -webkit-user-select: none;
  user-select: none;
  
  /* Improve touch performance */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Visual feedback for gesture areas */
.gestureHandler.enabled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
  opacity: 0;
  background: linear-gradient(
    90deg,
    rgba(79, 70, 229, 0.1) 0%,
    transparent 20%,
    transparent 80%,
    rgba(79, 70, 229, 0.1) 100%
  );
  transition: opacity 0.2s ease;
}

.gestureHandler.enabled:active::before {
  opacity: 1;
}

/* Disable text selection during gestures */
.gestureHandler.enabled * {
  -webkit-user-select: none;
  user-select: none;
}

/* Re-enable text selection for specific elements */
.gestureHandler.enabled input,
.gestureHandler.enabled textarea,
.gestureHandler.enabled [contenteditable],
.gestureHandler.enabled .selectable-text {
  -webkit-user-select: text;
  user-select: text;
}

/* Gesture indicators (optional visual cues) */
.gestureHandler[data-gesture-hint="true"]::after {
  content: '← Swipe →';
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: rgba(0, 0, 0, 0.3);
  pointer-events: none;
  opacity: 0;
  animation: gestureHint 3s ease-in-out infinite;
}

@keyframes gestureHint {
  0%, 90%, 100% {
    opacity: 0;
  }
  10%, 80% {
    opacity: 1;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .gestureHandler.enabled::before {
    background: linear-gradient(
      90deg,
      rgba(99, 102, 241, 0.1) 0%,
      transparent 20%,
      transparent 80%,
      rgba(99, 102, 241, 0.1) 100%
    );
  }
  
  .gestureHandler[data-gesture-hint="true"]::after {
    color: rgba(255, 255, 255, 0.3);
  }
}

/* Accessibility - respect reduced motion */
@media (prefers-reduced-motion: reduce) {
  .gestureHandler.enabled::before {
    transition: none;
  }
  
  .gestureHandler[data-gesture-hint="true"]::after {
    animation: none;
    opacity: 0.3;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .gestureHandler.enabled::before {
    background: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0.2) 0%,
      transparent 20%,
      transparent 80%,
      rgba(0, 0, 0, 0.2) 100%
    );
  }
  
  @media (prefers-color-scheme: dark) {
    .gestureHandler.enabled::before {
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.2) 0%,
        transparent 20%,
        transparent 80%,
        rgba(255, 255, 255, 0.2) 100%
      );
    }
  }
}