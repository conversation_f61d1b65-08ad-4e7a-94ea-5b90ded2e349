import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CompatibilityWarning } from '../components/CompatibilityWarning';

// Mock the browser compatibility hook
const mockUseBrowserCompatibility = vi.fn();
vi.mock('../hooks/useBrowserCompatibility', () => ({
  useBrowserCompatibility: () => mockUseBrowserCompatibility()
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('CompatibilityWarning', () => {
  const mockBrowserCompatibility = {
    browserInfo: {
      name: 'Chrome',
      version: '91',
      isMobile: false,
      isIOS: false,
      isAndroid: false,
      isChrome: true,
      isFirefox: false,
      isSafari: false,
      isEdge: false
    },
    issues: [],
    isCompatible: true,
    compatibilityScore: 95,
    getRecommendedBrowserMessage: vi.fn(() => null),
    getMobileOptimizations: vi.fn(() => []),
    requestPermissions: vi.fn(() => Promise.resolve({ microphone: true, notifications: true }))
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    mockUseBrowserCompatibility.mockReturnValue(mockBrowserCompatibility);
  });

  afterEach(() => {
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
  });

  it('should not render when fully compatible', () => {
    const { container } = render(<CompatibilityWarning />);
    expect(container.firstChild).toBeNull();
  });

  it('should render when there are compatibility issues', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input is not supported',
          fallback: 'Text input will be used instead',
          guidance: 'Try updating your browser'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Browser Compatibility')).toBeInTheDocument();
    expect(screen.getByText('70% compatible')).toBeInTheDocument();
    expect(screen.getByText('Voice input is not supported')).toBeInTheDocument();
  });

  it('should display critical issues prominently', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 40,
      isCompatible: false,
      issues: [
        {
          feature: 'audioAPI',
          severity: 'critical',
          message: 'Audio playback is not supported',
          fallback: 'Text responses only',
          guidance: 'Please update your browser'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Critical Issues')).toBeInTheDocument();
    expect(screen.getByText('Audio playback is not supported')).toBeInTheDocument();
    expect(screen.getByText('Fallback: Text responses only')).toBeInTheDocument();
  });

  it('should display warning issues', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 75,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input may not work properly',
          fallback: 'Use text input instead',
          guidance: 'Enable microphone permissions'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Limited Features')).toBeInTheDocument();
    expect(screen.getByText('Voice input may not work properly')).toBeInTheDocument();
  });

  it('should display info issues', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 85,
      issues: [
        {
          feature: 'webAudio',
          severity: 'info',
          message: 'Advanced audio features are not available',
          guidance: 'Some features may be limited'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Information')).toBeInTheDocument();
    expect(screen.getByText('Advanced audio features are not available')).toBeInTheDocument();
  });

  it('should display mobile optimizations', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 80,
      browserInfo: {
        ...mockBrowserCompatibility.browserInfo,
        isMobile: true,
        isIOS: true
      },
      getMobileOptimizations: vi.fn(() => [
        'Touch interactions are optimized for mobile',
        'Audio playback requires user interaction on iOS'
      ]),
      issues: []
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Mobile Optimizations')).toBeInTheDocument();
    expect(screen.getByText('Touch interactions are optimized for mobile')).toBeInTheDocument();
    expect(screen.getByText('Audio playback requires user interaction on iOS')).toBeInTheDocument();
  });

  it('should display browser recommendation', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 60,
      getRecommendedBrowserMessage: vi.fn(() => 'Please update to a modern browser'),
      issues: []
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Recommendation: Please update to a modern browser')).toBeInTheDocument();
  });

  it('should handle permission requests', async () => {
    const mockRequestPermissions = vi.fn(() => 
      Promise.resolve({ microphone: true, notifications: false })
    );

    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      requestPermissions: mockRequestPermissions,
      issues: [
        {
          feature: 'mediaDevices',
          severity: 'warning',
          message: 'Microphone access may not work',
          guidance: 'Grant microphone permissions'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    const permissionButton = screen.getByText('Request Permissions');
    fireEvent.click(permissionButton);

    await waitFor(() => {
      expect(mockRequestPermissions).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(screen.getByText('Permission Status:')).toBeInTheDocument();
      expect(screen.getByText('Microphone: ✅ Granted')).toBeInTheDocument();
      expect(screen.getByText('Notifications: ❌ Denied')).toBeInTheDocument();
    });
  });

  it('should dismiss warning when close button is clicked', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input is not supported'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    expect(screen.getByText('Browser Compatibility')).toBeInTheDocument();
    
    const dismissButton = screen.getByLabelText('Dismiss compatibility warning');
    fireEvent.click(dismissButton);
    
    expect(screen.queryByText('Browser Compatibility')).not.toBeInTheDocument();
  });

  it('should dismiss warning when continue button is clicked', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input is not supported'
        }
      ]
    });

    render(<CompatibilityWarning />);
    
    const continueButton = screen.getByText('Continue Anyway');
    fireEvent.click(continueButton);
    
    expect(screen.queryByText('Browser Compatibility')).not.toBeInTheDocument();
  });

  it('should save dismissal state to localStorage when showOnlyOnce is true', () => {
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input is not supported'
        }
      ]
    });

    render(<CompatibilityWarning showOnlyOnce={true} />);
    
    const dismissButton = screen.getByLabelText('Dismiss compatibility warning');
    fireEvent.click(dismissButton);
    
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('compatibility-warning-dismissed', 'true');
  });

  it('should not render if previously dismissed and showOnlyOnce is true', () => {
    mockLocalStorage.getItem.mockReturnValue('true');
    
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input is not supported'
        }
      ]
    });

    const { container } = render(<CompatibilityWarning showOnlyOnce={true} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('should call onDismiss callback when dismissed', () => {
    const mockOnDismiss = vi.fn();
    
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input is not supported'
        }
      ]
    });

    render(<CompatibilityWarning onDismiss={mockOnDismiss} />);
    
    const dismissButton = screen.getByLabelText('Dismiss compatibility warning');
    fireEvent.click(dismissButton);
    
    expect(mockOnDismiss).toHaveBeenCalled();
  });

  it('should apply correct CSS classes based on compatibility score', () => {
    // Test critical score
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 40,
      issues: [
        {
          feature: 'audioAPI',
          severity: 'critical',
          message: 'Audio not supported'
        }
      ]
    });

    const { rerender } = render(<CompatibilityWarning />);
    expect(document.querySelector('.critical')).toBeInTheDocument();

    // Test warning score
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 70,
      issues: [
        {
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice not supported'
        }
      ]
    });

    rerender(<CompatibilityWarning />);
    expect(document.querySelector('.warning')).toBeInTheDocument();

    // Test good score
    mockUseBrowserCompatibility.mockReturnValue({
      ...mockBrowserCompatibility,
      compatibilityScore: 85,
      issues: [
        {
          feature: 'webAudio',
          severity: 'info',
          message: 'Some features limited'
        }
      ]
    });

    rerender(<CompatibilityWarning />);
    expect(document.querySelector('.good')).toBeInTheDocument();
  });
});