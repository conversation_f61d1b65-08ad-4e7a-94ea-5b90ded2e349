import React from 'react';
import { getEnvironmentValidation } from '../utils/env';
import { getRemainingApiRequests } from '../utils/security';
import styles from './SecurityStatus.module.css';

interface SecurityStatusProps {
  showDetails?: boolean;
  className?: string;
}

export const SecurityStatus: React.FC<SecurityStatusProps> = ({ 
  showDetails = false, 
  className 
}) => {
  const validation = getEnvironmentValidation();
  const hfRequests = getRemainingApiRequests('huggingface');
  const elRequests = getRemainingApiRequests('elevenlabs');

  if (!showDetails && validation.isValid) {
    return null; // Don't show anything if everything is OK and details not requested
  }

  return (
    <div className={`${styles.securityStatus} ${className || ''}`}>
      {!validation.isValid && (
        <div className={styles.errorSection}>
          <h4>⚠️ Security Issues Detected</h4>
          
          {validation.errors.length > 0 && (
            <div className={styles.errors}>
              <strong>Errors:</strong>
              <ul>
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {validation.securityIssues.length > 0 && (
            <div className={styles.securityIssues}>
              <strong>Security Issues:</strong>
              <ul>
                {validation.securityIssues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {validation.warnings.length > 0 && showDetails && (
        <div className={styles.warningSection}>
          <h4>⚡ Warnings</h4>
          <ul>
            {validation.warnings.map((warning, index) => (
              <li key={index}>{warning}</li>
            ))}
          </ul>
        </div>
      )}

      {showDetails && (
        <div className={styles.statusSection}>
          <h4>📊 API Rate Limits</h4>
          <div className={styles.rateLimits}>
            <div className={styles.apiStatus}>
              <strong>Hugging Face:</strong>
              <span>{hfRequests.perMinute} requests/min remaining</span>
              <span>{hfRequests.perHour} requests/hour remaining</span>
            </div>
            <div className={styles.apiStatus}>
              <strong>Eleven Labs:</strong>
              <span>{elRequests.perMinute} requests/min remaining</span>
              <span>{elRequests.perHour} requests/hour remaining</span>
            </div>
          </div>
        </div>
      )}

      {validation.isValid && showDetails && (
        <div className={styles.successSection}>
          <h4>✅ Security Status: OK</h4>
          <p>All security checks passed successfully.</p>
        </div>
      )}
    </div>
  );
};

export default SecurityStatus;