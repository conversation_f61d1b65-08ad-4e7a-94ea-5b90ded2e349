import { describe, it, expect } from 'vitest';
import type {
  Message,
  PersonalityConfig,
  ConversationState,
  ConversationAction,
  HuggingFaceResponse,
  ElevenLabsRequest,
  ChatInterfaceProps,
  InputControllerProps,
  MessageBubbleProps,
  AvatarProps,
  ApiResponse,
  ValidationResult
} from '../types';

describe('Type Definitions', () => {
  it('should have Message interface with correct properties', () => {
    const message: Message = {
      id: '1',
      content: 'Hello',
      timestamp: new Date(),
      sender: 'user',
      audioUrl: 'test.mp3',
      emotion: 'happy'
    };

    expect(message.id).toBe('1');
    expect(message.sender).toBe('user');
    expect(message.emotion).toBe('happy');
  });

  it('should have PersonalityConfig interface with correct structure', () => {
    const personality: PersonalityConfig = {
      name: '<PERSON>',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'companion',
        hobbies: ['coding', 'music'],
        style: {
          speech: 'casual',
          humor: 'witty',
          depth: 'thoughtful'
        },
        boundaries: {
          avoid: ['politics'],
          safe_topics: ['technology', 'music']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: ['How are you?', 'What\'s new?']
      },
      version: '1.0.0'
    };

    expect(personality.name).toBe('Nathan');
    expect(personality.personality.dynamic_traits.adaptive_empathy).toBe(true);
  });

  it('should have ConversationState interface with correct properties', () => {
    const state: ConversationState = {
      messages: [],
      isLoading: false,
      inputMode: 'text',
      visualMode: 'visual',
      isListening: false,
      isPlaying: false,
      error: null
    };

    expect(state.inputMode).toBe('text');
    expect(state.visualMode).toBe('visual');
    expect(state.isLoading).toBe(false);
  });

  it('should have ConversationAction types working correctly', () => {
    const addMessageAction: ConversationAction = {
      type: 'ADD_MESSAGE',
      payload: {
        id: '1',
        content: 'Test',
        timestamp: new Date(),
        sender: 'user'
      }
    };

    const setLoadingAction: ConversationAction = {
      type: 'SET_LOADING',
      payload: true
    };

    expect(addMessageAction.type).toBe('ADD_MESSAGE');
    expect(setLoadingAction.type).toBe('SET_LOADING');
  });

  it('should have API response types working correctly', () => {
    const hfResponse: HuggingFaceResponse = {
      generated_text: 'Hello there!'
    };

    const elRequest: ElevenLabsRequest = {
      text: 'Hello world',
      model_id: 'eleven_monolingual_v1',
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5
      }
    };

    expect(hfResponse.generated_text).toBe('Hello there!');
    expect(elRequest.text).toBe('Hello world');
  });

  it('should have component prop types working correctly', () => {
    const chatProps: ChatInterfaceProps = {
      mode: 'visual',
      personality: {
        name: 'Nathan',
        pronouns: 'he/him',
        personality: {
          tone: 'friendly',
          role: 'companion',
          hobbies: [],
          style: {
            speech: 'casual',
            humor: 'witty',
            depth: 'thoughtful'
          },
          boundaries: {
            avoid: [],
            safe_topics: []
          },
          dynamic_traits: {
            adaptive_empathy: true,
            mirroring_style: true,
            emotionally_available: true
          }
        },
        conversation_tips: {
          starter_prompts: []
        },
        version: '1.0.0'
      }
    };

    const inputProps: InputControllerProps = {
      inputMode: 'voice',
      onModeChange: () => {},
      onMessageSend: () => {},
      isListening: true
    };

    expect(chatProps.mode).toBe('visual');
    expect(inputProps.inputMode).toBe('voice');
  });

  it('should have utility types working correctly', () => {
    const apiResponse: ApiResponse<string> = {
      data: 'success',
      success: true,
      timestamp: new Date()
    };

    const validation: ValidationResult<number> = {
      isValid: true,
      data: 42,
      errors: []
    };

    expect(apiResponse.success).toBe(true);
    expect(validation.isValid).toBe(true);
    expect(validation.data).toBe(42);
  });
});