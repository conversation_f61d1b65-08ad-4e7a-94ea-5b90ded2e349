import React, { ReactNode } from 'react';
import { ErrorBoundary } from './ErrorBoundary';

interface FeatureErrorBoundaryProps {
  children: ReactNode;
  featureName: string;
  fallbackComponent?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export const FeatureErrorBoundary: React.FC<FeatureErrorBoundaryProps> = ({
  children,
  featureName,
  fallbackComponent,
  onError,
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log feature-specific error
    console.error(`Feature Error in ${featureName}:`, {
      feature: featureName,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    });

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }
  };

  const defaultFallback = (
    <div style={{
      padding: '1.5rem',
      textAlign: 'center',
      backgroundColor: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '8px',
      margin: '1rem 0',
    }}>
      <h4 style={{ color: '#495057', marginBottom: '0.5rem' }}>
        {featureName} Temporarily Unavailable
      </h4>
      <p style={{ color: '#6c757d', fontSize: '0.9rem', marginBottom: '1rem' }}>
        This feature encountered an error and has been temporarily disabled.
      </p>
      <button
        onClick={() => window.location.reload()}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '0.875rem',
        }}
      >
        Refresh to Try Again
      </button>
    </div>
  );

  return (
    <ErrorBoundary
      level="feature"
      onError={handleError}
      fallback={fallbackComponent || defaultFallback}
      resetOnPropsChange={true}
    >
      {children}
    </ErrorBoundary>
  );
};