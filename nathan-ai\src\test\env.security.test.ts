import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock import.meta.env for testing
const mockEnv = {
  VITE_HUGGING_FACE_API_KEY: '',
  VITE_ELEVEN_LABS_API_KEY: '',
  VITE_ELEVEN_LABS_VOICE_ID: '',
  VITE_HUGGING_FACE_MODEL: '',
  DEV: false,
  PROD: false,
  NODE_ENV: 'test',
};

vi.stubGlobal('import', {
  meta: {
    env: mockEnv,
  },
});

describe('Environment Security', () => {
  beforeEach(() => {
    // Reset mock environment
    Object.keys(mockEnv).forEach(key => {
      if (key !== 'NODE_ENV') {
        (mockEnv as any)[key] = '';
      }
    });
    mockEnv.DEV = false;
    mockEnv.PROD = false;
    
    // Clear module cache to get fresh imports
    vi.resetModules();
  });

  describe('Environment Validation', () => {
    it('should validate correct API keys', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_1234567890abcdef1234567890abcdef123';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'abcdef1234567890abcdef1234567890ab';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_1234567890abcdef';
      
      const { validateEnvironment } = await import('../utils/env');
      const result = validateEnvironment();
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing required variables', async () => {
      // Leave environment empty
      const { validateEnvironment } = await import('../utils/env');
      const result = validateEnvironment();
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(error => error.includes('Missing required'))).toBe(true);
    });

    it('should detect invalid API key formats', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'invalid_key'; // Should start with hf_
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'short'; // Too short
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice123';
      
      const { validateEnvironment } = await import('../utils/env');
      const result = validateEnvironment();
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should detect placeholder values', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'your_api_key_here';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'placeholder';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'test';
      
      const { validateEnvironment } = await import('../utils/env');
      const result = validateEnvironment();
      
      expect(result.isValid).toBe(false);
      expect(result.securityIssues.length).toBeGreaterThan(0);
      expect(result.securityIssues.some(issue => issue.includes('placeholder'))).toBe(true);
    });

    it('should detect invalid characters in environment variables', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_key with spaces';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'key\nwith\nnewlines';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice\tid';
      
      const { validateEnvironment } = await import('../utils/env');
      const result = validateEnvironment();
      
      expect(result.isValid).toBe(false);
      expect(result.securityIssues.length).toBeGreaterThan(0);
      expect(result.securityIssues.some(issue => issue.includes('invalid characters'))).toBe(true);
    });

    it('should warn about development keys in production', async () => {
      mockEnv.PROD = true;
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_dev_1234567890abcdef1234567890abcdef';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'test1234567890abcdef1234567890ab';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_1234567890abcdef';
      
      const { validateEnvironment } = await import('../utils/env');
      const result = validateEnvironment();
      
      expect(result.securityIssues.some(issue => 
        issue.includes('development API keys')
      )).toBe(true);
    });
  });

  describe('Secure Environment Variable Access', () => {
    it('should throw error for missing required variables', async () => {
      const { getEnvVar } = await import('../utils/env');
      
      expect(() => {
        getEnvVar('VITE_NONEXISTENT_VAR');
      }).toThrow();
    });

    it('should return default values when provided', async () => {
      const { getEnvVar } = await import('../utils/env');
      
      const result = getEnvVar('VITE_NONEXISTENT_VAR', 'default_value');
      expect(result).toBe('default_value');
    });

    it('should validate secure environment variables', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'invalid key format';
      
      const { getSecureEnvVar } = await import('../utils/env');
      
      expect(() => {
        getSecureEnvVar('VITE_HUGGING_FACE_API_KEY', (value) => value.startsWith('hf_'));
      }).toThrow();
    });

    it('should reject environment variables with invalid characters', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_key with spaces';
      
      const { getSecureEnvVar } = await import('../utils/env');
      
      expect(() => {
        getSecureEnvVar('VITE_HUGGING_FACE_API_KEY');
      }).toThrow('Invalid format');
    });
  });

  describe('Production vs Development Configuration', () => {
    it('should have different error messages in production', async () => {
      mockEnv.PROD = true;
      
      const { getEnvVar } = await import('../utils/env');
      
      expect(() => {
        getEnvVar('VITE_NONEXISTENT_VAR');
      }).toThrow('[REDACTED]');
    });

    it('should show variable names in development', async () => {
      mockEnv.DEV = true;
      
      const { getEnvVar } = await import('../utils/env');
      
      expect(() => {
        getEnvVar('VITE_NONEXISTENT_VAR');
      }).toThrow('VITE_NONEXISTENT_VAR');
    });
  });

  describe('Configuration Loading', () => {
    it('should load configuration successfully with valid environment', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_1234567890abcdef1234567890abcdef123';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'abcdef1234567890abcdef1234567890ab';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_1234567890abcdef';
      mockEnv.VITE_HUGGING_FACE_MODEL = 'microsoft/DialoGPT-medium';
      
      const { config } = await import('../utils/env');
      
      expect(config.huggingFace.apiKey).toBe(mockEnv.VITE_HUGGING_FACE_API_KEY);
      expect(config.elevenLabs.apiKey).toBe(mockEnv.VITE_ELEVEN_LABS_API_KEY);
      expect(config.elevenLabs.voiceId).toBe(mockEnv.VITE_ELEVEN_LABS_VOICE_ID);
      expect(config.huggingFace.model).toBe(mockEnv.VITE_HUGGING_FACE_MODEL);
    });

    it('should use default model when not specified', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'hf_1234567890abcdef1234567890abcdef123';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'abcdef1234567890abcdef1234567890ab';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'voice_id_1234567890abcdef';
      // Don't set VITE_HUGGING_FACE_MODEL
      
      const { config } = await import('../utils/env');
      
      expect(config.huggingFace.model).toBe('microsoft/DialoGPT-medium');
    });

    it('should fail to load configuration with invalid environment', async () => {
      mockEnv.VITE_HUGGING_FACE_API_KEY = 'invalid';
      mockEnv.VITE_ELEVEN_LABS_API_KEY = 'invalid';
      mockEnv.VITE_ELEVEN_LABS_VOICE_ID = 'invalid';
      
      await expect(async () => {
        await import('../utils/env');
      }).rejects.toThrow();
    });
  });
});