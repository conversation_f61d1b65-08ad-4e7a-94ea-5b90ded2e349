/**
 * Health check utilities for monitoring application status
 */

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  checks: HealthCheck[];
  uptime: number;
  version: string;
}

export interface HealthCheck {
  name: string;
  status: 'pass' | 'warn' | 'fail';
  message?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

/**
 * Health check manager
 */
export class HealthCheckManager {
  private startTime: number;
  private version: string;
  private checks: Map<string, () => Promise<HealthCheck>> = new Map();

  constructor(version: string = '1.0.0') {
    this.startTime = Date.now();
    this.version = version;
    this.registerDefaultChecks();
  }

  /**
   * Register a health check
   */
  registerCheck(name: string, checkFn: () => Promise<HealthCheck>): void {
    this.checks.set(name, checkFn);
  }

  /**
   * Run all health checks
   */
  async runHealthChecks(): Promise<HealthCheckResult> {
    const timestamp = new Date().toISOString();
    const uptime = Date.now() - this.startTime;
    const checks: HealthCheck[] = [];

    // Run all registered checks
    for (const [name, checkFn] of this.checks.entries()) {
      try {
        const start = performance.now();
        const result = await checkFn();
        const duration = performance.now() - start;
        
        checks.push({
          ...result,
          duration: Math.round(duration)
        });
      } catch (error) {
        checks.push({
          name,
          status: 'fail',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: 0
        });
      }
    }

    // Determine overall status
    const failedChecks = checks.filter(check => check.status === 'fail');
    const warnChecks = checks.filter(check => check.status === 'warn');
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (failedChecks.length > 0) {
      status = 'unhealthy';
    } else if (warnChecks.length > 0) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      timestamp,
      checks,
      uptime,
      version: this.version
    };
  }

  /**
   * Register default health checks
   */
  private registerDefaultChecks(): void {
    // Browser compatibility check
    this.registerCheck('browser-compatibility', async () => {
      const issues: string[] = [];
      
      // Check for required APIs
      if (typeof fetch === 'undefined') {
        issues.push('Fetch API not available');
      }
      
      if (typeof localStorage === 'undefined') {
        issues.push('LocalStorage not available');
      }
      
      if (typeof WebSocket === 'undefined') {
        issues.push('WebSocket not available');
      }
      
      // Check for optional APIs
      const warnings: string[] = [];
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        warnings.push('Speech Recognition not available');
      }
      
      if (!('speechSynthesis' in window)) {
        warnings.push('Speech Synthesis not available');
      }

      return {
        name: 'browser-compatibility',
        status: issues.length > 0 ? 'fail' : warnings.length > 0 ? 'warn' : 'pass',
        message: issues.length > 0 ? issues.join(', ') : warnings.length > 0 ? warnings.join(', ') : 'All APIs available',
        metadata: {
          userAgent: navigator.userAgent,
          issues,
          warnings
        }
      };
    });

    // Memory usage check
    this.registerCheck('memory-usage', async () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
        const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
        const usagePercent = Math.round((usedMB / limitMB) * 100);

        let status: 'pass' | 'warn' | 'fail';
        let message: string;

        if (usagePercent > 90) {
          status = 'fail';
          message = `Critical memory usage: ${usagePercent}%`;
        } else if (usagePercent > 70) {
          status = 'warn';
          message = `High memory usage: ${usagePercent}%`;
        } else {
          status = 'pass';
          message = `Memory usage normal: ${usagePercent}%`;
        }

        return {
          name: 'memory-usage',
          status,
          message,
          metadata: {
            usedMB,
            limitMB,
            usagePercent
          }
        };
      } else {
        return {
          name: 'memory-usage',
          status: 'warn',
          message: 'Memory API not available',
          metadata: {}
        };
      }
    });

    // Local storage check
    this.registerCheck('local-storage', async () => {
      try {
        const testKey = '__health_check_test__';
        const testValue = 'test';
        
        localStorage.setItem(testKey, testValue);
        const retrieved = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);
        
        if (retrieved === testValue) {
          return {
            name: 'local-storage',
            status: 'pass',
            message: 'LocalStorage working correctly'
          };
        } else {
          return {
            name: 'local-storage',
            status: 'fail',
            message: 'LocalStorage read/write failed'
          };
        }
      } catch (error) {
        return {
          name: 'local-storage',
          status: 'fail',
          message: `LocalStorage error: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }
    });

    // Network connectivity check
    this.registerCheck('network-connectivity', async () => {
      const online = navigator.onLine;
      
      if (!online) {
        return {
          name: 'network-connectivity',
          status: 'fail',
          message: 'Device is offline'
        };
      }

      // Test actual connectivity with a simple request
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch('/health', {
          method: 'HEAD',
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        return {
          name: 'network-connectivity',
          status: 'pass',
          message: 'Network connectivity confirmed',
          metadata: {
            online: true,
            responseStatus: response.status
          }
        };
      } catch (error) {
        return {
          name: 'network-connectivity',
          status: 'warn',
          message: 'Network test failed, but device reports online',
          metadata: {
            online: true,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        };
      }
    });

    // Performance check
    this.registerCheck('performance', async () => {
      const start = performance.now();
      
      // Simulate some work
      const data = Array.from({ length: 1000 }, (_, i) => i);
      data.sort((a, b) => b - a);
      
      const duration = performance.now() - start;
      
      let status: 'pass' | 'warn' | 'fail';
      let message: string;
      
      if (duration > 100) {
        status = 'fail';
        message = `Poor performance: ${Math.round(duration)}ms`;
      } else if (duration > 50) {
        status = 'warn';
        message = `Slow performance: ${Math.round(duration)}ms`;
      } else {
        status = 'pass';
        message = `Good performance: ${Math.round(duration)}ms`;
      }
      
      return {
        name: 'performance',
        status,
        message,
        metadata: {
          duration: Math.round(duration)
        }
      };
    });
  }
}

/**
 * Global health check instance
 */
export const healthCheckManager = new HealthCheckManager();

/**
 * Express-style health check endpoint handler
 */
export async function healthCheckHandler(): Promise<Response> {
  try {
    const result = await healthCheckManager.runHealthChecks();
    
    const statusCode = result.status === 'healthy' ? 200 : 
                      result.status === 'degraded' ? 200 : 503;
    
    return new Response(JSON.stringify(result, null, 2), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    const errorResult: HealthCheckResult = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: [{
        name: 'health-check-system',
        status: 'fail',
        message: error instanceof Error ? error.message : 'Health check system failed'
      }],
      uptime: 0,
      version: '1.0.0'
    };
    
    return new Response(JSON.stringify(errorResult, null, 2), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
}

/**
 * Simple health check for basic monitoring
 */
export function simpleHealthCheck(): { status: string; timestamp: string } {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString()
  };
}

/**
 * Start periodic health monitoring
 */
export function startHealthMonitoring(intervalMs: number = 60000): () => void {
  const interval = setInterval(async () => {
    try {
      const result = await healthCheckManager.runHealthChecks();
      
      if (result.status === 'unhealthy') {
        console.error('Health check failed:', result);
      } else if (result.status === 'degraded') {
        console.warn('Health check degraded:', result);
      } else {
        console.log('Health check passed:', result.timestamp);
      }
    } catch (error) {
      console.error('Health monitoring error:', error);
    }
  }, intervalMs);
  
  return () => clearInterval(interval);
}

// Run health check if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  healthCheckManager.runHealthChecks()
    .then(result => {
      console.log('Health Check Results:');
      console.log(JSON.stringify(result, null, 2));
      process.exit(result.status === 'healthy' ? 0 : 1);
    })
    .catch(error => {
      console.error('Health check failed:', error);
      process.exit(1);
    });
}