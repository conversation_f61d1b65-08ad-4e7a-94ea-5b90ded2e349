import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useAudioConversation } from '../hooks/useAudioConversation';
import { AIService } from '../services/AIService';
import type { PersonalityConfig } from '../types/personality';

// Mock AI Service
vi.mock('../services/AIService', () => {
  return {
    AIService: vi.fn().mockImplementation(() => ({
      generateResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
    }))
  };
});

// Mock personality hook
vi.mock('../hooks/usePersonality', () => ({
  usePersonality: () => ({
    personality: {
      name: '<PERSON>',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly and supportive',
        role: 'conversational AI companion',
        hobbies: ['technology', 'learning', 'helping others'],
        style: {
          speech: 'natural and conversational',
          humor: 'light and appropriate',
          depth: 'adaptable to context'
        },
        boundaries: {
          avoid: ['harmful content', 'inappropriate topics'],
          safe_topics: ['technology', 'general conversation', 'learning']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: [
          'Hello! How are you doing today?',
          'What would you like to talk about?',
          'I\'m here to help with anything you need!'
        ]
      },
      version: '1.0.0'
    } as PersonalityConfig
  })
}));

// Mock conversation hook
vi.mock('../hooks/useConversation', () => ({
  useConversation: () => ({
    messages: [],
    isLoading: false,
    isListening: false,
    isPlaying: false,
    inputMode: 'text' as const,
    visualMode: 'visual' as const,
    error: null,
    addUserMessage: vi.fn().mockReturnValue({
      id: 'user-msg-1',
      content: 'Hello Nathan',
      timestamp: new Date(),
      sender: 'user' as const
    }),
    addNathanMessage: vi.fn().mockReturnValue({
      id: 'nathan-msg-1',
      content: 'Hello! How can I help you today?',
      timestamp: new Date(),
      sender: 'nathan' as const,
      emotion: 'happy' as const
    }),
    addMessage: vi.fn(),
    setLoading: vi.fn(),
    setListening: vi.fn(),
    setPlaying: vi.fn(),
    setInputMode: vi.fn(),
    setVisualMode: vi.fn(),
    setError: vi.fn(),
    clearMessages: vi.fn()
  })
}));

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-audio-url');
global.URL.revokeObjectURL = vi.fn();

describe('Audio Conversation Integration', () => {
  let mockAIService: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockAIService = {
      generateResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
    };
    (AIService as any).mockImplementation(() => mockAIService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Audio-enabled conversation flow', () => {
    it('should send message and generate audio response', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Hello! How can I help you today?',
        emotion: 'happy',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.sendMessage('Hello Nathan');
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Hello Nathan',
        expect.any(Object), // personality config
        { max_length: 150, temperature: 0.8 },
        { voice_settings: { stability: 0.75, similarity_boost: 0.75 } }
      );

      // Check that audio queue was populated
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0]).toMatchObject({
        id: 'nathan-msg-1',
        audioUrl: 'blob:mock-audio-url',
        text: 'Hello! How can I help you today?',
        priority: 1
      });
    });

    it('should handle TTS generation failure gracefully', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Hello! How can I help you today?',
        emotion: 'happy',
        audio: null // No audio generated
      });

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.sendMessage('Hello Nathan');
      });

      // Should still add message without audio
      expect(result.current.audioQueue).toHaveLength(0);
      expect(mockAIService.generateResponse).toHaveBeenCalled();
    });

    it('should interrupt audio when new message is sent', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'First response',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Send first message
      await act(async () => {
        await result.current.sendMessage('First message');
      });

      expect(result.current.audioQueue).toHaveLength(1);

      // Send second message (should interrupt)
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Second response',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      await act(async () => {
        await result.current.sendMessage('Second message');
      });

      // Audio queue should be cleared and repopulated with new message
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0].text).toBe('Second response');
    });

    it('should generate audio for existing message', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      const existingMessage = {
        id: 'existing-msg',
        content: 'Existing message without audio',
        timestamp: new Date(),
        sender: 'nathan' as const,
        emotion: 'neutral' as const
      };

      mockAIService.generateAudio.mockResolvedValue(mockAudioBuffer);

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.generateAudioForMessage(existingMessage);
      });

      expect(mockAIService.generateAudio).toHaveBeenCalledWith(
        'Existing message without audio',
        { voice_settings: { stability: 0.75, similarity_boost: 0.75 } }
      );

      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0]).toMatchObject({
        id: 'existing-msg',
        audioUrl: 'blob:mock-audio-url',
        text: 'Existing message without audio',
        priority: 1
      });
    });

    it('should not generate audio for user messages', async () => {
      const userMessage = {
        id: 'user-msg',
        content: 'User message',
        timestamp: new Date(),
        sender: 'user' as const
      };

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.generateAudioForMessage(userMessage);
      });

      expect(mockAIService.generateAudio).not.toHaveBeenCalled();
      expect(result.current.audioQueue).toHaveLength(0);
    });

    it('should handle audio completion correctly', async () => {
      const { result } = renderHook(() => useAudioConversation());

      // Simulate audio queue with items
      act(() => {
        result.current.audioQueue.push({
          id: 'audio-1',
          audioUrl: 'blob:mock-url-1',
          text: 'First audio',
          priority: 1
        });
        result.current.audioQueue.push({
          id: 'audio-2',
          audioUrl: 'blob:mock-url-2',
          text: 'Second audio',
          priority: 1
        });
      });

      expect(result.current.audioQueue).toHaveLength(2);

      // Complete first audio
      act(() => {
        result.current.handleAudioComplete('audio-1');
      });

      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0].id).toBe('audio-2');
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url-1');
    });

    it('should handle audio errors gracefully', async () => {
      const { result } = renderHook(() => useAudioConversation());

      act(() => {
        result.current.handleAudioError('Audio playback failed');
      });

      // Should set error state in conversation
      expect(result.current.handleAudioError).toBeDefined();
    });

    it('should clear conversation and audio', async () => {
      const { result } = renderHook(() => useAudioConversation());

      // Add some audio to queue
      act(() => {
        result.current.audioQueue.push({
          id: 'audio-1',
          audioUrl: 'blob:mock-url-1',
          text: 'Audio to clear',
          priority: 1
        });
      });

      expect(result.current.audioQueue).toHaveLength(1);

      act(() => {
        result.current.clearConversation();
      });

      expect(result.current.audioQueue).toHaveLength(0);
      expect(mockAIService.clearConversationHistory).toHaveBeenCalled();
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url-1');
    });

    it('should manage audio URL cache properly', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Response with audio',
        emotion: 'happy',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.sendMessage('Test message');
      });

      expect(global.URL.createObjectURL).toHaveBeenCalledWith(
        expect.any(Blob)
      );

      // Complete the audio
      act(() => {
        result.current.handleAudioComplete('nathan-msg-1');
      });

      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-audio-url');
    });

    it('should handle AI service errors', async () => {
      mockAIService.generateResponse.mockRejectedValue(new Error('AI service failed'));

      const { result } = renderHook(() => useAudioConversation());

      await expect(async () => {
        await act(async () => {
          await result.current.sendMessage('Test message');
        });
      }).rejects.toThrow('AI service failed');

      expect(result.current.audioQueue).toHaveLength(0);
    });

    it('should handle audio generation errors', async () => {
      const existingMessage = {
        id: 'existing-msg',
        content: 'Message for audio generation',
        timestamp: new Date(),
        sender: 'nathan' as const,
        emotion: 'neutral' as const
      };

      mockAIService.generateAudio.mockRejectedValue(new Error('Audio generation failed'));

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.generateAudioForMessage(existingMessage);
      });

      expect(result.current.audioQueue).toHaveLength(0);
      expect(result.current.isGeneratingAudio).toBe(false);
    });

    it('should provide access to AI service instance', () => {
      const { result } = renderHook(() => useAudioConversation());

      expect(result.current.aiService).toBeDefined();
      expect(result.current.aiService).toBeInstanceOf(Object);
    });

    it('should track audio generation state', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      // Mock a delayed response to test loading state
      mockAIService.generateResponse.mockImplementation(() => 
        new Promise(resolve => {
          setTimeout(() => {
            resolve({
              text: 'Delayed response',
              emotion: 'neutral',
              audio: mockAudioBuffer
            });
          }, 100);
        })
      );

      const { result } = renderHook(() => useAudioConversation());

      expect(result.current.isGeneratingAudio).toBe(false);

      const sendPromise = act(async () => {
        await result.current.sendMessage('Test message');
      });

      // Should be generating audio during the call
      await waitFor(() => {
        expect(result.current.isGeneratingAudio).toBe(true);
      });

      await sendPromise;

      // Should be done generating audio after completion
      expect(result.current.isGeneratingAudio).toBe(false);
    });
  });

  describe('Audio playback integration', () => {
    it('should handle playing state changes', () => {
      const { result } = renderHook(() => useAudioConversation());

      act(() => {
        result.current.handleAudioPlayingChange(true);
      });

      // Should update conversation playing state
      expect(result.current.handleAudioPlayingChange).toBeDefined();

      act(() => {
        result.current.handleAudioPlayingChange(false);
      });
    });

    it('should interrupt audio playback', () => {
      const { result } = renderHook(() => useAudioConversation());

      // Add audio to queue
      act(() => {
        result.current.audioQueue.push({
          id: 'audio-1',
          audioUrl: 'blob:mock-url',
          text: 'Audio to interrupt',
          priority: 1
        });
      });

      expect(result.current.audioQueue).toHaveLength(1);

      act(() => {
        result.current.interruptAudio();
      });

      expect(result.current.audioQueue).toHaveLength(0);
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url');
    });
  });

  describe('Memory management', () => {
    it('should clean up audio URLs on unmount', () => {
      const { result, unmount } = renderHook(() => useAudioConversation());

      // Add audio URLs to cache
      act(() => {
        result.current.audioQueue.push({
          id: 'audio-1',
          audioUrl: 'blob:mock-url-1',
          text: 'Audio 1',
          priority: 1
        });
        result.current.audioQueue.push({
          id: 'audio-2',
          audioUrl: 'blob:mock-url-2',
          text: 'Audio 2',
          priority: 1
        });
      });

      unmount();

      // Should clean up all URLs
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url-1');
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url-2');
    });

    it('should handle rapid audio queue changes', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Quick response',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Send multiple messages rapidly
      const promises = [
        act(async () => await result.current.sendMessage('Message 1')),
        act(async () => await result.current.sendMessage('Message 2')),
        act(async () => await result.current.sendMessage('Message 3'))
      ];

      await Promise.all(promises);

      // Should handle all messages and only have the last one in queue
      expect(result.current.audioQueue).toHaveLength(1);
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);
    });
  });
});