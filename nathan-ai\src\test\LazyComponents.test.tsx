import React, { Suspense } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import {
  LazyAvatar,
  LazyAudioPlayer,
  LazyVoiceInput,
  LazyTextInput,
  LazyInputController,
  LazyMessageBubble,
  LazyChatInterface,
  LazyErrorBoundary,
  LazyCompatibilityWarning,
  LazyNotificationSystem,
  LazyMobileGestureHandler
} from '../components/LazyComponents';

// Mock the actual components
vi.mock('../components/Avatar', () => ({
  default: ({ emotion }: { emotion: string }) => <div data-testid="avatar">{emotion}</div>
}));

vi.mock('../components/AudioPlayer', () => ({
  default: () => <div data-testid="audio-player">Audio Player</div>
}));

vi.mock('../components/VoiceInput', () => ({
  default: () => <div data-testid="voice-input">Voice Input</div>
}));

vi.mock('../components/TextInput', () => ({
  default: () => <div data-testid="text-input">Text Input</div>
}));

vi.mock('../components/InputController', () => ({
  default: () => <div data-testid="input-controller">Input Controller</div>
}));

vi.mock('../components/MessageBubble', () => ({
  default: () => <div data-testid="message-bubble">Message Bubble</div>
}));

vi.mock('../components/ChatInterface', () => ({
  default: () => <div data-testid="chat-interface">Chat Interface</div>
}));

vi.mock('../components/ErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="error-boundary">{children}</div>
  )
}));

vi.mock('../components/CompatibilityWarning', () => ({
  default: () => <div data-testid="compatibility-warning">Compatibility Warning</div>
}));

vi.mock('../components/NotificationSystem', () => ({
  default: () => <div data-testid="notification-system">Notification System</div>
}));

vi.mock('../components/MobileGestureHandler', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="mobile-gesture-handler">{children}</div>
  )
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<div data-testid="loading">Loading...</div>}>
    {children}
  </Suspense>
);

describe('LazyComponents', () => {
  describe('LazyAvatar', () => {
    it('should load and render Avatar component', async () => {
      render(
        <TestWrapper>
          <LazyAvatar emotion="happy" isAnimated={false} size="medium" />
        </TestWrapper>
      );

      // Should show loading initially
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Should load the actual component
      await waitFor(() => {
        expect(screen.getByTestId('avatar')).toBeInTheDocument();
        expect(screen.getByText('happy')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyAudioPlayer', () => {
    it('should load and render AudioPlayer component', async () => {
      render(
        <TestWrapper>
          <LazyAudioPlayer />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('audio-player')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyVoiceInput', () => {
    it('should load and render VoiceInput component', async () => {
      render(
        <TestWrapper>
          <LazyVoiceInput />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyTextInput', () => {
    it('should load and render TextInput component', async () => {
      render(
        <TestWrapper>
          <LazyTextInput />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('text-input')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyInputController', () => {
    it('should load and render InputController component', async () => {
      render(
        <TestWrapper>
          <LazyInputController />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('input-controller')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyMessageBubble', () => {
    it('should load and render MessageBubble component', async () => {
      render(
        <TestWrapper>
          <LazyMessageBubble />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('message-bubble')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyChatInterface', () => {
    it('should load and render ChatInterface component', async () => {
      render(
        <TestWrapper>
          <LazyChatInterface />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyErrorBoundary', () => {
    it('should load and render ErrorBoundary component', async () => {
      render(
        <TestWrapper>
          <LazyErrorBoundary>
            <div>Test content</div>
          </LazyErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
        expect(screen.getByText('Test content')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyCompatibilityWarning', () => {
    it('should load and render CompatibilityWarning component', async () => {
      render(
        <TestWrapper>
          <LazyCompatibilityWarning />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('compatibility-warning')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyNotificationSystem', () => {
    it('should load and render NotificationSystem component', async () => {
      render(
        <TestWrapper>
          <LazyNotificationSystem />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('notification-system')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('LazyMobileGestureHandler', () => {
    it('should load and render MobileGestureHandler component', async () => {
      render(
        <TestWrapper>
          <LazyMobileGestureHandler>
            <div>Test content</div>
          </LazyMobileGestureHandler>
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('mobile-gesture-handler')).toBeInTheDocument();
        expect(screen.getByText('Test content')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('Error handling', () => {
    it('should handle component loading errors gracefully', async () => {
      // Mock a component that fails to load
      const FailingComponent = React.lazy(() => Promise.reject(new Error('Load failed')));

      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <FailingComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // The error should be caught by the Suspense boundary
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).toBeInTheDocument();
      });

      consoleError.mockRestore();
    });
  });

  describe('Multiple lazy components', () => {
    it('should handle multiple lazy components loading simultaneously', async () => {
      render(
        <TestWrapper>
          <LazyAvatar emotion="neutral" isAnimated={false} size="small" />
          <LazyAudioPlayer />
          <LazyVoiceInput />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('avatar')).toBeInTheDocument();
        expect(screen.getByTestId('audio-player')).toBeInTheDocument();
        expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  describe('Props forwarding', () => {
    it('should forward props correctly to lazy components', async () => {
      render(
        <TestWrapper>
          <LazyAvatar emotion="thoughtful" isAnimated={true} size="large" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('avatar')).toBeInTheDocument();
        expect(screen.getByText('thoughtful')).toBeInTheDocument();
      });
    });
  });
});