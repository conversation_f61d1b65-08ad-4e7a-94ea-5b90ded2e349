import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  validatePersonalityConfig,
  loadDefaultPersonality,
  loadPersonalityConfig,
  savePersonalityConfig,
  updatePersonalityConfig,
  resetPersonalityConfig,
  formatPersonalityPrompt
} from '../utils/personality';
import { PersonalityConfig } from '../types/personality';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock console methods
const consoleMock = {
  warn: vi.fn(),
  error: vi.fn(),
};

Object.defineProperty(console, 'warn', {
  value: consoleMock.warn
});

Object.defineProperty(console, 'error', {
  value: consoleMock.error
});

describe('Personality System', () => {
  const validPersonalityConfig: PersonalityConfig = {
    name: 'TestBot',
    pronouns: 'they/them',
    personality: {
      tone: 'friendly and helpful',
      role: 'test assistant',
      hobbies: ['testing', 'debugging'],
      style: {
        speech: 'clear and concise',
        humor: 'light and appropriate',
        depth: 'adaptable to context'
      },
      boundaries: {
        avoid: ['harmful content'],
        safe_topics: ['technology', 'general conversation']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: false,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How can I help?']
    },
    version: '1.0.0'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    localStorageMock.setItem.mockImplementation(() => {}); // Reset to normal behavior
  });

  describe('validatePersonalityConfig', () => {
    it('should return true for valid personality config', () => {
      expect(validatePersonalityConfig(validPersonalityConfig)).toBe(true);
    });

    it('should return false for null or undefined', () => {
      expect(validatePersonalityConfig(null)).toBe(false);
      expect(validatePersonalityConfig(undefined)).toBe(false);
    });

    it('should return false for non-object input', () => {
      expect(validatePersonalityConfig('string')).toBe(false);
      expect(validatePersonalityConfig(123)).toBe(false);
      expect(validatePersonalityConfig([])).toBe(false);
    });

    it('should return false when missing required top-level properties', () => {
      const incomplete = { ...validPersonalityConfig };
      delete incomplete.name;
      expect(validatePersonalityConfig(incomplete)).toBe(false);
    });

    it('should return false when personality object is missing', () => {
      const invalid = { ...validPersonalityConfig };
      delete invalid.personality;
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });

    it('should return false when personality properties are missing', () => {
      const invalid = {
        ...validPersonalityConfig,
        personality: {
          ...validPersonalityConfig.personality
        }
      };
      delete invalid.personality.tone;
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });

    it('should return false when style object is invalid', () => {
      const invalid = {
        ...validPersonalityConfig,
        personality: {
          ...validPersonalityConfig.personality,
          style: {
            speech: 'valid',
            humor: 123, // Invalid type
            depth: 'valid'
          }
        }
      };
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });

    it('should return false when boundaries arrays are not arrays', () => {
      const invalid = {
        ...validPersonalityConfig,
        personality: {
          ...validPersonalityConfig.personality,
          boundaries: {
            avoid: 'not an array',
            safe_topics: ['valid']
          }
        }
      };
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });

    it('should return false when dynamic_traits has non-boolean values', () => {
      const invalid = {
        ...validPersonalityConfig,
        personality: {
          ...validPersonalityConfig.personality,
          dynamic_traits: {
            adaptive_empathy: 'true', // Should be boolean
            mirroring_style: false,
            emotionally_available: true
          }
        }
      };
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });

    it('should return false when conversation_tips is invalid', () => {
      const invalid = {
        ...validPersonalityConfig,
        conversation_tips: {
          starter_prompts: 'not an array'
        }
      };
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });

    it('should return false when hobbies is not an array', () => {
      const invalid = {
        ...validPersonalityConfig,
        personality: {
          ...validPersonalityConfig.personality,
          hobbies: 'not an array'
        }
      };
      expect(validatePersonalityConfig(invalid)).toBe(false);
    });
  });

  describe('loadDefaultPersonality', () => {
    it('should return a valid personality configuration', () => {
      const defaultConfig = loadDefaultPersonality();
      expect(validatePersonalityConfig(defaultConfig)).toBe(true);
      expect(defaultConfig.name).toBe('Nathan');
      expect(defaultConfig.pronouns).toBe('he/him');
    });

    it('should return consistent results', () => {
      const config1 = loadDefaultPersonality();
      const config2 = loadDefaultPersonality();
      expect(config1).toEqual(config2);
    });
  });

  describe('loadPersonalityConfig', () => {
    it('should return default config when localStorage is empty', () => {
      localStorageMock.getItem.mockReturnValue(null);
      const config = loadPersonalityConfig();
      expect(validatePersonalityConfig(config)).toBe(true);
      expect(config.name).toBe('Nathan');
    });

    it('should return stored config when valid', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify(validPersonalityConfig));
      const config = loadPersonalityConfig();
      expect(config).toEqual(validPersonalityConfig);
    });

    it('should return default config when stored config is invalid', () => {
      localStorageMock.getItem.mockReturnValue('{"invalid": "config"}');
      const config = loadPersonalityConfig();
      expect(config.name).toBe('Nathan');
      expect(consoleMock.warn).toHaveBeenCalledWith('Invalid stored personality config, using default');
    });

    it('should handle JSON parse errors gracefully', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');
      const config = loadPersonalityConfig();
      expect(config.name).toBe('Nathan');
      expect(consoleMock.warn).toHaveBeenCalledWith(
        'Error loading personality config from localStorage:',
        expect.any(Error)
      );
    });
  });

  describe('savePersonalityConfig', () => {
    it('should save valid config to localStorage', () => {
      savePersonalityConfig(validPersonalityConfig);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'nathan-personality-config',
        JSON.stringify(validPersonalityConfig, null, 2)
      );
    });

    it('should throw error for invalid config', () => {
      const invalidConfig = { invalid: 'config' } as any;
      expect(() => savePersonalityConfig(invalidConfig)).toThrow('Invalid personality configuration');
    });

    it('should handle localStorage errors', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage full');
      });
      
      expect(() => savePersonalityConfig(validPersonalityConfig)).toThrow(
        'Failed to save personality configuration: Error: Storage full'
      );
    });
  });

  describe('updatePersonalityConfig', () => {
    beforeEach(() => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify(validPersonalityConfig));
    });

    it('should update top-level properties', () => {
      const updated = updatePersonalityConfig({ name: 'UpdatedBot' });
      expect(updated.name).toBe('UpdatedBot');
      expect(updated.pronouns).toBe(validPersonalityConfig.pronouns); // Should preserve other properties
    });

    it('should deep merge personality object', () => {
      const updated = updatePersonalityConfig({
        personality: {
          tone: 'updated tone',
          style: {
            speech: 'updated speech'
          }
        } as any
      });
      
      expect(updated.personality.tone).toBe('updated tone');
      expect(updated.personality.style.speech).toBe('updated speech');
      expect(updated.personality.style.humor).toBe(validPersonalityConfig.personality.style.humor); // Should preserve
      expect(updated.personality.role).toBe(validPersonalityConfig.personality.role); // Should preserve
    });

    it('should deep merge conversation_tips', () => {
      const updated = updatePersonalityConfig({
        conversation_tips: {
          starter_prompts: ['New prompt']
        }
      });
      
      expect(updated.conversation_tips.starter_prompts).toEqual(['New prompt']);
    });

    it('should save the updated config', () => {
      updatePersonalityConfig({ name: 'UpdatedBot' });
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });
  });

  describe('resetPersonalityConfig', () => {
    it('should reset to default configuration', () => {
      const reset = resetPersonalityConfig();
      const defaultConfig = loadDefaultPersonality();
      expect(reset).toEqual(defaultConfig);
    });

    it('should save the default config to localStorage', () => {
      resetPersonalityConfig();
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });
  });

  describe('formatPersonalityPrompt', () => {
    it('should create a formatted prompt string', () => {
      const prompt = formatPersonalityPrompt(validPersonalityConfig);
      
      expect(prompt).toContain('TestBot');
      expect(prompt).toContain('they/them');
      expect(prompt).toContain('test assistant');
      expect(prompt).toContain('friendly and helpful');
      expect(prompt).toContain('testing, debugging');
      expect(prompt).toContain('clear and concise');
      expect(prompt).toContain('light and appropriate');
      expect(prompt).toContain('adaptable to context');
      expect(prompt).toContain('enabled'); // adaptive_empathy
      expect(prompt).toContain('disabled'); // mirroring_style
      expect(prompt).toContain('harmful content');
      expect(prompt).toContain('technology, general conversation');
    });

    it('should handle empty arrays gracefully', () => {
      const configWithEmptyArrays = {
        ...validPersonalityConfig,
        personality: {
          ...validPersonalityConfig.personality,
          hobbies: [],
          boundaries: {
            avoid: [],
            safe_topics: []
          }
        }
      };
      
      const prompt = formatPersonalityPrompt(configWithEmptyArrays);
      expect(prompt).toContain('Hobbies and interests: ');
      expect(prompt).toContain('Avoid topics: ');
      expect(prompt).toContain('Safe topics: ');
    });

    it('should be consistent for the same input', () => {
      const prompt1 = formatPersonalityPrompt(validPersonalityConfig);
      const prompt2 = formatPersonalityPrompt(validPersonalityConfig);
      expect(prompt1).toBe(prompt2);
    });
  });
});