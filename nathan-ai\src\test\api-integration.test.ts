import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AIService } from '../services/AIService';
import { HuggingFaceClient } from '../services/HuggingFaceClient';
import { ElevenLabsClient } from '../services/ElevenLabsClient';
import type { PersonalityConfig } from '../types/personality';

// Mock the env config
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  }
}));

// Mock the clients
vi.mock('../services/HuggingFaceClient');
vi.mock('../services/ElevenLabsClient');

describe('API Integration Tests', () => {
  let aiService: AIService;
  let mockHFClient: any;
  let mockELClient: any;
  let mockPersonality: PersonalityConfig;

  beforeEach(() => {
    // Create mock instances
    mockHFClient = {
      generatePersonalityResponse: vi.fn(),
      generateText: vi.fn()
    };
    mockELClient = {
      textToSpeech: vi.fn()
    };

    // Mock the constructors to return our mocks
    vi.mocked(HuggingFaceClient).mockImplementation(() => mockHFClient);
    vi.mocked(ElevenLabsClient).mockImplementation(() => mockELClient);

    aiService = new AIService();
    
    mockPersonality = {
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'companion',
        hobbies: ['reading'],
        style: {
          speech: 'casual',
          humor: 'light',
          depth: 'thoughtful'
        },
        boundaries: {
          avoid: [],
          safe_topics: []
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: []
      },
      version: '1.0.0'
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete conversation workflow', () => {
    it('should handle a complete conversation with personality integration', async () => {
      // Mock responses
      mockHFClient.generatePersonalityResponse
        .mockResolvedValueOnce('Hello! Nice to meet you!')
        .mockResolvedValueOnce('I\'m doing great, thanks for asking!');
      
      mockELClient.textToSpeech
        .mockResolvedValueOnce(new ArrayBuffer(512))
        .mockResolvedValueOnce(new ArrayBuffer(512));

      // First exchange
      const response1 = await aiService.generateResponse('Hi there!', mockPersonality);
      expect(response1.text).toBe('Hello! Nice to meet you!');
      expect(response1.audio).toBeInstanceOf(ArrayBuffer);
      expect(response1.emotion).toBeDefined();

      // Second exchange should include conversation history
      const response2 = await aiService.generateResponse('How are you?', mockPersonality);
      expect(response2.text).toBe('I\'m doing great, thanks for asking!');
      expect(response2.audio).toBeInstanceOf(ArrayBuffer);

      // Verify conversation history was passed
      expect(mockHFClient.generatePersonalityResponse).toHaveBeenCalledTimes(2);
      
      // Check that the second call includes history
      const secondCallArgs = mockHFClient.generatePersonalityResponse.mock.calls[1];
      const historyArg = secondCallArgs[2]; // Third argument is history
      expect(historyArg).toContain('User: Hi there!');
      expect(historyArg).toContain('Nathan: Hello! Nice to meet you!');
    });

    it('should handle conversation flow with mixed success/failure', async () => {
      // Mock HF success but EL failure
      mockHFClient.generatePersonalityResponse.mockResolvedValue('Hello there!');
      mockELClient.textToSpeech.mockRejectedValue(new Error('TTS service unavailable'));

      const response = await aiService.generateResponse('Hi', mockPersonality);
      
      // Should still get text response even if audio fails
      expect(response.text).toBe('Hello there!');
      expect(response.audio).toBeUndefined();
      expect(response.cached).toBe(false);
    });

    it('should properly cache and retrieve responses', async () => {
      mockHFClient.generatePersonalityResponse.mockResolvedValue('Cached response');
      mockELClient.textToSpeech.mockResolvedValue(new ArrayBuffer(512));

      // Clear history to ensure consistent cache key
      aiService.clearConversationHistory();

      // First call
      const response1 = await aiService.generateResponse('Test message', mockPersonality);
      expect(response1.cached).toBe(false);

      // Clear history again to match cache key
      aiService.clearConversationHistory();

      // Second call should use cache
      const response2 = await aiService.generateResponse('Test message', mockPersonality);
      expect(response2.cached).toBe(true);
      expect(response2.text).toBe('Cached response');

      // Should only call HF once
      expect(mockHFClient.generatePersonalityResponse).toHaveBeenCalledTimes(1);
    });

    it('should handle personality-specific responses', async () => {
      const customPersonality: PersonalityConfig = {
        ...mockPersonality,
        name: 'CustomBot',
        personality: {
          ...mockPersonality.personality,
          tone: 'formal',
          role: 'assistant'
        }
      };

      mockHFClient.generatePersonalityResponse.mockResolvedValue('Formal response');

      await aiService.generateResponse('Hello', customPersonality);

      // Verify personality was passed to HF client
      expect(mockHFClient.generatePersonalityResponse).toHaveBeenCalledWith(
        'Hello',
        customPersonality,
        expect.any(Array),
        undefined
      );
    });

    it('should handle service health checks', async () => {
      mockHFClient.generateText.mockResolvedValue('Health check response');
      mockELClient.textToSpeech.mockResolvedValue(new ArrayBuffer(512));

      const health = await aiService.healthCheck();

      expect(health.huggingFace).toBe(true);
      expect(health.elevenLabs).toBe(true);
      expect(health.overall).toBe(true);

      // Verify health check calls
      expect(mockHFClient.generateText).toHaveBeenCalledWith('test', { max_length: 10 });
      expect(mockELClient.textToSpeech).toHaveBeenCalledWith('test');
    });

    it('should handle conversation history limits', async () => {
      const service = new AIService({ conversationHistoryLimit: 2 });
      
      // Create new mocks for this service
      const newMockHF = {
        generatePersonalityResponse: vi.fn().mockResolvedValue('Response'),
        generateText: vi.fn()
      } as any;
      
      vi.mocked(HuggingFaceClient).mockImplementationOnce(() => newMockHF);
      vi.mocked(ElevenLabsClient).mockImplementationOnce(() => ({
        textToSpeech: vi.fn().mockResolvedValue(new ArrayBuffer(512))
      } as any));

      // Generate multiple exchanges
      for (let i = 0; i < 5; i++) {
        await service.generateTextResponse(`Message ${i}`, mockPersonality);
      }

      const history = service.getConversationHistory();
      expect(history.length).toBe(4); // 2 exchanges * 2 messages each
    });
  });

  describe('Error handling integration', () => {
    it('should gracefully handle complete service failure', async () => {
      // Create a fresh service with new mocks for this test
      const failingMockHF = {
        generatePersonalityResponse: vi.fn().mockRejectedValue(new Error('Service down')),
        generateText: vi.fn()
      } as any;
      const failingMockEL = {
        textToSpeech: vi.fn()
      } as any;
      
      vi.mocked(HuggingFaceClient).mockImplementationOnce(() => failingMockHF);
      vi.mocked(ElevenLabsClient).mockImplementationOnce(() => failingMockEL);
      
      const failingService = new AIService();

      await expect(
        failingService.generateResponse('Hello', mockPersonality)
      ).rejects.toThrow('Failed to generate response: Service down');
    });

    it('should handle partial service failure', async () => {
      mockHFClient.generatePersonalityResponse.mockResolvedValue('Text response');
      mockELClient.textToSpeech.mockRejectedValue(new Error('Audio service down'));

      const response = await aiService.generateResponse('Hello', mockPersonality);
      
      expect(response.text).toBe('Text response');
      expect(response.audio).toBeUndefined();
    });
  });

  describe('Performance and optimization', () => {
    it('should handle concurrent requests', async () => {
      mockHFClient.generatePersonalityResponse
        .mockResolvedValueOnce('Response 1')
        .mockResolvedValueOnce('Response 2')
        .mockResolvedValueOnce('Response 3');
      
      mockELClient.textToSpeech.mockResolvedValue(new ArrayBuffer(512));

      // Make concurrent requests
      const promises = [
        aiService.generateResponse('Message 1', mockPersonality),
        aiService.generateResponse('Message 2', mockPersonality),
        aiService.generateResponse('Message 3', mockPersonality)
      ];

      const responses = await Promise.all(promises);

      expect(responses).toHaveLength(3);
      expect(responses[0].text).toBe('Response 1');
      expect(responses[1].text).toBe('Response 2');
      expect(responses[2].text).toBe('Response 3');
    });

    it('should handle cache management', async () => {
      const service = new AIService({ maxCacheSize: 2 });
      
      // Create new mocks
      const newMockHF = {
        generatePersonalityResponse: vi.fn()
          .mockResolvedValueOnce('Response 1')
          .mockResolvedValueOnce('Response 2')
          .mockResolvedValueOnce('Response 3'),
        generateText: vi.fn()
      } as any;
      
      vi.mocked(HuggingFaceClient).mockImplementationOnce(() => newMockHF);
      vi.mocked(ElevenLabsClient).mockImplementationOnce(() => ({
        textToSpeech: vi.fn().mockResolvedValue(new ArrayBuffer(512))
      } as any));

      // Generate responses to fill cache
      await service.generateResponse('Message 1', mockPersonality);
      await service.generateResponse('Message 2', mockPersonality);
      await service.generateResponse('Message 3', mockPersonality);

      const stats = service.getCacheStats();
      expect(stats.size).toBeLessThanOrEqual(2);
      expect(stats.maxSize).toBe(2);
    });
  });
});