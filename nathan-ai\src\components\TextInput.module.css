.textInputContainer {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.inputWrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 12px;
  background: #ffffff;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  transition: border-color 0.2s ease;
}

.inputWrapper:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.textarea {
  flex: 1;
  min-height: 20px;
  max-height: 120px;
  padding: 0;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 16px;
  line-height: 1.5;
  background: transparent;
  overflow-y: auto;
}

.textarea::placeholder {
  color: #9ca3af;
}

.textarea.disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.sendButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  border: none;
  border-radius: 8px;
  background: #4f46e5;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.sendButton:hover:not(.disabled) {
  background: #4338ca;
  transform: translateY(-1px);
}

.sendButton:active:not(.disabled) {
  transform: translateY(0);
}

.sendButton.disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

.sendIcon {
  width: 20px;
  height: 20px;
}

.loadingSpinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 4px 8px 0;
}

.charCount {
  font-size: 12px;
  color: #6b7280;
  transition: color 0.2s ease;
}

.charCount.warning {
  color: #f59e0b;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .textInputContainer {
    max-width: 100%;
  }
  
  .inputWrapper {
    padding: 12px;
    border-radius: 16px;
    /* Safe area support */
    margin-left: max(0px, env(safe-area-inset-left));
    margin-right: max(0px, env(safe-area-inset-right));
  }
  
  .textarea {
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 24px;
    max-height: 100px; /* Limit height on mobile */
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
  }
  
  .sendButton {
    width: 44px; /* Touch target size */
    height: 44px;
    border-radius: 12px;
    /* Touch feedback */
    -webkit-tap-highlight-color: transparent;
  }
  
  .sendIcon {
    width: 20px;
    height: 20px;
  }
  
  .footer {
    padding: 4px 12px 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inputWrapper {
    background: #1f2937;
    border-color: #374151;
  }
  
  .inputWrapper:focus-within {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  .textarea {
    color: #f9fafb;
  }
  
  .textarea::placeholder {
    color: #6b7280;
  }
  
  .charCount {
    color: #9ca3af;
  }
}@m
edia (max-width: 480px) {
  .inputWrapper {
    padding: 10px;
    border-radius: 14px;
  }
  
  .textarea {
    font-size: 16px;
    min-height: 20px;
    max-height: 80px;
  }
  
  .sendButton {
    width: 40px;
    height: 40px;
    border-radius: 10px;
  }
  
  .sendIcon {
    width: 18px;
    height: 18px;
  }
  
  .footer {
    padding: 2px 10px 0;
  }
  
  .charCount {
    font-size: 11px;
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 500px) {
  .inputWrapper {
    padding: 8px;
  }
  
  .textarea {
    max-height: 60px;
    font-size: 14px;
  }
  
  .sendButton {
    width: 36px;
    height: 36px;
  }
  
  .sendIcon {
    width: 16px;
    height: 16px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .sendButton {
    /* Remove hover effects */
    transition: background-color 0.2s ease, transform 0.1s ease;
  }
  
  .sendButton:hover:not(.disabled) {
    transform: none;
  }
  
  .sendButton:active:not(.disabled) {
    transform: scale(0.95);
    background: #3730a3;
  }
  
  .textarea {
    /* Improve touch interaction */
    touch-action: manipulation;
    -webkit-user-select: text;
    user-select: text;
  }
}

/* Keyboard Adaptation */
@media (max-width: 768px) {
  .textInputContainer {
    /* Adjust for virtual keyboard */
    transition: margin-bottom 0.3s ease;
  }
  
  /* When keyboard is open */
  .textInputContainer[data-keyboard-open="true"] {
    margin-bottom: calc(var(--keyboard-height, 0px) * 0.1);
  }
}

/* Accessibility Improvements */
.textarea:focus {
  outline: none; /* Handled by inputWrapper */
}

.inputWrapper:focus-within {
  outline: 2px solid var(--primary-color, #4f46e5);
  outline-offset: -2px;
}

.sendButton:focus {
  outline: 2px solid var(--primary-color, #4f46e5);
  outline-offset: 2px;
}

/* Performance Optimizations */
@media (max-width: 768px) {
  .sendButton {
    /* Enable hardware acceleration */
    transform: translateZ(0);
  }
  
  .loadingSpinner {
    will-change: transform;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .inputWrapper {
    border-width: 1px;
  }
}

/* Prevent zoom on input focus (iOS) */
@media (max-width: 768px) {
  .textarea {
    font-size: 16px !important; /* Force prevent zoom */
  }
}

/* Auto-resize behavior improvement */
@media (max-width: 768px) {
  .textarea {
    resize: none;
    overflow-y: auto;
    /* Smooth scrolling */
    scroll-behavior: smooth;
  }
}