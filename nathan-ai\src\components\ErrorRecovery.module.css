.errorRecovery {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 200px;
}

.content {
  max-width: 500px;
  text-align: center;
  padding: 2rem;
  background: var(--surface-color, white);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--warning-color, #ffc107);
}

.icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.title {
  color: var(--text-color, #333);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.message {
  color: var(--text-secondary, #666);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.details {
  margin: 1rem 0;
  text-align: left;
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 4px;
}

.details summary {
  cursor: pointer;
  padding: 0.75rem;
  background-color: var(--background-color, #f8f9fa);
  font-weight: 500;
  user-select: none;
}

.details summary:hover {
  background-color: var(--hover-color, #e9ecef);
}

.errorText {
  padding: 1rem;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  background-color: var(--code-background, #f8f9fa);
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton,
.secondaryButton,
.tertiaryButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.primaryButton {
  background-color: var(--primary-color, #007bff);
  color: white;
}

.primaryButton:hover {
  background-color: var(--primary-hover, #0056b3);
}

.secondaryButton {
  background-color: var(--secondary-color, #6c757d);
  color: white;
}

.secondaryButton:hover {
  background-color: var(--secondary-hover, #545b62);
}

.tertiaryButton {
  background-color: transparent;
  color: var(--text-color, #333);
  border: 1px solid var(--border-color, #dee2e6);
}

.tertiaryButton:hover {
  background-color: var(--background-color, #f8f9fa);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .errorRecovery {
    padding: 1rem;
  }
  
  .content {
    padding: 1.5rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .primaryButton,
  .secondaryButton,
  .tertiaryButton {
    width: 100%;
  }
}