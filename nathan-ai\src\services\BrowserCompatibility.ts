export interface BrowserFeatures {
  webSpeechAPI: boolean;
  audioAPI: boolean;
  mediaDevices: boolean;
  notifications: boolean;
  localStorage: boolean;
  webAudio: boolean;
  getUserMedia: boolean;
}

export interface BrowserInfo {
  name: string;
  version: string;
  isMobile: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isChrome: boolean;
  isFirefox: boolean;
  isSafari: boolean;
  isEdge: boolean;
}

export interface CompatibilityIssue {
  feature: keyof BrowserFeatures;
  severity: 'critical' | 'warning' | 'info';
  message: string;
  fallback?: string;
  guidance?: string;
}

export class BrowserCompatibility {
  private features: BrowserFeatures;
  private browserInfo: BrowserInfo;
  private issues: CompatibilityIssue[] = [];

  constructor() {
    this.browserInfo = this.detectBrowser();
    this.features = this.detectFeatures();
    this.analyzeCompatibility();
  }

  /**
   * Detect browser information
   */
  private detectBrowser(): BrowserInfo {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    
    let name = 'Unknown';
    let version = 'Unknown';
    
    // Chrome
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      name = 'Chrome';
      const match = userAgent.match(/Chrome\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    }
    // Edge
    else if (userAgent.includes('Edg')) {
      name = 'Edge';
      const match = userAgent.match(/Edg\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    }
    // Firefox
    else if (userAgent.includes('Firefox')) {
      name = 'Firefox';
      const match = userAgent.match(/Firefox\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    }
    // Safari
    else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      name = 'Safari';
      const match = userAgent.match(/Version\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    }

    return {
      name,
      version,
      isMobile,
      isIOS: /iPad|iPhone|iPod/.test(userAgent),
      isAndroid: /Android/.test(userAgent),
      isChrome: name === 'Chrome',
      isFirefox: name === 'Firefox',
      isSafari: name === 'Safari',
      isEdge: name === 'Edge'
    };
  }

  /**
   * Detect available browser features
   */
  private detectFeatures(): BrowserFeatures {
    return {
      webSpeechAPI: this.checkWebSpeechAPI(),
      audioAPI: this.checkAudioAPI(),
      mediaDevices: this.checkMediaDevices(),
      notifications: this.checkNotifications(),
      localStorage: this.checkLocalStorage(),
      webAudio: this.checkWebAudio(),
      getUserMedia: this.checkGetUserMedia()
    };
  }

  /**
   * Check Web Speech API support
   */
  private checkWebSpeechAPI(): boolean {
    return !!(
      (window as any).SpeechRecognition ||
      (window as any).webkitSpeechRecognition ||
      (window as any).mozSpeechRecognition ||
      (window as any).msSpeechRecognition
    );
  }

  /**
   * Check Audio API support
   */
  private checkAudioAPI(): boolean {
    return !!(window.Audio && typeof window.Audio === 'function');
  }

  /**
   * Check MediaDevices API support
   */
  private checkMediaDevices(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  /**
   * Check Notifications API support
   */
  private checkNotifications(): boolean {
    return !!(window.Notification && typeof window.Notification === 'function');
  }

  /**
   * Check localStorage support
   */
  private checkLocalStorage(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * Check Web Audio API support
   */
  private checkWebAudio(): boolean {
    return !!(
      (window as any).AudioContext ||
      (window as any).webkitAudioContext ||
      (window as any).mozAudioContext ||
      (window as any).msAudioContext
    );
  }

  /**
   * Check getUserMedia support
   */
  private checkGetUserMedia(): boolean {
    return !!(
      navigator.mediaDevices?.getUserMedia ||
      (navigator as any).getUserMedia ||
      (navigator as any).webkitGetUserMedia ||
      (navigator as any).mozGetUserMedia ||
      (navigator as any).msGetUserMedia
    );
  }

  /**
   * Analyze compatibility and identify issues
   */
  private analyzeCompatibility(): void {
    this.issues = [];

    // Web Speech API issues
    if (!this.features.webSpeechAPI) {
      this.issues.push({
        feature: 'webSpeechAPI',
        severity: 'warning',
        message: 'Voice input is not supported in this browser',
        fallback: 'Text input will be used instead',
        guidance: this.browserInfo.isFirefox 
          ? 'Firefox has limited Web Speech API support. Try Chrome or Edge for voice features.'
          : 'Try updating your browser or switching to Chrome, Edge, or Safari for voice features.'
      });
    }

    // Audio API issues
    if (!this.features.audioAPI) {
      this.issues.push({
        feature: 'audioAPI',
        severity: 'critical',
        message: 'Audio playback is not supported',
        fallback: 'Text responses only',
        guidance: 'Your browser does not support audio playback. Please update your browser.'
      });
    }

    // Web Audio API issues (for advanced audio processing)
    if (!this.features.webAudio) {
      this.issues.push({
        feature: 'webAudio',
        severity: 'info',
        message: 'Advanced audio features are not available',
        fallback: 'Basic audio playback will be used',
        guidance: 'Some audio processing features may not work optimally.'
      });
    }

    // Mobile-specific issues
    if (this.browserInfo.isMobile) {
      if (this.browserInfo.isIOS) {
        this.issues.push({
          feature: 'audioAPI',
          severity: 'info',
          message: 'iOS requires user interaction to play audio',
          fallback: 'Audio will play after first user interaction',
          guidance: 'Tap anywhere on the screen to enable audio playback.'
        });
      }

      if (!this.features.webSpeechAPI && this.browserInfo.isAndroid) {
        this.issues.push({
          feature: 'webSpeechAPI',
          severity: 'warning',
          message: 'Voice input may not work on older Android browsers',
          fallback: 'Use text input instead',
          guidance: 'Try using Chrome on Android for better voice support.'
        });
      }
    }

    // localStorage issues
    if (!this.features.localStorage) {
      this.issues.push({
        feature: 'localStorage',
        severity: 'warning',
        message: 'Settings cannot be saved between sessions',
        fallback: 'Settings will reset when page is refreshed',
        guidance: 'Enable cookies and local storage in your browser settings.'
      });
    }

    // MediaDevices issues
    if (!this.features.mediaDevices) {
      this.issues.push({
        feature: 'mediaDevices',
        severity: 'warning',
        message: 'Microphone access may not work properly',
        fallback: 'Voice input may be limited',
        guidance: 'Ensure your browser supports modern media APIs and has microphone permissions.'
      });
    }

    // Browser-specific guidance
    if (this.browserInfo.isSafari && parseInt(this.browserInfo.version) < 14) {
      this.issues.push({
        feature: 'webSpeechAPI',
        severity: 'warning',
        message: 'Older Safari versions have limited voice support',
        fallback: 'Update Safari for better compatibility',
        guidance: 'Please update to Safari 14 or later for full voice features.'
      });
    }

    if (this.browserInfo.isFirefox) {
      this.issues.push({
        feature: 'webSpeechAPI',
        severity: 'info',
        message: 'Firefox has experimental Web Speech API support',
        fallback: 'Voice features may be limited',
        guidance: 'Enable speech recognition in Firefox settings or use Chrome for better voice support.'
      });
    }
  }

  /**
   * Get browser information
   */
  getBrowserInfo(): BrowserInfo {
    return { ...this.browserInfo };
  }

  /**
   * Get feature support status
   */
  getFeatures(): BrowserFeatures {
    return { ...this.features };
  }

  /**
   * Get compatibility issues
   */
  getIssues(): CompatibilityIssue[] {
    return [...this.issues];
  }

  /**
   * Get issues by severity
   */
  getIssuesBySeverity(severity: 'critical' | 'warning' | 'info'): CompatibilityIssue[] {
    return this.issues.filter(issue => issue.severity === severity);
  }

  /**
   * Check if a specific feature is supported
   */
  isFeatureSupported(feature: keyof BrowserFeatures): boolean {
    return this.features[feature];
  }

  /**
   * Get fallback recommendations for unsupported features
   */
  getFallbackRecommendations(): string[] {
    return this.issues
      .filter(issue => issue.fallback)
      .map(issue => issue.fallback!);
  }

  /**
   * Get user guidance messages
   */
  getUserGuidance(): string[] {
    return this.issues
      .filter(issue => issue.guidance)
      .map(issue => issue.guidance!);
  }

  /**
   * Check if browser is generally compatible
   */
  isCompatible(): boolean {
    const criticalIssues = this.getIssuesBySeverity('critical');
    return criticalIssues.length === 0;
  }

  /**
   * Get compatibility score (0-100)
   */
  getCompatibilityScore(): number {
    const totalFeatures = Object.keys(this.features).length;
    const supportedFeatures = Object.values(this.features).filter(Boolean).length;
    return Math.round((supportedFeatures / totalFeatures) * 100);
  }

  /**
   * Get recommended browser message
   */
  getRecommendedBrowserMessage(): string | null {
    const score = this.getCompatibilityScore();
    
    if (score < 50) {
      return 'For the best experience, please use a modern browser like Chrome, Firefox, Safari, or Edge.';
    }
    
    if (score < 80) {
      if (this.browserInfo.isFirefox) {
        return 'For full voice features, consider using Chrome or Edge.';
      }
      if (this.browserInfo.isSafari && parseInt(this.browserInfo.version) < 14) {
        return 'Please update Safari to the latest version for better compatibility.';
      }
      return 'Some features may be limited. Consider updating your browser.';
    }
    
    return null;
  }

  /**
   * Request permissions for features that need them
   */
  async requestPermissions(): Promise<{
    microphone: boolean;
    notifications: boolean;
  }> {
    const results = {
      microphone: false,
      notifications: false
    };

    // Request microphone permission
    if (this.features.mediaDevices) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach(track => track.stop());
        results.microphone = true;
      } catch (error) {
        console.warn('Microphone permission denied:', error);
      }
    }

    // Request notification permission
    if (this.features.notifications) {
      try {
        const permission = await Notification.requestPermission();
        results.notifications = permission === 'granted';
      } catch (error) {
        console.warn('Notification permission request failed:', error);
      }
    }

    return results;
  }

  /**
   * Get mobile-specific optimizations
   */
  getMobileOptimizations(): string[] {
    if (!this.browserInfo.isMobile) {
      return [];
    }

    const optimizations = [
      'Touch interactions are optimized for mobile',
      'Text input will auto-resize for better mobile experience'
    ];

    if (this.browserInfo.isIOS) {
      optimizations.push('Audio playback requires user interaction on iOS');
      optimizations.push('Voice input may require additional permissions on iOS');
    }

    if (this.browserInfo.isAndroid) {
      optimizations.push('Voice input works best in Chrome on Android');
    }

    return optimizations;
  }

  /**
   * Create a Web Speech API instance with fallbacks
   */
  createSpeechRecognition(): any | null {
    if (!this.features.webSpeechAPI) {
      return null;
    }

    const SpeechRecognition = 
      (window as any).SpeechRecognition ||
      (window as any).webkitSpeechRecognition ||
      (window as any).mozSpeechRecognition ||
      (window as any).msSpeechRecognition;

    if (!SpeechRecognition) {
      return null;
    }

    const recognition = new SpeechRecognition();
    
    // Apply browser-specific optimizations
    if (this.browserInfo.isChrome || this.browserInfo.isEdge) {
      recognition.continuous = true;
      recognition.interimResults = true;
    }
    
    if (this.browserInfo.isMobile) {
      // Mobile optimizations
      recognition.maxAlternatives = 1;
    }

    return recognition;
  }

  /**
   * Create an Audio Context with fallbacks
   */
  createAudioContext(): AudioContext | null {
    if (!this.features.webAudio) {
      return null;
    }

    const AudioContext = 
      (window as any).AudioContext ||
      (window as any).webkitAudioContext ||
      (window as any).mozAudioContext ||
      (window as any).msAudioContext;

    if (!AudioContext) {
      return null;
    }

    try {
      return new AudioContext();
    } catch (error) {
      console.warn('Failed to create AudioContext:', error);
      return null;
    }
  }
}