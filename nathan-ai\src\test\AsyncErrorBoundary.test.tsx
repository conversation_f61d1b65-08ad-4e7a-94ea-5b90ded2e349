import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { AsyncErrorBoundary } from '../components/AsyncErrorBoundary';

// Component that works normally
const WorkingComponent = () => <div>Component working</div>;

// Component that creates an unhandled promise rejection
const AsyncErrorComponent = ({ shouldThrow }: { shouldThrow: boolean }) => {
  React.useEffect(() => {
    if (shouldThrow) {
      // Create an unhandled promise rejection
      Promise.reject(new Error('Async error'));
    }
  }, [shouldThrow]);

  return <div>Async component</div>;
};

describe('AsyncErrorBoundary Component', () => {
  let originalAddEventListener: typeof window.addEventListener;
  let originalRemoveEventListener: typeof window.removeEventListener;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'group').mockImplementation(() => {});
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {});

    // Store original methods
    originalAddEventListener = window.addEventListener;
    originalRemoveEventListener = window.removeEventListener;

    // Mock event listeners
    window.addEventListener = vi.fn();
    window.removeEventListener = vi.fn();
  });

  afterEach(() => {
    // Restore original methods
    window.addEventListener = originalAddEventListener;
    window.removeEventListener = originalRemoveEventListener;
  });

  it('renders children when there is no error', () => {
    render(
      <AsyncErrorBoundary>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    expect(screen.getByText('Component working')).toBeInTheDocument();
  });

  it('sets up unhandled rejection and error listeners', () => {
    render(
      <AsyncErrorBoundary>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    expect(window.addEventListener).toHaveBeenCalledWith(
      'unhandledrejection',
      expect.any(Function)
    );
    expect(window.addEventListener).toHaveBeenCalledWith(
      'error',
      expect.any(Function)
    );
  });

  it('removes event listeners on unmount', () => {
    const { unmount } = render(
      <AsyncErrorBoundary>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    unmount();

    expect(window.removeEventListener).toHaveBeenCalledWith(
      'unhandledrejection',
      expect.any(Function)
    );
    expect(window.removeEventListener).toHaveBeenCalledWith(
      'error',
      expect.any(Function)
    );
  });

  it('calls onAsyncError when provided', async () => {
    const onAsyncErrorMock = vi.fn();

    // Mock the event listener to simulate an unhandled rejection
    const mockAddEventListener = vi.fn((event, handler) => {
      if (event === 'unhandledrejection') {
        // Simulate an unhandled rejection
        setTimeout(() => {
          const mockEvent = {
            reason: new Error('Test async error'),
            preventDefault: vi.fn(),
          };
          handler(mockEvent);
        }, 0);
      }
    });

    window.addEventListener = mockAddEventListener;

    render(
      <AsyncErrorBoundary onAsyncError={onAsyncErrorMock}>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    await waitFor(() => {
      expect(onAsyncErrorMock).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  it('handles promise rejection events correctly', async () => {
    const consoleSpy = vi.spyOn(console, 'error');

    // Mock the event listener to simulate an unhandled rejection
    const mockAddEventListener = vi.fn((event, handler) => {
      if (event === 'unhandledrejection') {
        setTimeout(() => {
          const mockEvent = {
            reason: new Error('Promise rejection error'),
            preventDefault: vi.fn(),
          };
          handler(mockEvent);
        }, 0);
      }
    });

    window.addEventListener = mockAddEventListener;

    render(
      <AsyncErrorBoundary>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Unhandled promise rejection:',
        expect.any(Error)
      );
    });
  });

  it('handles error events correctly', async () => {
    const consoleSpy = vi.spyOn(console, 'error');

    // Mock the event listener to simulate an error event
    const mockAddEventListener = vi.fn((event, handler) => {
      if (event === 'error') {
        setTimeout(() => {
          const mockEvent = {
            error: new Error('Global error'),
            message: 'Global error',
          };
          handler(mockEvent);
        }, 0);
      }
    });

    window.addEventListener = mockAddEventListener;

    render(
      <AsyncErrorBoundary>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Unhandled error:',
        expect.any(Error)
      );
    });
  });

  it('converts non-Error reasons to Error objects', async () => {
    const onAsyncErrorMock = vi.fn();

    // Mock the event listener to simulate an unhandled rejection with string reason
    const mockAddEventListener = vi.fn((event, handler) => {
      if (event === 'unhandledrejection') {
        setTimeout(() => {
          const mockEvent = {
            reason: 'String error reason',
            preventDefault: vi.fn(),
          };
          handler(mockEvent);
        }, 0);
      }
    });

    window.addEventListener = mockAddEventListener;

    render(
      <AsyncErrorBoundary onAsyncError={onAsyncErrorMock}>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    await waitFor(() => {
      expect(onAsyncErrorMock).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'String error reason',
        })
      );
    });
  });

  it('prevents default behavior on unhandled rejections', async () => {
    const preventDefaultMock = vi.fn();

    // Mock the event listener to simulate an unhandled rejection
    const mockAddEventListener = vi.fn((event, handler) => {
      if (event === 'unhandledrejection') {
        setTimeout(() => {
          const mockEvent = {
            reason: new Error('Test error'),
            preventDefault: preventDefaultMock,
          };
          handler(mockEvent);
        }, 0);
      }
    });

    window.addEventListener = mockAddEventListener;

    render(
      <AsyncErrorBoundary>
        <WorkingComponent />
      </AsyncErrorBoundary>
    );

    await waitFor(() => {
      expect(preventDefaultMock).toHaveBeenCalled();
    });
  });
});