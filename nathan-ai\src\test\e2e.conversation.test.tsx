import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { ChatInterface } from '../components/ChatInterface';
import { ConversationProvider, PersonalityProvider } from '../context';
import { AIService } from '../services/AIService';
import type { PersonalityConfig } from '../types/personality';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  },
  getEnvVar: vi.fn().mockImplementation((key: string, defaultValue?: string) => {
    const mockEnv: Record<string, string> = {
      'VITE_HUGGING_FACE_API_KEY': 'test-hf-key',
      'VITE_ELEVEN_LABS_API_KEY': 'test-el-key',
      'VITE_ELEVEN_LABS_VOICE_ID': 'test-voice-id'
    };
    return mockEnv[key] || defaultValue || '';
  })
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock Web Speech API
const mockSpeechRecognition = {
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: 'en-US',
  onstart: null,
  onend: null,
  onresult: null,
  onerror: null
};

Object.defineProperty(window, 'SpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  value: vi.fn(() => mockSpeechRecognition),
  writable: true
});

// Mock Audio API
const mockAudio = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 10,
  paused: true,
  ended: false,
  volume: 1,
  src: ''
};

Object.defineProperty(window, 'Audio', {
  value: vi.fn(() => mockAudio),
  writable: true
});

// Mock URL methods
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-audio-url');
global.URL.revokeObjectURL = vi.fn();

// Mock personality utilities
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn(),
  savePersonality: vi.fn(),
  validatePersonality: vi.fn().mockReturnValue({ isValid: true, errors: [] }),
  createDefaultPersonality: vi.fn().mockReturnValue({
    name: 'Nathan',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['technology', 'learning'],
      style: {
        speech: 'natural',
        humor: 'light',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: ['harmful content'],
        safe_topics: ['technology', 'general conversation']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How can I help?']
    },
    version: '1.0.0'
  })
}));

function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <PersonalityProvider>
      <ConversationProvider>
        {children}
      </ConversationProvider>
    </PersonalityProvider>
  );
}

describe('End-to-End Conversation Tests', () => {
  let mockAIService: any;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(async () => {
    vi.clearAllMocks();
    user = userEvent.setup();
    
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
      healthCheck: vi.fn().mockResolvedValue({
        online: true,
        huggingFace: true,
        elevenLabs: true,
        overall: true
      })
    };
    
    vi.mocked(AIService).mockImplementation(() => mockAIService);

    // Setup personality mock
    const { loadPersonality, savePersonality } = await import('../utils/personality');
    vi.mocked(loadPersonality).mockResolvedValue({
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'AI companion',
        hobbies: ['technology', 'learning', 'helping others'],
        style: {
          speech: 'natural and conversational',
          humor: 'light and appropriate',
          depth: 'thoughtful and engaging'
        },
        boundaries: {
          avoid: ['harmful content', 'inappropriate topics'],
          safe_topics: ['technology', 'general conversation', 'learning']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: [
          'Hello! How are you doing today?',
          'What would you like to talk about?',
          'I\'m here to help with anything you need!'
        ]
      },
      version: '1.0.0'
    });
    vi.mocked(savePersonality).mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Text Conversation Scenarios', () => {
    it('should handle a complete text conversation from start to finish', async () => {
      // Mock conversation responses
      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'Hello! I\'m Nathan, your AI companion. How are you doing today?',
          emotion: 'happy',
          audio: new ArrayBuffer(512)
        })
        .mockResolvedValueOnce({
          text: 'That\'s wonderful to hear! I\'m here to help with anything you need. What would you like to talk about?',
          emotion: 'enthusiastic',
          audio: new ArrayBuffer(512)
        })
        .mockResolvedValueOnce({
          text: 'I\'d be happy to help you learn about React! It\'s a powerful JavaScript library for building user interfaces. What specific aspect would you like to explore?',
          emotion: 'helpful',
          audio: new ArrayBuffer(512)
        });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      // Wait for component to initialize
      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should show initial state
      expect(screen.getByTestId('message-list')).toBeInTheDocument();
      expect(screen.getByTestId('input-controller')).toBeInTheDocument();

      // First exchange - greeting
      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Hello Nathan!');
      await user.click(sendButton);

      // Wait for user message to appear
      await waitFor(() => {
        expect(screen.getByText('Hello Nathan!')).toBeInTheDocument();
      });

      // Wait for Nathan's response
      await waitFor(() => {
        expect(screen.getByText(/Hello! I'm Nathan, your AI companion/)).toBeInTheDocument();
      });

      // Verify AI service was called with personality
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Hello Nathan!',
        expect.objectContaining({
          name: 'Nathan',
          personality: expect.objectContaining({
            tone: 'friendly',
            role: 'AI companion'
          })
        })
      );

      // Second exchange - positive response
      await user.type(textInput, 'I\'m doing great, thanks!');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('I\'m doing great, thanks!')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/That's wonderful to hear!/)).toBeInTheDocument();
      });

      // Third exchange - asking for help
      await user.type(textInput, 'Can you help me learn about React?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Can you help me learn about React?')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/I'd be happy to help you learn about React!/)).toBeInTheDocument();
      });

      // Verify conversation history is maintained
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);
      
      // Check that all messages are visible in the conversation
      expect(screen.getByText('Hello Nathan!')).toBeInTheDocument();
      expect(screen.getByText(/Hello! I'm Nathan, your AI companion/)).toBeInTheDocument();
      expect(screen.getByText('I\'m doing great, thanks!')).toBeInTheDocument();
      expect(screen.getByText(/That's wonderful to hear!/)).toBeInTheDocument();
      expect(screen.getByText('Can you help me learn about React?')).toBeInTheDocument();
      expect(screen.getByText(/I'd be happy to help you learn about React!/)).toBeInTheDocument();

      // Verify message count
      const messages = screen.getAllByTestId(/message-bubble/);
      expect(messages).toHaveLength(6); // 3 user + 3 Nathan messages
    });

    it('should handle conversation with audio responses', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'I can speak as well as type! This message should have audio.',
        emotion: 'happy',
        audio: mockAudioBuffer
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Can you speak to me?');
      await user.click(sendButton);

      // Wait for response with audio
      await waitFor(() => {
        expect(screen.getByText(/I can speak as well as type!/)).toBeInTheDocument();
      });

      // Verify audio was created and played
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(expect.any(Blob));
      expect(mockAudio.play).toHaveBeenCalled();

      // Should show audio controls
      expect(screen.getByTestId('audio-player')).toBeInTheDocument();
    });

    it('should handle conversation errors gracefully', async () => {
      mockAIService.generateResponse
        .mockRejectedValueOnce(new Error('Service temporarily unavailable'))
        .mockResolvedValueOnce({
          text: 'Sorry about that! I\'m back online now. How can I help you?',
          emotion: 'apologetic',
          audio: new ArrayBuffer(512)
        });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // First message fails
      await user.type(textInput, 'Hello Nathan!');
      await user.click(sendButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/service temporarily unavailable/i)).toBeInTheDocument();
      });

      // Should show retry button
      const retryButton = screen.getByTestId('retry-button');
      expect(retryButton).toBeInTheDocument();

      // Retry should work
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText(/Sorry about that! I'm back online now/)).toBeInTheDocument();
      });

      // Error message should be cleared
      expect(screen.queryByText(/service temporarily unavailable/i)).not.toBeInTheDocument();
    });
  });

  describe('Voice Input Conversation Scenarios', () => {
    it('should handle complete voice conversation workflow', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'I heard your voice message clearly! Voice interaction is working perfectly.',
        emotion: 'excited',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      await waitFor(() => {
        expect(screen.getByTestId('voice-input')).toBeInTheDocument();
      });

      // Start voice recording
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      expect(mockSpeechRecognition.start).toHaveBeenCalled();

      // Should show listening indicator
      expect(screen.getByTestId('listening-indicator')).toBeInTheDocument();

      // Simulate speech recognition
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'Hello Nathan, this is a voice message' },
            isFinal: true
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
      });

      // Simulate recognition end
      act(() => {
        mockSpeechRecognition.onend?.();
      });

      // Should process voice input and show response
      await waitFor(() => {
        expect(screen.getByText('Hello Nathan, this is a voice message')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/I heard your voice message clearly!/)).toBeInTheDocument();
      });

      // Should play audio response
      expect(mockAudio.play).toHaveBeenCalled();

      // Listening indicator should disappear
      expect(screen.queryByTestId('listening-indicator')).not.toBeInTheDocument();
    });

    it('should handle voice recognition errors and fallback to text', async () => {
      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Switch to voice mode
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      // Start voice recording
      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Simulate microphone permission denied
      act(() => {
        const mockError = { error: 'not-allowed' };
        mockSpeechRecognition.onerror?.(mockError);
      });

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/microphone access denied/i)).toBeInTheDocument();
      });

      // Should automatically switch to text mode
      await waitFor(() => {
        expect(screen.getByTestId('text-input')).toBeInTheDocument();
      });

      // Should show guidance message
      expect(screen.getByText(/switched to text input/i)).toBeInTheDocument();

      // Text input should still work
      mockAIService.generateResponse.mockResolvedValue({
        text: 'No problem! Text input works great too.',
        emotion: 'helpful',
        audio: new ArrayBuffer(512)
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Switching to text input');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/No problem! Text input works great too/)).toBeInTheDocument();
      });
    });

    it('should handle mixed voice and text conversation', async () => {
      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'I heard your voice message! Feel free to switch between voice and text.',
          emotion: 'happy',
          audio: new ArrayBuffer(512)
        })
        .mockResolvedValueOnce({
          text: 'Text messages work perfectly too! I can handle both input methods.',
          emotion: 'helpful',
          audio: new ArrayBuffer(512)
        });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Start with voice input
      const voiceToggle = screen.getByTestId('voice-toggle');
      await user.click(voiceToggle);

      const micButton = screen.getByTestId('mic-button');
      await user.click(micButton);

      // Send voice message
      act(() => {
        const mockEvent = {
          results: [{
            0: { transcript: 'This is a voice message' },
            isFinal: true
          }]
        };
        mockSpeechRecognition.onresult?.(mockEvent);
        mockSpeechRecognition.onend?.();
      });

      await waitFor(() => {
        expect(screen.getByText('This is a voice message')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/I heard your voice message!/)).toBeInTheDocument();
      });

      // Switch to text input
      const textToggle = screen.getByTestId('text-toggle');
      await user.click(textToggle);

      await waitFor(() => {
        expect(screen.getByTestId('text-input')).toBeInTheDocument();
      });

      // Send text message
      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Now using text input');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Now using text input')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/Text messages work perfectly too!/)).toBeInTheDocument();
      });

      // Both messages should be visible in conversation history
      expect(screen.getByText('This is a voice message')).toBeInTheDocument();
      expect(screen.getByText('Now using text input')).toBeInTheDocument();
    });
  });

  describe('Visual Mode Switching Scenarios', () => {
    it('should switch between visual and minimal modes seamlessly', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'The interface looks great in both modes!',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should start in visual mode with avatar
      expect(screen.getByTestId('avatar-container')).toBeInTheDocument();
      expect(screen.getByTestId('visual-elements')).toBeInTheDocument();

      // Send a message in visual mode
      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Testing visual mode');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Testing visual mode')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/The interface looks great in both modes!/)).toBeInTheDocument();
      });

      // Switch to minimal mode
      const minimalToggle = screen.getByTestId('minimal-toggle');
      await user.click(minimalToggle);

      // Visual elements should be hidden
      await waitFor(() => {
        expect(screen.queryByTestId('avatar-container')).not.toBeInTheDocument();
        expect(screen.queryByTestId('visual-elements')).not.toBeInTheDocument();
      });

      // Should show minimal interface
      expect(screen.getByTestId('minimal-interface')).toBeInTheDocument();

      // Conversation should still be visible
      expect(screen.getByText('Testing visual mode')).toBeInTheDocument();
      expect(screen.getByText(/The interface looks great in both modes!/)).toBeInTheDocument();

      // Switch back to visual mode
      const visualToggle = screen.getByTestId('visual-toggle');
      await user.click(visualToggle);

      // Visual elements should return
      await waitFor(() => {
        expect(screen.getByTestId('avatar-container')).toBeInTheDocument();
        expect(screen.getByTestId('visual-elements')).toBeInTheDocument();
      });

      // Conversation should still be intact
      expect(screen.getByText('Testing visual mode')).toBeInTheDocument();
      expect(screen.getByText(/The interface looks great in both modes!/)).toBeInTheDocument();
    });

    it('should maintain functionality across mode switches', async () => {
      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'Message in visual mode',
          emotion: 'happy',
          audio: new ArrayBuffer(512)
        })
        .mockResolvedValueOnce({
          text: 'Message in minimal mode',
          emotion: 'neutral',
          audio: new ArrayBuffer(512)
        });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Send message in visual mode
      await user.type(textInput, 'Visual mode message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Message in visual mode/)).toBeInTheDocument();
      });

      // Switch to minimal mode
      const minimalToggle = screen.getByTestId('minimal-toggle');
      await user.click(minimalToggle);

      // Send message in minimal mode
      await user.type(textInput, 'Minimal mode message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Message in minimal mode/)).toBeInTheDocument();
      });

      // Both messages should be visible
      expect(screen.getByText('Visual mode message')).toBeInTheDocument();
      expect(screen.getByText('Minimal mode message')).toBeInTheDocument();
      expect(screen.getByText(/Message in visual mode/)).toBeInTheDocument();
      expect(screen.getByText(/Message in minimal mode/)).toBeInTheDocument();
    });
  });

  describe('Error Recovery Scenarios', () => {
    it('should handle network connectivity issues end-to-end', async () => {
      // Start online
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Go offline
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      act(() => {
        window.dispatchEvent(new Event('offline'));
      });

      // Should show offline indicator
      await waitFor(() => {
        expect(screen.getByTestId('offline-indicator')).toBeInTheDocument();
      });

      // Try to send message while offline
      await user.type(textInput, 'Offline message');
      await user.click(sendButton);

      // Should show queued message notification
      await waitFor(() => {
        expect(screen.getByText(/message queued/i)).toBeInTheDocument();
      });

      // Come back online
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });

      mockAIService.generateResponse.mockResolvedValue({
        text: 'I received your queued message! Connection restored.',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      act(() => {
        window.dispatchEvent(new Event('online'));
      });

      // Should process queued messages
      await waitFor(() => {
        expect(screen.getByText('Offline message')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/I received your queued message!/)).toBeInTheDocument();
      });

      // Offline indicator should disappear
      expect(screen.queryByTestId('offline-indicator')).not.toBeInTheDocument();
    });

    it('should handle complete service recovery workflow', async () => {
      mockAIService.generateResponse
        .mockRejectedValueOnce(new Error('Service down'))
        .mockRejectedValueOnce(new Error('Service down'))
        .mockResolvedValueOnce({
          text: 'Service is back online! Thanks for your patience.',
          emotion: 'relieved',
          audio: new ArrayBuffer(512)
        });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // First attempt fails
      await user.type(textInput, 'Hello Nathan');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/service down/i)).toBeInTheDocument();
      });

      // Retry fails again
      const retryButton = screen.getByTestId('retry-button');
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText(/service down/i)).toBeInTheDocument();
      });

      // Third attempt succeeds
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText(/Service is back online!/)).toBeInTheDocument();
      });

      // Error messages should be cleared
      expect(screen.queryByText(/service down/i)).not.toBeInTheDocument();

      // User message should be visible
      expect(screen.getByText('Hello Nathan')).toBeInTheDocument();
    });
  });

  describe('Accessibility and Mobile Scenarios', () => {
    it('should support keyboard navigation throughout conversation', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Keyboard navigation is working perfectly!',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');
      const voiceToggle = screen.getByTestId('voice-toggle');

      // Tab navigation should work
      textInput.focus();
      expect(document.activeElement).toBe(textInput);

      await user.keyboard('{Tab}');
      expect(document.activeElement).toBe(sendButton);

      await user.keyboard('{Tab}');
      expect(document.activeElement).toBe(voiceToggle);

      // Enter key should work for sending messages
      textInput.focus();
      await user.type(textInput, 'Testing keyboard navigation');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByText('Testing keyboard navigation')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/Keyboard navigation is working perfectly!/)).toBeInTheDocument();
      });
    });

    it('should provide proper ARIA announcements for screen readers', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'This message should be announced to screen readers.',
        emotion: 'helpful',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Send message
      await user.type(textInput, 'Testing screen reader support');
      await user.click(sendButton);

      // Should have live region for announcements
      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });

      // Should announce new messages
      await waitFor(() => {
        expect(screen.getByRole('status')).toHaveTextContent(/new message from nathan/i);
      });

      // Should have proper ARIA labels
      expect(textInput).toHaveAttribute('aria-label');
      expect(sendButton).toHaveAttribute('aria-label');
      expect(screen.getByTestId('message-list')).toHaveAttribute('aria-label');
    });

    it('should handle touch interactions for mobile devices', async () => {
      // Mock touch events
      const mockTouchEvent = {
        touches: [{ clientX: 100, clientY: 100 }],
        preventDefault: vi.fn()
      };

      mockAIService.generateResponse.mockResolvedValue({
        text: 'Touch interactions work great on mobile!',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Simulate touch interaction
      fireEvent.touchStart(textInput, mockTouchEvent);
      fireEvent.touchEnd(textInput, mockTouchEvent);

      await user.type(textInput, 'Testing touch input');

      // Touch send button
      fireEvent.touchStart(sendButton, mockTouchEvent);
      fireEvent.touchEnd(sendButton, mockTouchEvent);

      await waitFor(() => {
        expect(screen.getByText('Testing touch input')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/Touch interactions work great on mobile!/)).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Memory Scenarios', () => {
    it('should handle long conversations without performance degradation', async () => {
      // Mock many responses
      const responses = Array.from({ length: 20 }, (_, i) => ({
        text: `Response number ${i + 1} in a long conversation.`,
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      }));

      mockAIService.generateResponse.mockImplementation(() => 
        Promise.resolve(responses.shift() || responses[0])
      );

      render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Send multiple messages rapidly
      for (let i = 1; i <= 10; i++) {
        await user.type(textInput, `Message ${i}`);
        await user.click(sendButton);
        
        // Wait for response before sending next
        await waitFor(() => {
          expect(screen.getByText(`Response number ${i} in a long conversation.`)).toBeInTheDocument();
        });
      }

      // All messages should be visible
      for (let i = 1; i <= 10; i++) {
        expect(screen.getByText(`Message ${i}`)).toBeInTheDocument();
        expect(screen.getByText(`Response number ${i} in a long conversation.`)).toBeInTheDocument();
      }

      // Should still be responsive
      await user.type(textInput, 'Final message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Final message')).toBeInTheDocument();
      });
    });

    it('should clean up resources properly on unmount', async () => {
      const { unmount } = render(
        <TestWrapper>
          <ChatInterface mode="visual" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Add some audio URLs that need cleanup
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Message with audio for cleanup test',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Test cleanup');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Message with audio for cleanup test/)).toBeInTheDocument();
      });

      // Unmount should clean up audio URLs
      unmount();

      // Should have called revokeObjectURL for cleanup
      expect(global.URL.revokeObjectURL).toHaveBeenCalled();
    });
  });
});