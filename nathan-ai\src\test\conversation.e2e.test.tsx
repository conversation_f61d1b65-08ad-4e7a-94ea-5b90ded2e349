import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import App from '../App';

// Mock environment variables
vi.mock('../utils/env', () => ({
  validateEnvironment: () => ({ isValid: true, errors: [] })
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock AI services
vi.mock('../services/HuggingFaceClient', () => ({
  HuggingFaceClient: vi.fn().mockImplementation(() => ({
    generateText: vi.fn().mockResolvedValue('Hello! How can I help you today?'),
    healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' })
  }))
}));

vi.mock('../services/ElevenLabsClient', () => ({
  ElevenLabsClient: vi.fn().mockImplementation(() => ({
    textToSpeech: vi.fn().mockResolvedValue(new ArrayBuffer(1024)),
    healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' })
  }))
}));

// Mock Web Speech API
const mockSpeechRecognition = {
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: 'en-US',
  onstart: null,
  onend: null,
  onresult: null,
  onerror: null,
  onspeechstart: null,
  onspeechend: null,
  onaudiostart: null,
  onaudioend: null,
  onsoundstart: null,
  onsoundend: null,
  onnomatch: null
};

Object.defineProperty(window, 'SpeechRecognition', {
  writable: true,
  value: vi.fn().mockImplementation(() => mockSpeechRecognition)
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  writable: true,
  value: vi.fn().mockImplementation(() => mockSpeechRecognition)
});

// Mock Audio API
const mockAudio = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 10,
  paused: true,
  ended: false,
  volume: 1,
  muted: false,
  src: '',
  onloadeddata: null,
  oncanplay: null,
  onplay: null,
  onpause: null,
  onended: null,
  onerror: null
};

Object.defineProperty(window, 'Audio', {
  writable: true,
  value: vi.fn().mockImplementation(() => mockAudio)
});

// Mock URL.createObjectURL and revokeObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn().mockReturnValue('blob:mock-url')
});

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn()
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  writable: true,
  value: localStorageMock
});

describe('End-to-End Conversation Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  it('should complete a full text conversation flow', async () => {
    render(<App />);

    // Wait for app to initialize
    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Should show the chat interface
    expect(screen.getByRole('main')).toBeInTheDocument();

    // Should show input controller
    const textInput = screen.getByRole('textbox');
    expect(textInput).toBeInTheDocument();

    // Type a message
    await user.type(textInput, 'Hello Nathan!');
    
    // Send the message
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText(/processing your message/i)).toBeInTheDocument();
    });

    // Should show the response
    await waitFor(() => {
      expect(screen.getByText('Hello! How can I help you today?')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should clear the input
    expect(textInput).toHaveValue('');
  });

  it('should handle voice input mode switching', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Find and click voice mode toggle
    const voiceToggle = screen.getByRole('button', { name: /voice/i });
    await user.click(voiceToggle);

    // Should show voice input interface
    expect(screen.getByRole('button', { name: /start listening/i })).toBeInTheDocument();

    // Click to start listening
    const listenButton = screen.getByRole('button', { name: /start listening/i });
    await user.click(listenButton);

    // Should show listening state
    expect(mockSpeechRecognition.start).toHaveBeenCalled();

    // Simulate speech recognition result
    act(() => {
      const mockEvent = {
        results: [{
          0: { transcript: 'Hello from voice input' },
          isFinal: true
        }]
      };
      mockSpeechRecognition.onresult?.(mockEvent);
    });

    // Should process the voice input
    await waitFor(() => {
      expect(screen.getByText(/processing your message/i)).toBeInTheDocument();
    });
  });

  it('should handle visual mode switching', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Should start in visual mode by default
    const modeSelect = screen.getByLabelText(/mode/i);
    expect(modeSelect).toHaveValue('visual');

    // Switch to minimal mode
    await user.selectOptions(modeSelect, 'minimal');

    // Should update the mode
    expect(modeSelect).toHaveValue('minimal');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('nathan-visual-mode', 'minimal');
  });

  it('should handle theme switching', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Should start with auto theme by default
    const themeSelect = screen.getByLabelText(/theme/i);
    expect(themeSelect).toHaveValue('auto');

    // Switch to dark theme
    await user.selectOptions(themeSelect, 'dark');

    // Should update the theme
    expect(themeSelect).toHaveValue('dark');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('nathan-theme', 'dark');
  });

  it('should handle audio playback integration', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Send a message to trigger audio response
    const textInput = screen.getByRole('textbox');
    await user.type(textInput, 'Tell me a joke');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText(/processing your message/i)).toBeInTheDocument();
    });

    // Should eventually show the response and play audio
    await waitFor(() => {
      expect(screen.getByText('Hello! How can I help you today?')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Audio should be created and played
    expect(URL.createObjectURL).toHaveBeenCalled();
    expect(mockAudio.play).toHaveBeenCalled();
  });

  it('should handle error states gracefully', async () => {
    // Mock AI service to throw an error by re-mocking the module
    vi.doMock('../services/HuggingFaceClient', () => ({
      HuggingFaceClient: vi.fn().mockImplementation(() => ({
        generateText: vi.fn().mockRejectedValue(new Error('API Error')),
        healthCheck: vi.fn().mockResolvedValue({ status: 'healthy' })
      }))
    }));

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Send a message that will fail
    const textInput = screen.getByRole('textbox');
    await user.type(textInput, 'This will fail');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/failed to send message/i)).toBeInTheDocument();
    });

    // Should be able to dismiss error
    const dismissButton = screen.getByRole('button', { name: /dismiss error/i });
    await user.click(dismissButton);

    // Error should be gone
    await waitFor(() => {
      expect(screen.queryByText(/failed to send message/i)).not.toBeInTheDocument();
    });
  });

  it('should persist user preferences across sessions', async () => {
    // Mock localStorage to return saved preferences
    localStorageMock.getItem.mockImplementation((key) => {
      switch (key) {
        case 'nathan-theme': return 'dark';
        case 'nathan-visual-mode': return 'minimal';
        case 'nathan-input-mode': return 'voice';
        default: return null;
      }
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Should restore saved preferences
    const themeSelect = screen.getByLabelText(/theme/i);
    const modeSelect = screen.getByLabelText(/mode/i);
    
    expect(themeSelect).toHaveValue('dark');
    expect(modeSelect).toHaveValue('minimal');
    
    // Should show voice input by default
    expect(screen.getByRole('button', { name: /start listening/i })).toBeInTheDocument();
  });

  it('should handle mobile responsive layout', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375
    });

    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 667
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Should render mobile-friendly layout
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();

    // Controls should be accessible on mobile
    const themeSelect = screen.getByLabelText(/theme/i);
    const modeSelect = screen.getByLabelText(/mode/i);
    
    expect(themeSelect).toBeInTheDocument();
    expect(modeSelect).toBeInTheDocument();
  });

  it('should handle conversation interruption', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Send first message
    const textInput = screen.getByRole('textbox');
    await user.type(textInput, 'First message');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText(/nathan is thinking/i)).toBeInTheDocument();
    });

    // Should show cancel button during loading
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    expect(cancelButton).toBeInTheDocument();

    // Click cancel to interrupt
    await user.click(cancelButton);

    // Should show interruption message
    await waitFor(() => {
      expect(screen.getByText(/conversation interrupted/i)).toBeInTheDocument();
    });
  });

  it('should handle complete conversation flow with all modes', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });

    // Test text input
    const textInput = screen.getByRole('textbox');
    await user.type(textInput, 'Hello Nathan!');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Wait for response
    await waitFor(() => {
      expect(screen.getByText('Hello! How can I help you today?')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Switch to voice mode
    const voiceToggle = screen.getByRole('button', { name: /voice/i });
    await user.click(voiceToggle);

    // Should show voice input interface
    expect(screen.getByRole('button', { name: /start listening/i })).toBeInTheDocument();

    // Switch to minimal mode
    const modeSelect = screen.getByLabelText(/mode/i);
    await user.selectOptions(modeSelect, 'minimal');

    // Should update the interface
    expect(modeSelect).toHaveValue('minimal');

    // Switch theme
    const themeSelect = screen.getByLabelText(/theme/i);
    await user.selectOptions(themeSelect, 'dark');

    // Should update theme
    expect(themeSelect).toHaveValue('dark');
  });
});