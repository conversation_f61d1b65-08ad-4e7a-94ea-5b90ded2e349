import React, { useState, useEffect } from 'react';
import { useBrowserCompatibility } from '../hooks/useBrowserCompatibility';
import type { CompatibilityIssue } from '../services/BrowserCompatibility';
import styles from './CompatibilityWarning.module.css';

interface CompatibilityWarningProps {
  onDismiss?: () => void;
  showOnlyOnce?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export const CompatibilityWarning: React.FC<CompatibilityWarningProps> = ({
  onDismiss,
  showOnlyOnce = true,
  autoHide = false,
  autoHideDelay = 10000
}) => {
  const {
    browserInfo,
    issues,
    isCompatible,
    compatibilityScore,
    getRecommendedBrowserMessage,
    getMobileOptimizations,
    requestPermissions
  } = useBrowserCompatibility();

  const [isDismissed, setIsDismissed] = useState(false);
  const [showPermissionRequest, setShowPermissionRequest] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<{
    microphone: boolean;
    notifications: boolean;
  } | null>(null);

  // Check if warning was previously dismissed
  useEffect(() => {
    if (showOnlyOnce) {
      const dismissed = localStorage.getItem('compatibility-warning-dismissed');
      if (dismissed === 'true') {
        setIsDismissed(true);
      }
    }
  }, [showOnlyOnce]);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && !isDismissed) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, isDismissed]);

  const handleDismiss = () => {
    setIsDismissed(true);
    if (showOnlyOnce) {
      localStorage.setItem('compatibility-warning-dismissed', 'true');
    }
    onDismiss?.();
  };

  const handleRequestPermissions = async () => {
    setShowPermissionRequest(true);
    try {
      const permissions = await requestPermissions();
      setPermissionStatus(permissions);
    } catch (error) {
      console.error('Failed to request permissions:', error);
    }
  };

  // Don't show if dismissed or fully compatible
  if (isDismissed || (isCompatible && compatibilityScore > 90)) {
    return null;
  }

  const criticalIssues = issues.filter(issue => issue.severity === 'critical');
  const warningIssues = issues.filter(issue => issue.severity === 'warning');
  const infoIssues = issues.filter(issue => issue.severity === 'info');
  const recommendedMessage = getRecommendedBrowserMessage();
  const mobileOptimizations = getMobileOptimizations();

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'good';
    if (score >= 60) return 'warning';
    return 'critical';
  };

  return (
    <div className={`${styles.compatibilityWarning} ${styles[getScoreColor(compatibilityScore)]}`}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h3 className={styles.title}>
            Browser Compatibility
            <span className={styles.score}>
              {compatibilityScore}% compatible
            </span>
          </h3>
          <p className={styles.browserInfo}>
            {browserInfo.name} {browserInfo.version}
            {browserInfo.isMobile && ' (Mobile)'}
          </p>
        </div>
        <button
          className={styles.dismissButton}
          onClick={handleDismiss}
          aria-label="Dismiss compatibility warning"
        >
          ×
        </button>
      </div>

      <div className={styles.content}>
        {/* Critical Issues */}
        {criticalIssues.length > 0 && (
          <div className={styles.issueSection}>
            <h4 className={styles.issueTitle}>
              <span className={styles.criticalIcon}>⚠️</span>
              Critical Issues
            </h4>
            <ul className={styles.issueList}>
              {criticalIssues.map((issue, index) => (
                <li key={index} className={styles.issueItem}>
                  <strong>{issue.message}</strong>
                  {issue.fallback && (
                    <div className={styles.fallback}>
                      Fallback: {issue.fallback}
                    </div>
                  )}
                  {issue.guidance && (
                    <div className={styles.guidance}>
                      {issue.guidance}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Warning Issues */}
        {warningIssues.length > 0 && (
          <div className={styles.issueSection}>
            <h4 className={styles.issueTitle}>
              <span className={styles.warningIcon}>⚡</span>
              Limited Features
            </h4>
            <ul className={styles.issueList}>
              {warningIssues.map((issue, index) => (
                <li key={index} className={styles.issueItem}>
                  <strong>{issue.message}</strong>
                  {issue.fallback && (
                    <div className={styles.fallback}>
                      {issue.fallback}
                    </div>
                  )}
                  {issue.guidance && (
                    <div className={styles.guidance}>
                      {issue.guidance}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Info Issues */}
        {infoIssues.length > 0 && (
          <div className={styles.issueSection}>
            <h4 className={styles.issueTitle}>
              <span className={styles.infoIcon}>ℹ️</span>
              Information
            </h4>
            <ul className={styles.issueList}>
              {infoIssues.map((issue, index) => (
                <li key={index} className={styles.issueItem}>
                  {issue.message}
                  {issue.guidance && (
                    <div className={styles.guidance}>
                      {issue.guidance}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Mobile Optimizations */}
        {mobileOptimizations.length > 0 && (
          <div className={styles.issueSection}>
            <h4 className={styles.issueTitle}>
              <span className={styles.mobileIcon}>📱</span>
              Mobile Optimizations
            </h4>
            <ul className={styles.issueList}>
              {mobileOptimizations.map((optimization, index) => (
                <li key={index} className={styles.issueItem}>
                  {optimization}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Recommended Browser Message */}
        {recommendedMessage && (
          <div className={styles.recommendation}>
            <strong>Recommendation:</strong> {recommendedMessage}
          </div>
        )}

        {/* Permission Request */}
        {(warningIssues.some(issue => issue.feature === 'mediaDevices') || 
          warningIssues.some(issue => issue.feature === 'notifications')) && (
          <div className={styles.permissionSection}>
            {!showPermissionRequest ? (
              <button
                className={styles.permissionButton}
                onClick={handleRequestPermissions}
              >
                Request Permissions
              </button>
            ) : permissionStatus ? (
              <div className={styles.permissionStatus}>
                <h5>Permission Status:</h5>
                <ul>
                  <li>
                    Microphone: {permissionStatus.microphone ? '✅ Granted' : '❌ Denied'}
                  </li>
                  <li>
                    Notifications: {permissionStatus.notifications ? '✅ Granted' : '❌ Denied'}
                  </li>
                </ul>
              </div>
            ) : (
              <div className={styles.permissionLoading}>
                Requesting permissions...
              </div>
            )}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className={styles.actions}>
        <button
          className={styles.continueButton}
          onClick={handleDismiss}
        >
          Continue Anyway
        </button>
      </div>
    </div>
  );
};

export default CompatibilityWarning;