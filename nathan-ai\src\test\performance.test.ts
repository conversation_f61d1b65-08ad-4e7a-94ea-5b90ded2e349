/**
 * Performance tests and benchmarks
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock browser APIs for testing
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    memory: {
      usedJSHeapSize: 1024 * 1024 * 50 // 50MB
    }
  },
  writable: true
});

Object.defineProperty(global, 'URL', {
  value: {
    createObjectURL: vi.fn(() => 'blob:mock-url'),
    revokeObjectURL: vi.fn()
  },
  writable: true
});

Object.defineProperty(global, 'Blob', {
  value: class MockBlob {
    constructor(public parts: any[], public options: any = {}) {}
    get type() { return this.options.type || ''; }
    get size() { return this.parts.reduce((size, part) => size + (part.length || part.byteLength || 0), 0); }
  },
  writable: true
});
import { 
  memoize, 
  debounce, 
  throttle, 
  measurePerformance, 
  getMemoryUsage,
  ApiPerformanceTracker,
  AudioLatencyTracker,
  clearCache
} from '../utils/performance';
import { 
  LRUCache, 
  ApiResponseCache, 
  AudioCache, 
  cacheManager 
} from '../utils/cache';

describe('Performance Utilities', () => {
  beforeEach(() => {
    clearCache();
    cacheManager.clearAll();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Memoization', () => {
    it('should cache expensive computations', () => {
      let callCount = 0;
      const expensiveFunction = memoize((x: number) => {
        callCount++;
        return x * x;
      });

      // First call
      const result1 = expensiveFunction(5);
      expect(result1).toBe(25);
      expect(callCount).toBe(1);

      // Second call with same input should use cache
      const result2 = expensiveFunction(5);
      expect(result2).toBe(25);
      expect(callCount).toBe(1);

      // Different input should call function again
      const result3 = expensiveFunction(10);
      expect(result3).toBe(100);
      expect(callCount).toBe(2);
    });

    it('should use custom key generator', () => {
      let callCount = 0;
      const fn = memoize(
        (obj: { x: number; y: number }) => {
          callCount++;
          return obj.x + obj.y;
        },
        (obj) => `${obj.x}-${obj.y}`
      );

      fn({ x: 1, y: 2 });
      fn({ x: 1, y: 2 });
      expect(callCount).toBe(1);
    });

    it('should limit cache size', () => {
      const fn = memoize((x: number) => x * 2);
      
      // Fill cache beyond limit
      for (let i = 0; i < 150; i++) {
        fn(i);
      }
      
      // Should not crash and should still work
      expect(fn(200)).toBe(400);
    });
  });

  describe('Debounce', () => {
    it('should delay function execution', async () => {
      vi.useFakeTimers();
      
      let callCount = 0;
      const debouncedFn = debounce(() => {
        callCount++;
      }, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      expect(callCount).toBe(0);

      vi.advanceTimersByTime(100);
      expect(callCount).toBe(1);

      vi.useRealTimers();
    });

    it('should reset delay on subsequent calls', async () => {
      vi.useFakeTimers();
      
      let callCount = 0;
      const debouncedFn = debounce(() => {
        callCount++;
      }, 100);

      debouncedFn();
      vi.advanceTimersByTime(50);
      debouncedFn(); // Should reset the timer
      vi.advanceTimersByTime(50);
      expect(callCount).toBe(0);
      
      vi.advanceTimersByTime(50);
      expect(callCount).toBe(1);

      vi.useRealTimers();
    });
  });

  describe('Throttle', () => {
    it('should limit function calls', async () => {
      vi.useFakeTimers();
      
      let callCount = 0;
      const throttledFn = throttle(() => {
        callCount++;
      }, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(callCount).toBe(1);

      vi.advanceTimersByTime(100);
      throttledFn();
      expect(callCount).toBe(2);

      vi.useRealTimers();
    });
  });

  describe('Performance Measurement', () => {
    it('should measure function execution time', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      const result = measurePerformance('test-function', () => {
        return 42;
      });

      expect(result).toBe(42);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Performance: test-function took')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Memory Usage', () => {
    it('should return memory usage when available', () => {
      const mockPerformance = {
        memory: {
          usedJSHeapSize: 1024 * 1024 * 50 // 50MB
        }
      };

      Object.defineProperty(global, 'performance', {
        value: mockPerformance,
        writable: true
      });

      const usage = getMemoryUsage();
      expect(usage).toBe(50); // 50MB
    });

    it('should return 0 when memory API not available', () => {
      Object.defineProperty(global, 'performance', {
        value: {},
        writable: true
      });

      const usage = getMemoryUsage();
      expect(usage).toBe(0);
    });
  });

  describe('API Performance Tracking', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should track API response times', () => {
      const endTracking = ApiPerformanceTracker.startTracking('test-api');
      
      vi.advanceTimersByTime(100);
      endTracking();

      const avgTime = ApiPerformanceTracker.getAverageTime('test-api');
      expect(avgTime).toBeGreaterThan(0);
    });

    it('should calculate average times correctly', () => {
      // Track multiple requests
      for (let i = 0; i < 3; i++) {
        const endTracking = ApiPerformanceTracker.startTracking('test-api');
        vi.advanceTimersByTime(100);
        endTracking();
      }

      const avgTime = ApiPerformanceTracker.getAverageTime('test-api');
      expect(avgTime).toBeCloseTo(100, 0);
    });

    it('should return all metrics', () => {
      const endTracking1 = ApiPerformanceTracker.startTracking('api1');
      vi.advanceTimersByTime(50);
      endTracking1();

      const endTracking2 = ApiPerformanceTracker.startTracking('api2');
      vi.advanceTimersByTime(100);
      endTracking2();

      const metrics = ApiPerformanceTracker.getAllMetrics();
      expect(metrics).toHaveProperty('api1');
      expect(metrics).toHaveProperty('api2');
      expect(metrics.api1).toBeCloseTo(50, 0);
      expect(metrics.api2).toBeCloseTo(100, 0);
    });
  });

  describe('Audio Latency Tracking', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should track audio latency', () => {
      const startTime = performance.now();
      vi.advanceTimersByTime(200);
      
      AudioLatencyTracker.trackLatency(startTime);
      
      const avgLatency = AudioLatencyTracker.getAverageLatency();
      expect(avgLatency).toBeCloseTo(200, 0);
    });

    it('should calculate average latency correctly', () => {
      const startTime = performance.now();
      
      // Track multiple latencies
      AudioLatencyTracker.trackLatency(startTime - 100);
      AudioLatencyTracker.trackLatency(startTime - 200);
      AudioLatencyTracker.trackLatency(startTime - 300);

      const avgLatency = AudioLatencyTracker.getAverageLatency();
      expect(avgLatency).toBeCloseTo(200, 0);
    });
  });
});

describe('Cache System', () => {
  beforeEach(() => {
    cacheManager.clearAll();
  });

  describe('LRU Cache', () => {
    it('should store and retrieve values', () => {
      const cache = new LRUCache<string>({ maxSize: 3, ttl: 1000 });
      
      cache.set('key1', 'value1');
      expect(cache.get('key1')).toBe('value1');
      expect(cache.has('key1')).toBe(true);
    });

    it('should respect TTL', async () => {
      const cache = new LRUCache<string>({ maxSize: 3, ttl: 100 });
      
      cache.set('key1', 'value1');
      expect(cache.get('key1')).toBe('value1');
      
      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));
      expect(cache.get('key1')).toBeNull();
    });

    it('should evict LRU items when full', () => {
      const cache = new LRUCache<string>({ maxSize: 2, ttl: 1000 });
      
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      cache.set('key3', 'value3'); // Should evict key1
      
      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBe('value2');
      expect(cache.get('key3')).toBe('value3');
    });

    it('should update access statistics', () => {
      const cache = new LRUCache<string>({ maxSize: 3, ttl: 1000 });
      
      cache.set('key1', 'value1');
      cache.get('key1');
      cache.get('key1');
      
      const stats = cache.getStats();
      expect(stats.size).toBe(1);
      expect(stats.entries[0].accessCount).toBe(2);
    });
  });

  describe('API Response Cache', () => {
    it('should cache API responses', () => {
      const cache = new ApiResponseCache();
      const response = { data: 'test response' };
      
      cache.set('test-endpoint', { param: 'value' }, response);
      const cached = cache.get('test-endpoint', { param: 'value' });
      
      expect(cached).toEqual(response);
    });

    it('should generate consistent keys', () => {
      const cache = new ApiResponseCache();
      
      const key1 = cache.generateKey('endpoint', { b: 2, a: 1 });
      const key2 = cache.generateKey('endpoint', { a: 1, b: 2 });
      
      expect(key1).toBe(key2);
    });

    it('should return null for non-existent keys', () => {
      const cache = new ApiResponseCache();
      
      const result = cache.get('non-existent', {});
      expect(result).toBeNull();
    });
  });

  describe('Audio Cache', () => {
    it('should cache audio buffers', () => {
      const cache = new AudioCache();
      const audioBuffer = new ArrayBuffer(1024);
      const voiceSettings = { stability: 0.5 };
      
      cache.set('test text', voiceSettings, audioBuffer);
      const cached = cache.get('test text', voiceSettings);
      
      expect(cached).toBe(audioBuffer);
    });

    it('should generate blob URLs', () => {
      const cache = new AudioCache();
      const audioBuffer = new ArrayBuffer(1024);
      const voiceSettings = { stability: 0.5 };
      
      cache.set('test text', voiceSettings, audioBuffer);
      const url = cache.getUrl('test text', voiceSettings);
      
      expect(url).toMatch(/^blob:/);
    });

    it('should normalize text for consistent caching', () => {
      const cache = new AudioCache();
      const audioBuffer = new ArrayBuffer(1024);
      const voiceSettings = { stability: 0.5 };
      
      cache.set('  Test Text  ', voiceSettings, audioBuffer);
      const cached = cache.get('test text', voiceSettings);
      
      expect(cached).toBe(audioBuffer);
    });

    it('should clean up blob URLs on clear', () => {
      const revokeObjectURLSpy = vi.spyOn(URL, 'revokeObjectURL').mockImplementation(() => {});
      
      const cache = new AudioCache();
      const audioBuffer = new ArrayBuffer(1024);
      const voiceSettings = { stability: 0.5 };
      
      cache.set('test text', voiceSettings, audioBuffer);
      cache.getUrl('test text', voiceSettings);
      cache.clear();
      
      expect(revokeObjectURLSpy).toHaveBeenCalled();
      revokeObjectURLSpy.mockRestore();
    });
  });

  describe('Cache Manager', () => {
    it('should provide access to all caches', () => {
      expect(cacheManager.apiCache).toBeInstanceOf(ApiResponseCache);
      expect(cacheManager.audioCache).toBeInstanceOf(AudioCache);
      expect(cacheManager.personalityCache).toBeDefined();
    });

    it('should clear all caches', () => {
      cacheManager.apiCache.set('test', {}, 'data');
      cacheManager.audioCache.set('test', {}, new ArrayBuffer(1024));
      cacheManager.personalityCache.set('test', {});
      
      cacheManager.clearAll();
      
      expect(cacheManager.apiCache.get('test', {})).toBeNull();
      expect(cacheManager.audioCache.get('test', {})).toBeNull();
      expect(cacheManager.personalityCache.get('test')).toBeNull();
    });

    it('should provide global stats', () => {
      cacheManager.apiCache.set('test', {}, 'data');
      
      const stats = cacheManager.getGlobalStats();
      expect(stats).toHaveProperty('api');
      expect(stats).toHaveProperty('audio');
      expect(stats).toHaveProperty('personality');
    });
  });
});

describe('Performance Benchmarks', () => {
  it('should benchmark component rendering', () => {
    const renderTimes: number[] = [];
    
    for (let i = 0; i < 10; i++) {
      const start = Date.now();
      
      // Simulate component rendering work
      const data = Array.from({ length: 1000 }, (_, i) => ({ id: i, value: Math.random() }));
      data.sort((a, b) => a.value - b.value);
      
      const end = Date.now();
      renderTimes.push(end - start);
    }
    
    const avgRenderTime = renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length;
    
    // Should render in reasonable time (less than 100ms on average for test environment)
    expect(avgRenderTime).toBeLessThan(100);
  });

  it('should benchmark cache performance', () => {
    const cache = new LRUCache<string>({ maxSize: 1000, ttl: 60000 });
    
    // Benchmark cache writes
    const writeStart = Date.now();
    for (let i = 0; i < 1000; i++) {
      cache.set(`key${i}`, `value${i}`);
    }
    const writeTime = Date.now() - writeStart;
    
    // Benchmark cache reads
    const readStart = Date.now();
    for (let i = 0; i < 1000; i++) {
      cache.get(`key${i}`);
    }
    const readTime = Date.now() - readStart;
    
    // Cache operations should be fast (relaxed for test environment)
    expect(writeTime).toBeLessThan(500); // 500ms for 1000 writes
    expect(readTime).toBeLessThan(100);  // 100ms for 1000 reads
  });

  it('should benchmark memoization performance', () => {
    let callCount = 0;
    const expensiveFunction = (n: number): number => {
      callCount++;
      // Simulate expensive computation
      let result = 0;
      for (let i = 0; i < n; i++) {
        result += Math.sqrt(i);
      }
      return result;
    };
    
    const memoizedFunction = memoize(expensiveFunction);
    
    // First call (should be slow)
    const start1 = Date.now();
    memoizedFunction(1000); // Reduced for test performance
    const time1 = Date.now() - start1;
    
    // Second call (should be fast due to memoization)
    const start2 = Date.now();
    memoizedFunction(1000);
    const time2 = Date.now() - start2;
    
    expect(callCount).toBe(1); // Function should only be called once
    expect(time2).toBeLessThan(Math.max(1, time1)); // Memoized call should be faster or equal
  });
});