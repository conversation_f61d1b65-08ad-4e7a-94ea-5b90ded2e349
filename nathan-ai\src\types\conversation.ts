import type { Message } from './message';

export interface ConversationState {
  messages: Message[];
  isLoading: boolean;
  inputMode: 'voice' | 'text';
  visualMode: 'visual' | 'minimal';
  isListening: boolean;
  isPlaying: boolean;
  error: string | null;
}

export type ConversationAction =
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_INPUT_MODE'; payload: 'voice' | 'text' }
  | { type: 'SET_VISUAL_MODE'; payload: 'visual' | 'minimal' }
  | { type: 'SET_LISTENING'; payload: boolean }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_MESSAGES' };

// Input and Visual mode type aliases for better type safety
export type InputMode = 'voice' | 'text';
export type VisualMode = 'visual' | 'minimal';
