import type { Message, EmotionType } from './message';
import type { PersonalityConfig } from './personality';
import type { ConversationAction, ConversationState } from './conversation';

// Chat Interface Props
export interface ChatInterfaceProps {
  mode: 'visual' | 'minimal';
  personality: PersonalityConfig;
}

// Input Controller Props
export interface InputControllerProps {
  inputMode: 'voice' | 'text';
  onModeChange: (mode: 'voice' | 'text') => void;
  onMessageSend: (message: string) => void;
  isListening: boolean;
}

// Voice Input Props and State
export interface VoiceInputProps {
  onTranscript: (transcript: string) => void;
  onListeningChange: (isListening: boolean) => void;
  onError: (error: string) => void;
  isActive: boolean;
}

export interface VoiceInputState {
  isListening: boolean;
  transcript: string;
  error: string | null;
  isSupported: boolean;
}

// Text Input Props
export interface TextInputProps {
  onMessageSend: (message: string) => void;
  isLoading: boolean;
  placeholder?: string;
  maxLength?: number;
}

// Message Bubble Props
export interface MessageBubbleProps {
  message: Message;
  isUser: boolean;
  showAvatar: boolean;
  personality: PersonalityConfig;
}

// Avatar Props
export interface AvatarProps {
  emotion: EmotionType;
  isAnimated: boolean;
  size: 'small' | 'medium' | 'large';
}

// Audio Player Props
export interface AudioPlayerProps {
  audioQueue: AudioQueueItem[];
  isPlaying: boolean;
  onPlayingChange: (playing: boolean) => void;
  onAudioComplete: (id: string) => void;
  onError: (error: string) => void;
  volume?: number;
}

export interface AudioQueueItem {
  id: string;
  audioUrl: string;
  text: string;
  priority?: number;
}

export interface AudioPlayerState {
  currentAudio: HTMLAudioElement | null;
  currentItem: AudioQueueItem | null;
  isLoading: boolean;
  error: string | null;
  volume: number;
}

// App Context Props
export interface AppContextType {
  state: ConversationState;
  dispatch: React.Dispatch<ConversationAction>;
  personality: PersonalityConfig;
  updatePersonality: (config: Partial<PersonalityConfig>) => void;
}

// Error Boundary Props
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

// Utility Props Types
export type InputMode = 'voice' | 'text';
export type VisualMode = 'visual' | 'minimal';
export type AvatarSize = 'small' | 'medium' | 'large';

// Common component props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Loading state props
export interface LoadingProps extends BaseComponentProps {
  isLoading: boolean;
  loadingText?: string;
}

// Button props
export interface ButtonProps extends BaseComponentProps {
  onClick: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
}