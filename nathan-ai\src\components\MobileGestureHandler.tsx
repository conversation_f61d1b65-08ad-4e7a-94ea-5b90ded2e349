import React, { useRef, useEffect } from 'react';
import { useSwipeGestures, useMobileResponsive } from '../hooks/useMobileResponsive';
import styles from './MobileGestureHandler.module.css';

interface MobileGestureHandlerProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  className?: string;
  disabled?: boolean;
}

export const MobileGestureHandler: React.FC<MobileGestureHandlerProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  className = '',
  disabled = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { isMobile, isTouch } = useMobileResponsive();
  
  const { handleTouchStart } = useSwipeGestures({
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold: 50
  });

  // Only enable gestures on mobile touch devices
  const gesturesEnabled = isMobile && isTouch && !disabled;

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !gesturesEnabled) return;

    // Prevent default touch behaviors that might interfere
    const preventDefaultTouch = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault(); // Prevent pinch zoom
      }
    };

    container.addEventListener('touchstart', preventDefaultTouch, { passive: false });
    
    return () => {
      container.removeEventListener('touchstart', preventDefaultTouch);
    };
  }, [gesturesEnabled]);

  return (
    <div
      ref={containerRef}
      className={`${styles.gestureHandler} ${className} ${gesturesEnabled ? styles.enabled : ''}`}
      onTouchStart={gesturesEnabled ? handleTouchStart : undefined}
      data-gesture-enabled={gesturesEnabled}
    >
      {children}
    </div>
  );
};