import { useCallback } from 'react';
import { useConversationContext } from '../context/ConversationContext';
import type { Message } from '../types/message';
import type { InputMode, VisualMode } from '../types/conversation';

/**
 * Custom hook for managing conversation state and actions
 * Provides convenient methods for common conversation operations
 */
export function useConversation() {
  const { state, dispatch } = useConversationContext();

  // Add a new message to the conversation
  const addMessage = useCallback((message: Message) => {
    dispatch({ type: 'ADD_MESSAGE', payload: message });
  }, [dispatch]);

  // Set loading state
  const setLoading = useCallback((isLoading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: isLoading });
  }, [dispatch]);

  // Set input mode (voice or text)
  const setInputMode = useCallback((mode: InputMode) => {
    dispatch({ type: 'SET_INPUT_MODE', payload: mode });
  }, [dispatch]);

  // Set visual mode (visual or minimal)
  const setVisualMode = useCallback((mode: VisualMode) => {
    dispatch({ type: 'SET_VISUAL_MODE', payload: mode });
  }, [dispatch]);

  // Set listening state for voice input
  const setListening = useCallback((isListening: boolean) => {
    dispatch({ type: 'SET_LISTENING', payload: isListening });
  }, [dispatch]);

  // Set playing state for audio output
  const setPlaying = useCallback((isPlaying: boolean) => {
    dispatch({ type: 'SET_PLAYING', payload: isPlaying });
  }, [dispatch]);

  // Set error state
  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, [dispatch]);

  // Clear all messages
  const clearMessages = useCallback(() => {
    dispatch({ type: 'CLEAR_MESSAGES' });
  }, [dispatch]);

  // Helper function to create a user message
  const createUserMessage = useCallback((content: string): Message => {
    return {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      timestamp: new Date(),
      sender: 'user',
    };
  }, []);

  // Helper function to create a Nathan message
  const createNathanMessage = useCallback((
    content: string, 
    emotion?: Message['emotion'],
    audioUrl?: string
  ): Message => {
    return {
      id: `nathan-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      timestamp: new Date(),
      sender: 'nathan',
      emotion,
      audioUrl,
    };
  }, []);

  // Add a user message (convenience method)
  const addUserMessage = useCallback((content: string) => {
    const message = createUserMessage(content);
    addMessage(message);
    return message;
  }, [createUserMessage, addMessage]);

  // Add a Nathan message (convenience method)
  const addNathanMessage = useCallback((
    content: string, 
    emotion?: Message['emotion'],
    audioUrl?: string
  ) => {
    const message = createNathanMessage(content, emotion, audioUrl);
    addMessage(message);
    return message;
  }, [createNathanMessage, addMessage]);

  // Toggle input mode between voice and text
  const toggleInputMode = useCallback(() => {
    const newMode = state.inputMode === 'voice' ? 'text' : 'voice';
    setInputMode(newMode);
  }, [state.inputMode, setInputMode]);

  // Toggle visual mode between visual and minimal
  const toggleVisualMode = useCallback(() => {
    const newMode = state.visualMode === 'visual' ? 'minimal' : 'visual';
    setVisualMode(newMode);
  }, [state.visualMode, setVisualMode]);

  // Get the last message in the conversation
  const getLastMessage = useCallback((): Message | null => {
    return state.messages.length > 0 ? state.messages[state.messages.length - 1] : null;
  }, [state.messages]);

  // Get messages by sender
  const getMessagesBySender = useCallback((sender: 'user' | 'nathan'): Message[] => {
    return state.messages.filter(message => message.sender === sender);
  }, [state.messages]);

  return {
    // State
    ...state,
    
    // Actions
    addMessage,
    setLoading,
    setInputMode,
    setVisualMode,
    setListening,
    setPlaying,
    setError,
    clearMessages,
    
    // Helper methods
    createUserMessage,
    createNathanMessage,
    addUserMessage,
    addNathanMessage,
    toggleInputMode,
    toggleVisualMode,
    getLastMessage,
    getMessagesBySender,
  };
}