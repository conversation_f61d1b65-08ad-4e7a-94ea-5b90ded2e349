export interface PersonalityConfig {
  name: string;
  pronouns: string;
  personality: {
    tone: string;
    role: string;
    hobbies: string[];
    style: {
      speech: string;
      humor: string;
      depth: string;
    };
    boundaries: {
      avoid: string[];
      safe_topics: string[];
    };
    dynamic_traits: {
      adaptive_empathy: boolean;
      mirroring_style: boolean;
      emotionally_available: boolean;
    };
  };
  conversation_tips: {
    starter_prompts: string[];
  };
  version: string;
}
