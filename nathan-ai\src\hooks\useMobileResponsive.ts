import { useState, useEffect, useCallback } from 'react';

interface MobileResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  screenWidth: number;
  screenHeight: number;
  isTouch: boolean;
  hasHover: boolean;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

interface MobileGestures {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
}

export const useMobileResponsive = () => {
  const [state, setState] = useState<MobileResponsiveState>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    orientation: 'portrait',
    screenWidth: 0,
    screenHeight: 0,
    isTouch: false,
    hasHover: true,
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 }
  });

  const updateState = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const isMobile = width < 768;
    const isTablet = width >= 768 && width < 1024;
    const isDesktop = width >= 1024;
    const orientation = width > height ? 'landscape' : 'portrait';
    
    // Detect touch capability
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // Detect hover capability
    const hasHover = window.matchMedia('(hover: hover)').matches;
    
    // Get safe area insets (iOS)
    const safeAreaInsets = {
      top: parseInt(getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)') || '0'),
      bottom: parseInt(getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-left)') || '0'),
      right: parseInt(getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-right)') || '0')
    };

    setState({
      isMobile,
      isTablet,
      isDesktop,
      orientation,
      screenWidth: width,
      screenHeight: height,
      isTouch,
      hasHover,
      safeAreaInsets
    });
  }, []);

  useEffect(() => {
    updateState();
    
    const handleResize = () => updateState();
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateState, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [updateState]);

  return state;
};

export const useSwipeGestures = (gestures: MobileGestures) => {
  const { threshold = 50 } = gestures;
  
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startX = touch.clientX;
    const startY = touch.clientY;
    const startTime = Date.now();

    const handleTouchEnd = (endEvent: TouchEvent) => {
      const endTouch = endEvent.changedTouches[0];
      const endX = endTouch.clientX;
      const endY = endTouch.clientY;
      const endTime = Date.now();
      
      const deltaX = endX - startX;
      const deltaY = endY - startY;
      const deltaTime = endTime - startTime;
      
      // Only consider swipes that are fast enough (< 300ms) and long enough
      if (deltaTime > 300) return;
      
      const absDeltaX = Math.abs(deltaX);
      const absDeltaY = Math.abs(deltaY);
      
      if (absDeltaX > threshold && absDeltaX > absDeltaY) {
        // Horizontal swipe
        if (deltaX > 0) {
          gestures.onSwipeRight?.();
        } else {
          gestures.onSwipeLeft?.();
        }
      } else if (absDeltaY > threshold && absDeltaY > absDeltaX) {
        // Vertical swipe
        if (deltaY > 0) {
          gestures.onSwipeDown?.();
        } else {
          gestures.onSwipeUp?.();
        }
      }
      
      document.removeEventListener('touchend', handleTouchEnd);
    };

    document.addEventListener('touchend', handleTouchEnd);
  }, [gestures, threshold]);

  return { handleTouchStart };
};

export const useViewportHeight = () => {
  const [viewportHeight, setViewportHeight] = useState(0);

  useEffect(() => {
    const updateHeight = () => {
      // Use visualViewport API if available (better for mobile)
      if (window.visualViewport) {
        setViewportHeight(window.visualViewport.height);
      } else {
        setViewportHeight(window.innerHeight);
      }
    };

    updateHeight();

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateHeight);
      return () => window.visualViewport?.removeEventListener('resize', updateHeight);
    } else {
      window.addEventListener('resize', updateHeight);
      return () => window.removeEventListener('resize', updateHeight);
    }
  }, []);

  return viewportHeight;
};

export const useKeyboardHeight = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  useEffect(() => {
    if (!window.visualViewport) return;

    const handleViewportChange = () => {
      const viewport = window.visualViewport!;
      const keyboardHeight = window.innerHeight - viewport.height;
      
      setKeyboardHeight(keyboardHeight);
      setIsKeyboardOpen(keyboardHeight > 150); // Threshold for keyboard detection
    };

    window.visualViewport.addEventListener('resize', handleViewportChange);
    
    return () => {
      window.visualViewport?.removeEventListener('resize', handleViewportChange);
    };
  }, []);

  return { keyboardHeight, isKeyboardOpen };
};

export const useMobileFriendlyScroll = (elementRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Improve scroll performance on mobile
    element.style.webkitOverflowScrolling = 'touch';
    element.style.overscrollBehavior = 'contain';
    
    // Prevent scroll chaining on mobile
    const preventScrollChaining = (e: TouchEvent) => {
      const { scrollTop, scrollHeight, clientHeight } = element;
      
      if (scrollTop === 0 && e.touches[0].clientY > e.touches[0].clientY) {
        e.preventDefault();
      }
      
      if (scrollTop + clientHeight >= scrollHeight && e.touches[0].clientY < e.touches[0].clientY) {
        e.preventDefault();
      }
    };

    element.addEventListener('touchmove', preventScrollChaining, { passive: false });
    
    return () => {
      element.removeEventListener('touchmove', preventScrollChaining);
    };
  }, [elementRef]);
};

export const usePreventZoom = () => {
  useEffect(() => {
    // Prevent pinch zoom on mobile
    const preventZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    const preventDoubleTabZoom = (e: TouchEvent) => {
      let lastTouchEnd = 0;
      const now = new Date().getTime();
      if (now - lastTouchEnd <= 300) {
        e.preventDefault();
      }
      lastTouchEnd = now;
    };

    document.addEventListener('touchstart', preventZoom, { passive: false });
    document.addEventListener('touchend', preventDoubleTabZoom, { passive: false });
    
    return () => {
      document.removeEventListener('touchstart', preventZoom);
      document.removeEventListener('touchend', preventDoubleTabZoom);
    };
  }, []);
};

export const useMobileOptimizations = () => {
  const responsive = useMobileResponsive();
  const viewportHeight = useViewportHeight();
  const { keyboardHeight, isKeyboardOpen } = useKeyboardHeight();

  useEffect(() => {
    if (responsive.isMobile) {
      // Set CSS custom properties for mobile optimizations
      document.documentElement.style.setProperty('--viewport-height', `${viewportHeight}px`);
      document.documentElement.style.setProperty('--keyboard-height', `${keyboardHeight}px`);
      document.documentElement.style.setProperty('--is-keyboard-open', isKeyboardOpen ? '1' : '0');
      
      // Add mobile class to body
      document.body.classList.add('mobile-device');
      
      if (responsive.isTouch) {
        document.body.classList.add('touch-device');
      }
      
      if (!responsive.hasHover) {
        document.body.classList.add('no-hover');
      }
    } else {
      document.body.classList.remove('mobile-device', 'touch-device', 'no-hover');
    }
    
    return () => {
      document.body.classList.remove('mobile-device', 'touch-device', 'no-hover');
    };
  }, [responsive, viewportHeight, keyboardHeight, isKeyboardOpen]);

  return {
    ...responsive,
    viewportHeight,
    keyboardHeight,
    isKeyboardOpen
  };
};