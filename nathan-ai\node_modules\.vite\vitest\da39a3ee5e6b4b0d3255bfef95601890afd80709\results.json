{"version": "3.2.4", "results": [[":src/test/ErrorBoundary.test.tsx", {"duration": 737.5219999999999, "failed": false}], [":src/test/AsyncErrorBoundary.test.tsx", {"duration": 347.6777000000002, "failed": false}], [":src/test/FeatureErrorBoundary.test.tsx", {"duration": 430.6313, "failed": false}], [":src/test/ApiErrorBoundary.test.tsx", {"duration": 446.34029999999984, "failed": false}], [":src/test/ErrorRecovery.test.tsx", {"duration": 289.6120999999994, "failed": false}], [":src/test/ApiErrorHandler.test.ts", {"duration": 9138.4555, "failed": true}], [":src/test/NotificationSystem.test.tsx", {"duration": 373.79449999999997, "failed": true}], [":src/test/api-error-integration.test.ts", {"duration": 0, "failed": true}], [":src/test/BrowserCompatibility.test.ts", {"duration": 49.74419999999918, "failed": true}], [":src/test/responsive.test.tsx", {"duration": 140.34699999999975, "failed": false}], [":src/test/mobilePerformance.test.ts", {"duration": 49.17979999999989, "failed": true}], [":src/test/mobilePerformance.integration.test.ts", {"duration": 44.22599999999875, "failed": false}], [":src/test/Avatar.test.tsx", {"duration": 386.96939999999995, "failed": false}], [":src/test/personalitySystem.integration.test.tsx", {"duration": 0, "failed": true}], [":src/test/conversationFlow.integration.test.tsx", {"duration": 229.29069999999956, "failed": true}], [":src/test/errorRecovery.integration.test.tsx", {"duration": 215.52670000000035, "failed": true}], [":src/test/stateIntegration.test.tsx", {"duration": 3740.2439000000004, "failed": true}], [":src/test/inputModes.integration.test.tsx", {"duration": 0, "failed": true}], [":src/test/e2e.conversation.test.tsx", {"duration": 133.39480000000003, "failed": true}], [":src/test/e2e.workflow.test.ts", {"duration": 320.8041999999996, "failed": true}], [":src/test/security.test.ts", {"duration": 185.8795, "failed": true}], [":src/test/env.security.test.ts", {"duration": 192.17799999999988, "failed": true}], [":src/test/SecurityStatus.test.tsx", {"duration": 225.2514000000001, "failed": true}], [":src/test/inputValidation.test.ts", {"duration": 314.6841999999997, "failed": true}], [":src/test/csp.test.ts", {"duration": 71.1327999999994, "failed": true}], [":src/test/performance.test.ts", {"duration": 272.6151, "failed": false}], [":src/test/MobileGestureHandler.test.tsx", {"duration": 350.0662000000002, "failed": true}], [":src/test/setup.test.ts", {"duration": 8.555899999999838, "failed": false}], [":src/test/AIService.test.ts", {"duration": 119.26520000000073, "failed": false}]]}