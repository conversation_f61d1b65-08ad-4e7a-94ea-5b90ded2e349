import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  optimizeForMobile,
  createMobileOptimizedBundle,
  enableMobilePerformanceMode,
  disableMobilePerformanceMode,
  getMobileOptimizationStatus,
  optimizeImageLoading,
  optimizeAudioForMobile,
  reducedMotionPreference,
  touchOptimizations
} from '../utils/mobileOptimizations';

// Mock performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    memory: {
      usedJSHeapSize: 50000000,
      totalJSHeapSize: 100000000,
      jsHeapSizeLimit: 200000000,
    },
    now: vi.fn(() => Date.now()),
  },
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

describe('Mobile Optimizations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset DOM
    document.body.innerHTML = '';
    document.documentElement.className = '';
    
    // Reset window properties
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    Object.defineProperty(navigator, 'userAgent', {
      writable: true,
      configurable: true,
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    });

    Object.defineProperty(navigator, 'connection', {
      writable: true,
      configurable: true,
      value: {
        effectiveType: '4g',
        saveData: false,
        downlink: 10,
        rtt: 50,
      },
    });

    // Reset localStorage
    localStorage.clear();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('optimizeForMobile', () => {
    it('applies mobile optimizations when on mobile device', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        configurable: true,
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      });

      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const result = optimizeForMobile();

      expect(result.applied).toBe(true);
      expect(result.optimizations).toContain('mobile-device-detected');
      expect(document.documentElement.classList.contains('mobile-optimized')).toBe(true);
    });

    it('skips optimizations on desktop', () => {
      const result = optimizeForMobile();

      expect(result.applied).toBe(false);
      expect(result.optimizations).toHaveLength(0);
      expect(document.documentElement.classList.contains('mobile-optimized')).toBe(false);
    });

    it('applies aggressive optimizations on slow connection', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        configurable: true,
        value: 'Mozilla/5.0 (Android 10; Mobile; rv:81.0)',
      });

      Object.defineProperty(navigator, 'connection', {
        writable: true,
        configurable: true,
        value: {
          effectiveType: '2g',
          saveData: true,
        },
      });

      const result = optimizeForMobile();

      expect(result.applied).toBe(true);
      expect(result.optimizations).toContain('aggressive-mode');
      expect(document.documentElement.classList.contains('aggressive-mobile-optimization')).toBe(true);
    });

    it('applies memory optimizations when memory is low', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        configurable: true,
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      });

      Object.defineProperty(window, 'performance', {
        writable: true,
        value: {
          memory: {
            usedJSHeapSize: 180000000,
            totalJSHeapSize: 200000000,
            jsHeapSizeLimit: 200000000,
          },
        },
      });

      const result = optimizeForMobile();

      expect(result.applied).toBe(true);
      expect(result.optimizations).toContain('memory-optimization');
    });
  });

  describe('createMobileOptimizedBundle', () => {
    it('creates optimized bundle configuration', () => {
      const config = createMobileOptimizedBundle();

      expect(config).toHaveProperty('chunkSizeWarningLimit');
      expect(config).toHaveProperty('rollupOptions');
      expect(config.chunkSizeWarningLimit).toBeLessThan(1000);
    });

    it('includes mobile-specific chunk splitting', () => {
      const config = createMobileOptimizedBundle();

      expect(config.rollupOptions.output.manualChunks).toBeDefined();
      expect(typeof config.rollupOptions.output.manualChunks).toBe('function');
    });

    it('configures compression for mobile', () => {
      const config = createMobileOptimizedBundle();

      expect(config.rollupOptions.plugins).toBeDefined();
      expect(Array.isArray(config.rollupOptions.plugins)).toBe(true);
    });
  });

  describe('enableMobilePerformanceMode', () => {
    it('enables performance mode and saves to localStorage', () => {
      enableMobilePerformanceMode();

      expect(localStorage.getItem('nathan-mobile-performance-mode')).toBe('enabled');
      expect(document.documentElement.classList.contains('mobile-performance-mode')).toBe(true);
    });

    it('applies performance optimizations to DOM', () => {
      enableMobilePerformanceMode();

      const style = document.querySelector('style[data-mobile-performance]');
      expect(style).toBeTruthy();
      expect(style?.textContent).toContain('will-change: auto');
    });
  });

  describe('disableMobilePerformanceMode', () => {
    it('disables performance mode and removes from localStorage', () => {
      enableMobilePerformanceMode();
      disableMobilePerformanceMode();

      expect(localStorage.getItem('nathan-mobile-performance-mode')).toBe('disabled');
      expect(document.documentElement.classList.contains('mobile-performance-mode')).toBe(false);
    });

    it('removes performance optimizations from DOM', () => {
      enableMobilePerformanceMode();
      disableMobilePerformanceMode();

      const style = document.querySelector('style[data-mobile-performance]');
      expect(style).toBeFalsy();
    });
  });

  describe('getMobileOptimizationStatus', () => {
    it('returns current optimization status', () => {
      const status = getMobileOptimizationStatus();

      expect(status).toHaveProperty('isMobile');
      expect(status).toHaveProperty('performanceModeEnabled');
      expect(status).toHaveProperty('networkInfo');
      expect(status).toHaveProperty('memoryInfo');
      expect(status).toHaveProperty('appliedOptimizations');
    });

    it('detects mobile device correctly', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        configurable: true,
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      });

      const status = getMobileOptimizationStatus();
      expect(status.isMobile).toBe(true);
    });

    it('includes network information', () => {
      const status = getMobileOptimizationStatus();

      expect(status.networkInfo).toHaveProperty('effectiveType');
      expect(status.networkInfo).toHaveProperty('saveData');
    });

    it('includes memory information when available', () => {
      const status = getMobileOptimizationStatus();

      expect(status.memoryInfo).toHaveProperty('usedJSHeapSize');
      expect(status.memoryInfo).toHaveProperty('totalJSHeapSize');
    });
  });

  describe('optimizeImageLoading', () => {
    it('sets up lazy loading for images', () => {
      document.body.innerHTML = `
        <img src="test1.jpg" alt="Test 1" />
        <img src="test2.jpg" alt="Test 2" />
      `;

      optimizeImageLoading();

      const images = document.querySelectorAll('img');
      images.forEach(img => {
        expect(img.getAttribute('loading')).toBe('lazy');
      });
    });

    it('adds intersection observer for images', () => {
      document.body.innerHTML = `
        <img src="test.jpg" alt="Test" />
      `;

      optimizeImageLoading();

      expect(IntersectionObserver).toHaveBeenCalled();
    });

    it('handles images without src gracefully', () => {
      document.body.innerHTML = `
        <img alt="Test without src" />
      `;

      expect(() => optimizeImageLoading()).not.toThrow();
    });
  });

  describe('optimizeAudioForMobile', () => {
    it('returns mobile-optimized audio configuration', () => {
      const config = optimizeAudioForMobile();

      expect(config).toHaveProperty('sampleRate');
      expect(config).toHaveProperty('bitRate');
      expect(config).toHaveProperty('channels');
      expect(config).toHaveProperty('format');
    });

    it('uses lower quality settings on slow connection', () => {
      Object.defineProperty(navigator, 'connection', {
        writable: true,
        configurable: true,
        value: {
          effectiveType: '2g',
          saveData: true,
        },
      });

      const config = optimizeAudioForMobile();

      expect(config.bitRate).toBeLessThan(128);
      expect(config.sampleRate).toBeLessThan(44100);
    });

    it('uses higher quality settings on good connection', () => {
      const config = optimizeAudioForMobile();

      expect(config.bitRate).toBeGreaterThanOrEqual(128);
      expect(config.sampleRate).toBeGreaterThanOrEqual(22050);
    });
  });

  describe('reducedMotionPreference', () => {
    it('detects reduced motion preference', () => {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });

      const prefersReduced = reducedMotionPreference();
      expect(prefersReduced).toBe(true);
    });

    it('handles missing matchMedia gracefully', () => {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: undefined,
      });

      const prefersReduced = reducedMotionPreference();
      expect(prefersReduced).toBe(false);
    });
  });

  describe('touchOptimizations', () => {
    it('applies touch-friendly styles', () => {
      touchOptimizations.enable();

      const style = document.querySelector('style[data-touch-optimizations]');
      expect(style).toBeTruthy();
      expect(style?.textContent).toContain('touch-action');
    });

    it('removes touch optimizations when disabled', () => {
      touchOptimizations.enable();
      touchOptimizations.disable();

      const style = document.querySelector('style[data-touch-optimizations]');
      expect(style).toBeFalsy();
    });

    it('handles multiple enable/disable calls gracefully', () => {
      touchOptimizations.enable();
      touchOptimizations.enable();
      touchOptimizations.disable();

      const styles = document.querySelectorAll('style[data-touch-optimizations]');
      expect(styles.length).toBeLessThanOrEqual(1);
    });
  });
});