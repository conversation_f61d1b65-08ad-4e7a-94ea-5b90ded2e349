import { describe, it, expect } from 'vitest';

describe('Test Setup', () => {
  it('should have testing environment configured', () => {
    expect(true).toBe(true);
  });

  it('should have mocked Web Speech API', () => {
    expect(window.SpeechRecognition).toBeDefined();
    expect(window.webkitSpeechRecognition).toBeDefined();
  });

  it('should have mocked Audio API', () => {
    expect(window.Audio).toBeDefined();
  });
});
