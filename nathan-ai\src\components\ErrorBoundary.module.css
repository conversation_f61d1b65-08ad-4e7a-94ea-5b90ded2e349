.errorBoundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: var(--background-color, #f5f5f5);
  font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.content {
  max-width: 600px;
  padding: 2rem;
  background: var(--surface-color, white);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid var(--error-color, #dc3545);
}

.icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.title {
  color: var(--error-color, #dc3545);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.message {
  color: var(--text-color, #333);
  margin-bottom: 1rem;
  line-height: 1.6;
  font-weight: 500;
}

.suggestion {
  color: var(--text-secondary, #666);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 0.9rem;
}

.details {
  margin: 1.5rem 0;
  text-align: left;
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 6px;
  overflow: hidden;
}

.details summary {
  cursor: pointer;
  padding: 0.75rem 1rem;
  background-color: var(--background-color, #f8f9fa);
  font-weight: 500;
  user-select: none;
  transition: background-color 0.2s ease;
}

.details summary:hover {
  background-color: var(--hover-color, #e9ecef);
}

.details[open] summary {
  border-bottom: 1px solid var(--border-color, #dee2e6);
}

.errorInfo {
  padding: 1rem;
}

.errorMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: var(--text-secondary, #666);
}

.errorMeta span {
  background-color: var(--background-color, #f8f9fa);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: monospace;
}

.error,
.stack {
  background-color: var(--code-background, #f8f9fa);
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 4px;
  padding: 1rem;
  margin: 0.5rem 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.button:hover {
  transform: translateY(-1px);
}

.button:active {
  transform: translateY(0);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.primary {
  background-color: var(--primary-color, #007bff);
  color: white;
}

.primary:hover:not(:disabled) {
  background-color: var(--primary-hover, #0056b3);
}

.secondary {
  background-color: var(--secondary-color, #6c757d);
  color: white;
}

.secondary:hover:not(:disabled) {
  background-color: var(--secondary-hover, #545b62);
}

.tertiary {
  background-color: transparent;
  color: var(--text-color, #333);
  border: 1px solid var(--border-color, #dee2e6);
}

.tertiary:hover:not(:disabled) {
  background-color: var(--background-color, #f8f9fa);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .errorBoundary {
    padding: 1rem;
  }
  
  .content {
    padding: 1.5rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
}