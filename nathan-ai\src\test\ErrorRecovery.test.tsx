import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ErrorRecovery } from '../components/ErrorRecovery';

// Mock window.location.reload
const reloadMock = vi.fn();
Object.defineProperty(window, 'location', {
  value: { reload: reloadMock },
  writable: true,
});

describe('ErrorRecovery Component', () => {
  const mockError = new Error('Test error message');
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default props', () => {
    render(<ErrorRecovery error={mockError} />);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('An error occurred while processing your request.')).toBeInTheDocument();
    expect(screen.getByText('Refresh Page')).toBeInTheDocument();
  });

  it('renders with custom title and message', () => {
    render(
      <ErrorRecovery 
        error={mockError}
        title="Custom Error Title"
        message="Custom error message"
      />
    );

    expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
    expect(screen.getByText('Custom error message')).toBeInTheDocument();
  });

  it('shows retry button when onRetry is provided', () => {
    const onRetryMock = vi.fn();

    render(<ErrorRecovery error={mockError} onRetry={onRetryMock} />);

    const retryButton = screen.getByText('Try Again');
    expect(retryButton).toBeInTheDocument();

    fireEvent.click(retryButton);
    expect(onRetryMock).toHaveBeenCalled();
  });

  it('shows reset button when onReset is provided', () => {
    const onResetMock = vi.fn();

    render(<ErrorRecovery error={mockError} onReset={onResetMock} />);

    const resetButton = screen.getByText('Reset');
    expect(resetButton).toBeInTheDocument();

    fireEvent.click(resetButton);
    expect(onResetMock).toHaveBeenCalled();
  });

  it('refreshes page when refresh button is clicked', () => {
    render(<ErrorRecovery error={mockError} />);

    fireEvent.click(screen.getByText('Refresh Page'));
    expect(reloadMock).toHaveBeenCalled();
  });

  it('shows error details when showDetails is true', () => {
    const errorWithStack = new Error('Test error');
    errorWithStack.stack = 'Error: Test error\n    at test.js:1:1';

    render(<ErrorRecovery error={errorWithStack} showDetails={true} />);

    const detailsElement = screen.getByText('Error Details');
    expect(detailsElement).toBeInTheDocument();

    // Click to expand details
    fireEvent.click(detailsElement);

    expect(screen.getByText(/Test error/)).toBeInTheDocument();
  });

  it('does not show error details when showDetails is false', () => {
    render(<ErrorRecovery error={mockError} showDetails={false} />);

    expect(screen.queryByText('Error Details')).not.toBeInTheDocument();
  });

  it('handles error without stack trace', () => {
    const errorWithoutStack = new Error('Simple error');
    errorWithoutStack.stack = undefined;

    render(<ErrorRecovery error={errorWithoutStack} showDetails={true} />);

    const detailsElement = screen.getByText('Error Details');
    fireEvent.click(detailsElement);

    expect(screen.getByText(/Simple error/)).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    render(<ErrorRecovery error={mockError} />);

    expect(document.querySelector('[class*="errorRecovery"]')).toBeInTheDocument();
    expect(document.querySelector('[class*="content"]')).toBeInTheDocument();
    expect(document.querySelector('[class*="actions"]')).toBeInTheDocument();
  });

  it('renders all buttons when all handlers are provided', () => {
    const onRetryMock = vi.fn();
    const onResetMock = vi.fn();

    render(
      <ErrorRecovery 
        error={mockError} 
        onRetry={onRetryMock}
        onReset={onResetMock}
      />
    );

    expect(screen.getByText('Try Again')).toBeInTheDocument();
    expect(screen.getByText('Reset')).toBeInTheDocument();
    expect(screen.getByText('Refresh Page')).toBeInTheDocument();
  });

  it('displays warning icon', () => {
    render(<ErrorRecovery error={mockError} />);

    expect(screen.getByText('⚠️')).toBeInTheDocument();
  });
});