#!/usr/bin/env node

/**
 * Deployment script for <PERSON> AI
 */

const { execSync } = require('child_process');
const { readFileSync, writeFileSync, existsSync } = require('fs');
const { join } = require('path');

const PROJECT_ROOT = process.cwd();
const DIST_DIR = join(PROJECT_ROOT, 'dist');

// Deployment configuration
const DEPLOYMENT_CONFIG = {
  // Static hosting services
  netlify: {
    buildCommand: 'npm run build',
    publishDir: 'dist',
    redirects: [
      { from: '/*', to: '/index.html', status: 200 }
    ]
  },
  vercel: {
    buildCommand: 'npm run build',
    outputDirectory: 'dist',
    framework: 'vite'
  },
  github_pages: {
    buildCommand: 'npm run build',
    publishDir: 'dist',
    basePath: process.env.GITHUB_REPOSITORY ? `/${process.env.GITHUB_REPOSITORY.split('/')[1]}` : ''
  }
};

console.log('🚀 Starting deployment process...\n');

// Step 1: Validate environment
console.log('1. Validating deployment environment...');
validateEnvironment();
console.log('✅ Environment validation passed\n');

// Step 2: Run production build
console.log('2. Running production build...');
try {
  execSync('node scripts/build.cjs', { stdio: 'inherit' });
  console.log('✅ Production build completed\n');
} catch (error) {
  console.error('❌ Production build failed');
  process.exit(1);
}

// Step 3: Generate deployment configurations
console.log('3. Generating deployment configurations...');
generateDeploymentConfigs();
console.log('✅ Deployment configurations generated\n');

// Step 4: Run health checks
console.log('4. Running pre-deployment health checks...');
runHealthChecks();
console.log('✅ Health checks passed\n');

// Step 5: Deploy based on environment
const deploymentTarget = process.env.DEPLOYMENT_TARGET || 'static';
console.log(`5. Deploying to ${deploymentTarget}...`);

switch (deploymentTarget) {
  case 'netlify':
    deployToNetlify();
    break;
  case 'vercel':
    deployToVercel();
    break;
  case 'github-pages':
    deployToGitHubPages();
    break;
  case 'static':
  default:
    prepareStaticDeployment();
    break;
}

console.log('🎉 Deployment completed successfully!\n');

/**
 * Validate deployment environment
 */
function validateEnvironment() {
  // Check if dist directory exists
  if (!existsSync(DIST_DIR)) {
    throw new Error('Dist directory not found. Run build first.');
  }
  
  // Check required files
  const requiredFiles = ['index.html'];
  requiredFiles.forEach(file => {
    if (!existsSync(join(DIST_DIR, file))) {
      throw new Error(`Required file ${file} not found in dist directory`);
    }
  });
  
  // Validate environment variables for production
  const requiredEnvVars = ['NODE_ENV'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log(`⚠️  Missing environment variables: ${missingVars.join(', ')}`);
    console.log('Setting NODE_ENV to production...');
    process.env.NODE_ENV = 'production';
  }
}

/**
 * Generate deployment configuration files
 */
function generateDeploymentConfigs() {
  // Netlify configuration
  const netlifyConfig = {
    build: DEPLOYMENT_CONFIG.netlify,
    redirects: DEPLOYMENT_CONFIG.netlify.redirects,
    headers: [
      {
        for: '/js/*',
        values: {
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      },
      {
        for: '/css/*',
        values: {
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      },
      {
        for: '/images/*',
        values: {
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      },
      {
        for: '/*',
        values: {
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block',
          'X-Content-Type-Options': 'nosniff',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
          'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io; media-src 'self' blob:;"
        }
      }
    ]
  };
  
  writeFileSync(
    join(PROJECT_ROOT, 'netlify.toml'),
    `[build]
  command = "${netlifyConfig.build.buildCommand}"
  publish = "${netlifyConfig.build.publishDir}"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/css/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io; media-src 'self' blob:;"
`
  );
  
  // Vercel configuration
  const vercelConfig = {
    version: 2,
    builds: [
      {
        src: 'package.json',
        use: '@vercel/static-build',
        config: {
          distDir: 'dist'
        }
      }
    ],
    routes: [
      {
        src: '/js/(.*)',
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      },
      {
        src: '/css/(.*)',
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      },
      {
        src: '/(.*)',
        dest: '/index.html'
      }
    ],
    headers: [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ]
  };
  
  writeFileSync(
    join(PROJECT_ROOT, 'vercel.json'),
    JSON.stringify(vercelConfig, null, 2)
  );
  
  // GitHub Pages workflow
  const githubWorkflow = `name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Build
      run: npm run build
      env:
        NODE_ENV: production
        
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: \${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
`;
  
  const workflowDir = join(PROJECT_ROOT, '.github', 'workflows');
  execSync(`mkdir -p "${workflowDir}"`, { stdio: 'inherit' });
  writeFileSync(join(workflowDir, 'deploy.yml'), githubWorkflow);
}

/**
 * Run health checks before deployment
 */
function runHealthChecks() {
  const checks = [];
  
  // Check 1: Verify index.html exists and is valid
  const indexPath = join(DIST_DIR, 'index.html');
  if (existsSync(indexPath)) {
    const indexContent = readFileSync(indexPath, 'utf8');
    if (indexContent.includes('<div id="root">')) {
      checks.push({ name: 'Index HTML', status: 'pass' });
    } else {
      checks.push({ name: 'Index HTML', status: 'fail', message: 'Missing root div' });
    }
  } else {
    checks.push({ name: 'Index HTML', status: 'fail', message: 'File not found' });
  }
  
  // Check 2: Verify JavaScript bundles exist
  const jsDir = join(DIST_DIR, 'js');
  if (existsSync(jsDir)) {
    const jsFiles = require('fs').readdirSync(jsDir).filter(f => f.endsWith('.js'));
    if (jsFiles.length > 0) {
      checks.push({ name: 'JavaScript bundles', status: 'pass', count: jsFiles.length });
    } else {
      checks.push({ name: 'JavaScript bundles', status: 'fail', message: 'No JS files found' });
    }
  } else {
    checks.push({ name: 'JavaScript bundles', status: 'fail', message: 'JS directory not found' });
  }
  
  // Check 3: Verify CSS files exist
  const cssDir = join(DIST_DIR, 'css');
  if (existsSync(cssDir)) {
    const cssFiles = require('fs').readdirSync(cssDir).filter(f => f.endsWith('.css'));
    checks.push({ name: 'CSS files', status: 'pass', count: cssFiles.length });
  } else {
    checks.push({ name: 'CSS files', status: 'warn', message: 'CSS directory not found' });
  }
  
  // Check 4: Verify assets
  const assetsDir = join(DIST_DIR, 'assets');
  if (existsSync(assetsDir)) {
    checks.push({ name: 'Assets directory', status: 'pass' });
  } else {
    checks.push({ name: 'Assets directory', status: 'warn', message: 'Assets directory not found' });
  }
  
  // Report results
  console.log('Health Check Results:');
  checks.forEach(check => {
    const icon = check.status === 'pass' ? '✅' : check.status === 'warn' ? '⚠️' : '❌';
    const message = check.message ? ` (${check.message})` : '';
    const count = check.count ? ` (${check.count} files)` : '';
    console.log(`   ${icon} ${check.name}${count}${message}`);
  });
  
  const failures = checks.filter(check => check.status === 'fail');
  if (failures.length > 0) {
    throw new Error(`Health checks failed: ${failures.map(f => f.name).join(', ')}`);
  }
}

/**
 * Deploy to Netlify
 */
function deployToNetlify() {
  console.log('Deploying to Netlify...');
  console.log('📝 Netlify configuration generated at netlify.toml');
  console.log('🔗 Connect your repository to Netlify and it will auto-deploy');
  console.log('💡 Make sure to set environment variables in Netlify dashboard');
}

/**
 * Deploy to Vercel
 */
function deployToVercel() {
  console.log('Deploying to Vercel...');
  console.log('📝 Vercel configuration generated at vercel.json');
  console.log('🔗 Connect your repository to Vercel and it will auto-deploy');
  console.log('💡 Make sure to set environment variables in Vercel dashboard');
}

/**
 * Deploy to GitHub Pages
 */
function deployToGitHubPages() {
  console.log('Deploying to GitHub Pages...');
  console.log('📝 GitHub Actions workflow generated at .github/workflows/deploy.yml');
  console.log('🔗 Push to main branch to trigger deployment');
  console.log('💡 Enable GitHub Pages in repository settings');
}

/**
 * Prepare static deployment
 */
function prepareStaticDeployment() {
  console.log('Preparing static deployment...');
  
  // Create deployment instructions
  const instructions = `# Static Deployment Instructions

## Files Ready for Deployment
Your application has been built and is ready for deployment. The \`dist\` directory contains all the files needed.

## Deployment Options

### 1. Static File Server
Upload the contents of the \`dist\` directory to any static file server:
- Apache
- Nginx  
- AWS S3 + CloudFront
- Google Cloud Storage
- Azure Static Web Apps

### 2. CDN Deployment
For optimal performance, use a CDN:
- Cloudflare
- AWS CloudFront
- Google Cloud CDN
- Azure CDN

### 3. Container Deployment
Use the included Dockerfile to deploy in a container:
\`\`\`bash
docker build -t nathan-ai .
docker run -p 3000:3000 nathan-ai
\`\`\`

## Environment Variables
Make sure to set these environment variables in your deployment environment:
- NODE_ENV=production
- VITE_HUGGINGFACE_API_KEY=your_api_key
- VITE_ELEVENLABS_API_KEY=your_api_key
- VITE_ELEVENLABS_VOICE_ID=your_voice_id

## Security Headers
Configure your server to include these security headers:
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin
- Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io; media-src 'self' blob:;

## Caching
Configure caching for optimal performance:
- Static assets (JS, CSS, images): 1 year
- HTML files: No cache or short cache (1 hour)

## Health Check
Your deployment should respond to GET / with a 200 status code.
`;
  
  writeFileSync(join(DIST_DIR, 'DEPLOYMENT.md'), instructions);
  
  // Create simple Dockerfile
  const dockerfile = `FROM nginx:alpine

# Copy built application
COPY dist/ /usr/share/nginx/html/

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
`;
  
  writeFileSync(join(PROJECT_ROOT, 'Dockerfile'), dockerfile);
  
  // Create nginx configuration
  const nginxConfig = `events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options nosniff;
    add_header Referrer-Policy strict-origin-when-cross-origin;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io; media-src 'self' blob:;";
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # Cache static assets
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # SPA routing
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\\n";
            add_header Content-Type text/plain;
        }
    }
}
`;
  
  writeFileSync(join(PROJECT_ROOT, 'nginx.conf'), nginxConfig);
  
  console.log('✅ Static deployment files created:');
  console.log('   📄 DEPLOYMENT.md - Deployment instructions');
  console.log('   🐳 Dockerfile - Container deployment');
  console.log('   ⚙️  nginx.conf - Nginx configuration');
}