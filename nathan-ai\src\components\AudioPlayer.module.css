.audioPlayer {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 40px;
  transition: all 0.3s ease;
}

.audioPlayer:empty {
  display: none;
}

/* Loading indicator */
.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64b5f6;
  font-size: 14px;
}

.loadingSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(100, 181, 246, 0.3);
  border-top: 2px solid #64b5f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-weight: 500;
}

/* Error message */
.errorMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f48fb1;
  font-size: 14px;
  background: rgba(244, 143, 177, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(244, 143, 177, 0.2);
}

.errorIcon {
  font-size: 16px;
}

.errorText {
  font-weight: 500;
  flex: 1;
}

/* Now playing indicator */
.nowPlaying {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #81c784;
  font-size: 14px;
  background: rgba(129, 199, 132, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(129, 199, 132, 0.2);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.playingIcon {
  font-size: 16px;
  animation: soundWave 1.5s ease-in-out infinite;
}

@keyframes soundWave {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.playingText {
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Queue information */
.queueInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}

.queueCount {
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .audioPlayer {
    padding: 6px;
    gap: 6px;
  }
  
  .loadingIndicator,
  .errorMessage,
  .nowPlaying {
    font-size: 13px;
    padding: 6px 10px;
  }
  
  .playingText {
    font-size: 13px;
  }
  
  .queueInfo {
    font-size: 11px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .audioPlayer {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .loadingIndicator {
    color: #90caf9;
  }
  
  .loadingSpinner {
    border-color: rgba(144, 202, 249, 0.3);
    border-top-color: #90caf9;
  }
  
  .errorMessage {
    color: #f8bbd9;
    background: rgba(248, 187, 217, 0.1);
    border-color: rgba(248, 187, 217, 0.2);
  }
  
  .nowPlaying {
    color: #a5d6a7;
    background: rgba(165, 214, 167, 0.1);
    border-color: rgba(165, 214, 167, 0.2);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .audioPlayer {
    border-width: 2px;
  }
  
  .loadingIndicator,
  .errorMessage,
  .nowPlaying {
    border-width: 2px;
    font-weight: 600;
  }
}

/* Screen reader only content */
.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Debug info (development only) */
.debugInfo {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  z-index: 9999;
  max-width: 200px;
}

.debugInfo div {
  margin: 2px 0;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .loadingSpinner,
  .pulse,
  .soundWave {
    animation: none;
  }
  
  .audioPlayer {
    transition: none;
  }
}