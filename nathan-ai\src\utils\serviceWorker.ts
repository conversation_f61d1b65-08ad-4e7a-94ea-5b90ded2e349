/**
 * Service Worker registration and management for mobile optimization
 */

// Check if service workers are supported
export const isServiceWorkerSupported = (): boolean => {
  return 'serviceWorker' in navigator;
};

// Register service worker with error handling
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if (!isServiceWorkerSupported()) {
    console.log('Service Worker not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    });

    console.log('Service Worker registered successfully:', registration);

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available, notify user
            notifyUserOfUpdate();
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
    return null;
  }
};

// Unregister service worker
export const unregisterServiceWorker = async (): Promise<boolean> => {
  if (!isServiceWorkerSupported()) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('Service Worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('Service Worker unregistration failed:', error);
    return false;
  }
};

// Check if app is running in standalone mode (PWA)
export const isStandalone = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true;
};

// Check if app can be installed (PWA)
export const canInstallPWA = (): boolean => {
  return 'beforeinstallprompt' in window;
};

// Notify user of app update
const notifyUserOfUpdate = () => {
  // Create a custom event for the app to handle
  const updateEvent = new CustomEvent('sw-update-available', {
    detail: {
      message: 'A new version of Nathan AI is available. Refresh to update.',
      action: 'refresh'
    }
  });
  
  window.dispatchEvent(updateEvent);
};

// Force update service worker
export const updateServiceWorker = async (): Promise<void> => {
  if (!isServiceWorkerSupported()) {
    return;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      await registration.update();
      
      // If there's a waiting worker, skip waiting and reload
      if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    }
  } catch (error) {
    console.error('Service Worker update failed:', error);
  }
};

// Get cache usage information
export const getCacheUsage = async (): Promise<{ used: number; quota: number } | null> => {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0
      };
    } catch (error) {
      console.error('Failed to get cache usage:', error);
    }
  }
  return null;
};

// Clear all caches
export const clearAllCaches = async (): Promise<void> => {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('All caches cleared');
    } catch (error) {
      console.error('Failed to clear caches:', error);
    }
  }
};

// Background sync support
export const requestBackgroundSync = async (tag: string): Promise<void> => {
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.sync.register(tag);
      console.log('Background sync registered:', tag);
    } catch (error) {
      console.error('Background sync registration failed:', error);
    }
  }
};

// Queue message for background sync
export const queueMessageForSync = (message: any): void => {
  // Store in localStorage for now (could be upgraded to IndexedDB)
  const queueKey = 'nathan-message-queue';
  const existingQueue = JSON.parse(localStorage.getItem(queueKey) || '[]');
  
  const queuedMessage = {
    id: Date.now().toString(),
    message,
    timestamp: new Date().toISOString()
  };
  
  existingQueue.push(queuedMessage);
  localStorage.setItem(queueKey, JSON.stringify(existingQueue));
  
  // Request background sync
  requestBackgroundSync('background-sync-messages');
};

// Get network status
export const getNetworkStatus = (): { online: boolean; effectiveType?: string } => {
  const online = navigator.onLine;
  let effectiveType = 'unknown';
  
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    effectiveType = connection.effectiveType || 'unknown';
  }
  
  return { online, effectiveType };
};

// Listen for network changes
export const onNetworkChange = (callback: (online: boolean) => void): (() => void) => {
  const handleOnline = () => callback(true);
  const handleOffline = () => callback(false);
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};