import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import React from 'react';
import { conversationReducer, ConversationProvider, useConversationContext } from '../context/ConversationContext';
import { PersonalityProvider, usePersonalityContext } from '../context/PersonalityContext';
import type { ConversationState, ConversationAction } from '../types/conversation';
import type { Message } from '../types/message';

// Mock the personality utility
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn().mockResolvedValue({
    name: '<PERSON>',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['coding', 'music'],
      style: {
        speech: 'casual',
        humor: 'witty',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: ['politics'],
        safe_topics: ['technology', 'hobbies']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How are you?']
    },
    version: '1.0.0'
  }),
  savePersonality: vi.fn().mockResolvedValue(undefined)
}));

describe('conversationReducer', () => {
  const initialState: ConversationState = {
    messages: [],
    isLoading: false,
    inputMode: 'text',
    visualMode: 'visual',
    isListening: false,
    isPlaying: false,
    error: null,
  };

  const mockMessage: Message = {
    id: 'test-1',
    content: 'Hello',
    timestamp: new Date(),
    sender: 'user',
  };

  it('should handle ADD_MESSAGE action', () => {
    const action: ConversationAction = {
      type: 'ADD_MESSAGE',
      payload: mockMessage,
    };

    const newState = conversationReducer(initialState, action);

    expect(newState.messages).toHaveLength(1);
    expect(newState.messages[0]).toEqual(mockMessage);
    expect(newState.error).toBeNull();
  });

  it('should handle SET_LOADING action', () => {
    const action: ConversationAction = {
      type: 'SET_LOADING',
      payload: true,
    };

    const newState = conversationReducer(initialState, action);

    expect(newState.isLoading).toBe(true);
  });

  it('should handle SET_INPUT_MODE action', () => {
    const action: ConversationAction = {
      type: 'SET_INPUT_MODE',
      payload: 'voice',
    };

    const newState = conversationReducer(initialState, action);

    expect(newState.inputMode).toBe('voice');
  });

  it('should stop listening when switching away from voice mode', () => {
    const stateWithListening = {
      ...initialState,
      inputMode: 'voice' as const,
      isListening: true,
    };

    const action: ConversationAction = {
      type: 'SET_INPUT_MODE',
      payload: 'text',
    };

    const newState = conversationReducer(stateWithListening, action);

    expect(newState.inputMode).toBe('text');
    expect(newState.isListening).toBe(false);
  });

  it('should handle SET_VISUAL_MODE action', () => {
    const action: ConversationAction = {
      type: 'SET_VISUAL_MODE',
      payload: 'minimal',
    };

    const newState = conversationReducer(initialState, action);

    expect(newState.visualMode).toBe('minimal');
  });

  it('should handle SET_LISTENING action', () => {
    const action: ConversationAction = {
      type: 'SET_LISTENING',
      payload: true,
    };

    const newState = conversationReducer(initialState, action);

    expect(newState.isListening).toBe(true);
  });

  it('should clear error when starting to listen', () => {
    const stateWithError = {
      ...initialState,
      error: 'Some error',
    };

    const action: ConversationAction = {
      type: 'SET_LISTENING',
      payload: true,
    };

    const newState = conversationReducer(stateWithError, action);

    expect(newState.isListening).toBe(true);
    expect(newState.error).toBeNull();
  });

  it('should handle SET_PLAYING action', () => {
    const action: ConversationAction = {
      type: 'SET_PLAYING',
      payload: true,
    };

    const newState = conversationReducer(initialState, action);

    expect(newState.isPlaying).toBe(true);
  });

  it('should handle SET_ERROR action', () => {
    const stateWithActivity = {
      ...initialState,
      isLoading: true,
      isListening: true,
    };

    const action: ConversationAction = {
      type: 'SET_ERROR',
      payload: 'Test error',
    };

    const newState = conversationReducer(stateWithActivity, action);

    expect(newState.error).toBe('Test error');
    expect(newState.isLoading).toBe(false);
    expect(newState.isListening).toBe(false);
  });

  it('should handle CLEAR_MESSAGES action', () => {
    const stateWithMessages = {
      ...initialState,
      messages: [mockMessage],
      error: 'Some error',
    };

    const action: ConversationAction = {
      type: 'CLEAR_MESSAGES',
    };

    const newState = conversationReducer(stateWithMessages, action);

    expect(newState.messages).toHaveLength(0);
    expect(newState.error).toBeNull();
  });

  it('should return current state for unknown action', () => {
    const unknownAction = { type: 'UNKNOWN_ACTION' } as any;

    const newState = conversationReducer(initialState, unknownAction);

    expect(newState).toBe(initialState);
  });
});

describe('ConversationProvider', () => {
  it('should provide conversation context', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      React.createElement(ConversationProvider, {}, children)
    );

    const { result } = renderHook(() => useConversationContext(), { wrapper });

    expect(result.current.state).toBeDefined();
    expect(result.current.dispatch).toBeDefined();
    expect(typeof result.current.dispatch).toBe('function');
  });

  it('should accept initial state', () => {
    const initialState = { inputMode: 'voice' as const };
    
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      React.createElement(ConversationProvider, { initialState }, children)
    );

    const { result } = renderHook(() => useConversationContext(), { wrapper });

    expect(result.current.state.inputMode).toBe('voice');
  });

  it('should throw error when used outside provider', () => {
    expect(() => {
      renderHook(() => useConversationContext());
    }).toThrow('useConversationContext must be used within a ConversationProvider');
  });
});

describe('PersonalityProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should provide personality context', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      React.createElement(PersonalityProvider, {}, children)
    );

    const { result } = renderHook(() => usePersonalityContext(), { wrapper });

    expect(result.current.personality).toBeDefined();
    expect(result.current.isLoading).toBeDefined();
    expect(result.current.error).toBeDefined();
    expect(result.current.updatePersonality).toBeDefined();
    expect(result.current.resetPersonality).toBeDefined();
  });

  it('should load personality on mount', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      React.createElement(PersonalityProvider, {}, children)
    );

    const { result } = renderHook(() => usePersonalityContext(), { wrapper });

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    // Wait for personality to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.personality).toBeDefined();
    expect(result.current.personality?.name).toBe('Nathan');
  });

  it('should throw error when used outside provider', () => {
    expect(() => {
      renderHook(() => usePersonalityContext());
    }).toThrow('usePersonalityContext must be used within a PersonalityProvider');
  });
});