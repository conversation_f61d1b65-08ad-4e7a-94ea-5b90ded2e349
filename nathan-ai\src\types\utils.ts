// Utility types for Nathan AI

// Generic API response wrapper
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  error?: string;
  timestamp: Date;
}

// Generic loading state
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Environment configuration
export interface EnvironmentConfig {
  HUGGING_FACE_API_KEY: string;
  ELEVEN_LABS_API_KEY: string;
  ELEVEN_LABS_VOICE_ID: string;
  NODE_ENV: 'development' | 'production' | 'test';
}

// Browser capability detection
export interface BrowserCapabilities {
  speechRecognition: boolean;
  audioContext: boolean;
  mediaDevices: boolean;
  localStorage: boolean;
}

// Performance metrics
export interface PerformanceMetrics {
  apiResponseTime: number;
  ttsGenerationTime: number;
  audioPlaybackLatency: number;
  messageProcessingTime: number;
}

// Configuration validation
export type ValidationResult<T> = {
  isValid: boolean;
  data?: T;
  errors: string[];
};

// Event handler types
export type EventHandler<T = void> = (event: T) => void;
export type AsyncEventHandler<T = void> = (event: T) => Promise<void>;

// Storage types
export interface StorageAdapter {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

// Retry configuration
export interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier: number;
}

// Deep partial type for configuration updates
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Omit multiple keys utility
export type OmitMultiple<T, K extends keyof T> = Omit<T, K>;

// Pick multiple keys utility
export type PickMultiple<T, K extends keyof T> = Pick<T, K>;