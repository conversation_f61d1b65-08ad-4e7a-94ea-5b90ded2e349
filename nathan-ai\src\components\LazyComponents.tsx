import { Suspense, ComponentType } from 'react';
import { mobileOptimizedLazy, shouldUseAggressiveOptimization } from '../utils/lazyLoading';

// Loading fallback component optimized for mobile
const MobileLoadingFallback = ({ componentName }: { componentName: string }) => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '1rem',
    minHeight: '60px',
    fontSize: '0.9rem',
    color: 'var(--text-secondary, #666)'
  }}>
    <div style={{
      width: '20px',
      height: '20px',
      border: '2px solid #f3f3f3',
      borderTop: '2px solid #3498db',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginRight: '0.5rem'
    }} />
    Loading {componentName}...
  </div>
);

// Lazy-loaded components with mobile optimization
export const LazyAudioPlayer = mobileOptimizedLazy(
  () => import('./AudioPlayer').then(module => ({ default: module.default || module.AudioPlayer })),
  { preload: false, priority: 'low' }
);

export const LazyVoiceInput = mobileOptimizedLazy(
  () => import('./VoiceInput').then(module => ({ default: module.default || module.VoiceInput })),
  { preload: true, priority: 'high' }
);

export const LazyAvatar = mobileOptimizedLazy(
  () => import('./Avatar').then(module => ({ default: module.default || module.Avatar })),
  { preload: shouldUseAggressiveOptimization() ? false : true, priority: 'low' }
);

export const LazyCompatibilityWarning = mobileOptimizedLazy(
  () => import('./CompatibilityWarning').then(module => ({ default: module.default || module.CompatibilityWarning })),
  { preload: false, priority: 'low' }
);

export const LazyNotificationSystem = mobileOptimizedLazy(
  () => import('./NotificationSystem').then(module => ({ default: module.default || module.NotificationSystem })),
  { preload: true, priority: 'high' }
);

// Wrapper component with mobile-optimized suspense
export const LazyWrapper = ({ 
  children, 
  fallback, 
  componentName = 'Component' 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
  componentName?: string;
}) => (
  <Suspense fallback={fallback || <MobileLoadingFallback componentName={componentName} />}>
    {children}
  </Suspense>
);

// Higher-order component for lazy loading with error boundary
export const withLazyLoading = <P extends object>(
  Component: ComponentType<P>,
  componentName: string,
  options?: { priority?: 'high' | 'low'; preload?: boolean }
) => {
  const LazyComponent = mobileOptimizedLazy(
    () => Promise.resolve({ default: Component }),
    options
  );

  return (props: P) => (
    <LazyWrapper componentName={componentName}>
      <LazyComponent {...props} />
    </LazyWrapper>
  );
};