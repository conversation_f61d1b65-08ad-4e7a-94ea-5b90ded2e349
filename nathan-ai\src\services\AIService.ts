import { HuggingFaceClient } from './HuggingFaceClient';
import { ElevenLabsClient } from './ElevenLabsClient';
import { ApiErrorHandler } from './ApiErrorHandler';
import type { PersonalityConfig } from '../types/personality';
import type { Message } from '../types/message';
import type { GenerationOptions, TTSOptions } from '../types/api';
import { HFError, ELError } from '../types/api';

export interface AIServiceOptions {
  enableTTS?: boolean;
  enableCaching?: boolean;
  maxCacheSize?: number;
  conversationHistoryLimit?: number;
}

export interface ConversationResponse {
  text: string;
  audio?: ArrayBuffer;
  emotion?: string;
  cached?: boolean;
}

export class AIService {
  private huggingFaceClient: HuggingFaceClient;
  private elevenLabsClient: ElevenLabsClient;
  private errorHandler: ApiErrorHandler;
  private responseCache: Map<string, ConversationResponse>;
  private conversationHistory: string[];
  private options: Required<AIServiceOptions>;

  constructor(options: AIServiceOptions = {}) {
    this.huggingFaceClient = new HuggingFaceClient();
    this.elevenLabsClient = new ElevenLabsClient();
    this.errorHandler = new ApiErrorHandler();
    this.responseCache = new Map();
    this.conversationHistory = [];
    
    this.options = {
      enableTTS: options.enableTTS ?? true,
      enableCaching: options.enableCaching ?? true,
      maxCacheSize: options.maxCacheSize ?? 100,
      conversationHistoryLimit: options.conversationHistoryLimit ?? 10
    };

    // Setup error handler notifications
    this.setupErrorHandling();
  }

  /**
   * Generate a complete conversation response with text and optional audio
   */
  async generateResponse(
    userMessage: string,
    personality: PersonalityConfig,
    generationOptions?: GenerationOptions,
    ttsOptions?: TTSOptions
  ): Promise<ConversationResponse> {
    // Create cache key
    const cacheKey = this.createCacheKey(userMessage, personality, this.conversationHistory);
    
    // Check cache first
    if (this.options.enableCaching && this.responseCache.has(cacheKey)) {
      const cachedResponse = this.responseCache.get(cacheKey)!;
      return { ...cachedResponse, cached: true };
    }

    try {
      // Check if offline and queue request if needed
      if (!this.errorHandler.isCurrentlyOnline()) {
        this.errorHandler.addToOfflineQueue('text', {
          userMessage,
          personality,
          generationOptions,
          ttsOptions
        });
        throw new Error('Currently offline. Request has been queued.');
      }

      // Generate text response with retry logic
      const textResponse = await this.errorHandler.executeWithRetry(
        () => this.huggingFaceClient.generatePersonalityResponse(
          userMessage,
          personality,
          this.conversationHistory,
          generationOptions
        ),
        'HuggingFace text generation'
      );

      // Update conversation history
      this.updateConversationHistory(userMessage, textResponse);

      // Generate audio if enabled with graceful degradation
      let audioBuffer: ArrayBuffer | undefined;
      if (this.options.enableTTS) {
        try {
          audioBuffer = await this.errorHandler.executeWithRetry(
            () => this.elevenLabsClient.textToSpeech(textResponse, ttsOptions),
            'ElevenLabs audio generation'
          );
        } catch (error) {
          console.warn('TTS generation failed, continuing with text only:', error);
          
          // Handle TTS-specific errors
          if (error instanceof ELError) {
            this.errorHandler.handleElevenLabsError(error);
          }
          
          // Continue without audio rather than failing completely
        }
      }

      // Detect emotion from response (simple implementation)
      const emotion = this.detectEmotion(textResponse);

      const response: ConversationResponse = {
        text: textResponse,
        audio: audioBuffer,
        emotion,
        cached: false
      };

      // Cache the response
      if (this.options.enableCaching) {
        this.cacheResponse(cacheKey, response);
      }

      return response;

    } catch (error) {
      console.error('AI Service error:', error);
      
      // Handle specific error types
      if (error instanceof HFError) {
        this.errorHandler.handleHuggingFaceError(error);
      } else if (error instanceof ELError) {
        this.errorHandler.handleElevenLabsError(error);
      } else {
        this.errorHandler.showUserFriendlyMessage('api');
      }
      
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate text-only response (faster, no TTS)
   */
  async generateTextResponse(
    userMessage: string,
    personality: PersonalityConfig,
    generationOptions?: GenerationOptions
  ): Promise<string> {
    try {
      // Check if offline and queue request if needed
      if (!this.errorHandler.isCurrentlyOnline()) {
        this.errorHandler.addToOfflineQueue('text', {
          userMessage,
          personality,
          generationOptions
        });
        throw new Error('Currently offline. Request has been queued.');
      }

      const response = await this.errorHandler.executeWithRetry(
        () => this.huggingFaceClient.generatePersonalityResponse(
          userMessage,
          personality,
          this.conversationHistory,
          generationOptions
        ),
        'HuggingFace text generation'
      );

      this.updateConversationHistory(userMessage, response);
      return response;

    } catch (error) {
      console.error('Text generation error:', error);
      
      if (error instanceof HFError) {
        this.errorHandler.handleHuggingFaceError(error);
      } else {
        this.errorHandler.showUserFriendlyMessage('api');
      }
      
      throw new Error(`Failed to generate text response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate audio for existing text
   */
  async generateAudio(
    text: string,
    ttsOptions?: TTSOptions
  ): Promise<ArrayBuffer> {
    try {
      // Check if offline and queue request if needed
      if (!this.errorHandler.isCurrentlyOnline()) {
        this.errorHandler.addToOfflineQueue('audio', {
          text,
          ttsOptions
        });
        throw new Error('Currently offline. Request has been queued.');
      }

      return await this.errorHandler.executeWithRetry(
        () => this.elevenLabsClient.textToSpeech(text, ttsOptions),
        'ElevenLabs audio generation'
      );
    } catch (error) {
      console.error('Audio generation error:', error);
      
      if (error instanceof ELError) {
        this.errorHandler.handleElevenLabsError(error);
      } else {
        this.errorHandler.showUserFriendlyMessage('api');
      }
      
      throw new Error(`Failed to generate audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process a conversation flow with multiple exchanges
   */
  async processConversationFlow(
    messages: Message[],
    personality: PersonalityConfig,
    options?: {
      generateAudio?: boolean;
      generationOptions?: GenerationOptions;
      ttsOptions?: TTSOptions;
    }
  ): Promise<ConversationResponse[]> {
    const responses: ConversationResponse[] = [];
    
    // Clear history to start fresh
    this.clearConversationHistory();

    for (const message of messages) {
      if (message.sender === 'user') {
        const response = await this.generateResponse(
          message.content,
          personality,
          options?.generationOptions,
          options?.generateAudio ? options.ttsOptions : undefined
        );
        responses.push(response);
      }
    }

    return responses;
  }

  /**
   * Setup error handling notifications
   */
  private setupErrorHandling(): void {
    this.errorHandler.onNotification((notification) => {
      // Use global notification system if available
      if ((window as any).__addNotification) {
        (window as any).__addNotification(notification);
      } else {
        // Fallback to console logging
        console.log(`Notification: ${notification.title} - ${notification.message}`);
      }
    });
  }

  /**
   * Update conversation history with new exchange
   */
  private updateConversationHistory(userMessage: string, aiResponse: string): void {
    this.conversationHistory.push(`User: ${userMessage}`);
    this.conversationHistory.push(`${this.getCurrentPersonalityName()}: ${aiResponse}`);

    // Limit history size
    if (this.conversationHistory.length > this.options.conversationHistoryLimit * 2) {
      this.conversationHistory = this.conversationHistory.slice(-this.options.conversationHistoryLimit * 2);
    }
  }

  /**
   * Get current personality name for history formatting
   */
  private getCurrentPersonalityName(): string {
    // This could be enhanced to track the current personality
    return 'Nathan'; // Default for now
  }

  /**
   * Create cache key for response caching
   */
  private createCacheKey(
    userMessage: string,
    personality: PersonalityConfig,
    history: string[]
  ): string {
    const historyKey = history.slice(-4).join('|'); // Last 4 history items
    const personalityKey = `${personality.name}-${personality.version}`;
    return `${personalityKey}:${historyKey}:${userMessage}`;
  }

  /**
   * Cache a response
   */
  private cacheResponse(key: string, response: ConversationResponse): void {
    // Remove oldest entries if cache is full
    if (this.responseCache.size >= this.options.maxCacheSize) {
      const firstKey = this.responseCache.keys().next().value;
      if (firstKey) {
        this.responseCache.delete(firstKey);
      }
    }

    // Don't cache the audio buffer to save memory, only text and metadata
    this.responseCache.set(key, {
      text: response.text,
      emotion: response.emotion,
      cached: false
    });
  }

  /**
   * Simple emotion detection from text response
   */
  private detectEmotion(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'neutral';
    }
    
    const lowerText = text.toLowerCase();
    
    // Simple keyword-based emotion detection
    if (lowerText.includes('!') || lowerText.includes('excited') || lowerText.includes('amazing')) {
      return 'excited';
    }
    if (lowerText.includes('sorry') || lowerText.includes('sad') || lowerText.includes('unfortunately')) {
      return 'empathetic';
    }
    if (lowerText.includes('?') || lowerText.includes('curious') || lowerText.includes('interesting')) {
      return 'curious';
    }
    if (lowerText.includes('haha') || lowerText.includes('funny') || lowerText.includes('😄')) {
      return 'happy';
    }
    
    return 'neutral';
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): string[] {
    return [...this.conversationHistory];
  }

  /**
   * Clear conversation history
   */
  clearConversationHistory(): void {
    this.conversationHistory = [];
  }

  /**
   * Set conversation history (useful for restoring state)
   */
  setConversationHistory(history: string[]): void {
    this.conversationHistory = [...history];
  }

  /**
   * Clear response cache
   */
  clearCache(): void {
    this.responseCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; maxSize: number; hitRate?: number } {
    return {
      size: this.responseCache.size,
      maxSize: this.options.maxCacheSize
    };
  }

  /**
   * Update service options
   */
  updateOptions(newOptions: Partial<AIServiceOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{
    huggingFace: boolean;
    elevenLabs: boolean;
    overall: boolean;
    online: boolean;
  }> {
    const results = {
      huggingFace: false,
      elevenLabs: false,
      overall: false,
      online: this.errorHandler.isCurrentlyOnline()
    };

    if (!results.online) {
      return results;
    }

    try {
      // Test HuggingFace with a simple request
      await this.huggingFaceClient.generateText('test', { max_length: 10 });
      results.huggingFace = true;
    } catch (error) {
      console.warn('HuggingFace health check failed:', error);
      if (error instanceof HFError) {
        this.errorHandler.handleHuggingFaceError(error);
      }
    }

    try {
      // Test ElevenLabs with a simple request
      await this.elevenLabsClient.textToSpeech('test');
      results.elevenLabs = true;
    } catch (error) {
      console.warn('ElevenLabs health check failed:', error);
      if (error instanceof ELError) {
        this.errorHandler.handleElevenLabsError(error);
      }
    }

    results.overall = results.huggingFace && (results.elevenLabs || !this.options.enableTTS);
    return results;
  }

  /**
   * Get error handler instance for external use
   */
  getErrorHandler(): ApiErrorHandler {
    return this.errorHandler;
  }

  /**
   * Get offline queue status
   */
  getOfflineQueueStatus() {
    return this.errorHandler.getOfflineQueueStatus();
  }

  /**
   * Clear offline queue
   */
  clearOfflineQueue(): void {
    this.errorHandler.clearOfflineQueue();
  }
}