import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { BrowserCompatibility } from '../services/BrowserCompatibility';

// Mock navigator and window objects
const mockNavigator = {
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  mediaDevices: {
    getUserMedia: vi.fn()
  }
};

const mockWindow = {
  SpeechRecognition: vi.fn(),
  webkitSpeechRecognition: vi.fn(),
  Audio: vi.fn(),
  AudioContext: vi.fn(),
  webkitAudioContext: vi.fn(),
  Notification: vi.fn(),
  localStorage: {
    setItem: vi.fn(),
    removeItem: vi.fn()
  }
};

// Mock global objects
Object.defineProperty(global, 'navigator', {
  value: mockNavigator,
  writable: true
});

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});

describe('BrowserCompatibility', () => {
  let compatibility: BrowserCompatibility;

  beforeEach(() => {
    vi.clearAllMocks();
    compatibility = new BrowserCompatibility();
  });

  describe('Browser Detection', () => {
    it('should detect Chrome browser', () => {
      const browserInfo = compatibility.getBrowserInfo();
      expect(browserInfo.name).toBe('Chrome');
      expect(browserInfo.isChrome).toBe(true);
      expect(browserInfo.isMobile).toBe(false);
    });

    it('should detect mobile browsers', () => {
      // Mock mobile user agent
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        writable: true
      });

      const mobileCompatibility = new BrowserCompatibility();
      const browserInfo = mobileCompatibility.getBrowserInfo();
      
      expect(browserInfo.isMobile).toBe(true);
      expect(browserInfo.isIOS).toBe(true);
      expect(browserInfo.isSafari).toBe(true);
    });

    it('should detect Firefox browser', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
        writable: true
      });

      const firefoxCompatibility = new BrowserCompatibility();
      const browserInfo = firefoxCompatibility.getBrowserInfo();
      
      expect(browserInfo.name).toBe('Firefox');
      expect(browserInfo.isFirefox).toBe(true);
    });

    it('should detect Edge browser', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
        writable: true
      });

      const edgeCompatibility = new BrowserCompatibility();
      const browserInfo = edgeCompatibility.getBrowserInfo();
      
      expect(browserInfo.name).toBe('Edge');
      expect(browserInfo.isEdge).toBe(true);
    });
  });

  describe('Feature Detection', () => {
    it('should detect Web Speech API support', () => {
      const features = compatibility.getFeatures();
      expect(features.webSpeechAPI).toBe(true);
    });

    it('should detect Audio API support', () => {
      const features = compatibility.getFeatures();
      expect(features.audioAPI).toBe(true);
    });

    it('should detect Web Audio API support', () => {
      const features = compatibility.getFeatures();
      expect(features.webAudio).toBe(true);
    });

    it('should detect localStorage support', () => {
      const features = compatibility.getFeatures();
      expect(features.localStorage).toBe(true);
    });

    it('should handle missing features gracefully', () => {
      // Mock missing features
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;
      delete (window as any).Audio;

      const limitedCompatibility = new BrowserCompatibility();
      const features = limitedCompatibility.getFeatures();
      
      expect(features.webSpeechAPI).toBe(false);
      expect(features.audioAPI).toBe(false);
    });
  });

  describe('Compatibility Analysis', () => {
    it('should identify issues with missing features', () => {
      // Mock browser without speech support
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;

      const limitedCompatibility = new BrowserCompatibility();
      const issues = limitedCompatibility.getIssues();
      
      const speechIssue = issues.find(issue => issue.feature === 'webSpeechAPI');
      expect(speechIssue).toBeDefined();
      expect(speechIssue?.severity).toBe('warning');
      expect(speechIssue?.message).toContain('Voice input is not supported');
    });

    it('should provide Firefox-specific guidance', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
        writable: true
      });

      const firefoxCompatibility = new BrowserCompatibility();
      const issues = firefoxCompatibility.getIssues();
      
      const firefoxIssue = issues.find(issue => 
        issue.message.includes('Firefox has experimental Web Speech API support')
      );
      expect(firefoxIssue).toBeDefined();
    });

    it('should identify mobile-specific issues', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
        writable: true
      });

      const mobileCompatibility = new BrowserCompatibility();
      const issues = mobileCompatibility.getIssues();
      
      const iosIssue = issues.find(issue => 
        issue.message.includes('iOS requires user interaction to play audio')
      );
      expect(iosIssue).toBeDefined();
    });
  });

  describe('Compatibility Scoring', () => {
    it('should calculate compatibility score', () => {
      const score = compatibility.getCompatibilityScore();
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should return lower score for limited browsers', () => {
      // Mock limited browser
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;
      delete (window as any).AudioContext;
      delete (window as any).webkitAudioContext;

      const limitedCompatibility = new BrowserCompatibility();
      const score = limitedCompatibility.getCompatibilityScore();
      
      expect(score).toBeLessThan(100);
    });

    it('should determine overall compatibility', () => {
      const isCompatible = compatibility.isCompatible();
      expect(typeof isCompatible).toBe('boolean');
    });
  });

  describe('Recommendations', () => {
    it('should provide fallback recommendations', () => {
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;

      const limitedCompatibility = new BrowserCompatibility();
      const fallbacks = limitedCompatibility.getFallbackRecommendations();
      
      expect(fallbacks.length).toBeGreaterThan(0);
      expect(fallbacks.some(fallback => 
        fallback.includes('Text input will be used instead')
      )).toBe(true);
    });

    it('should provide user guidance', () => {
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;

      const limitedCompatibility = new BrowserCompatibility();
      const guidance = limitedCompatibility.getUserGuidance();
      
      expect(guidance.length).toBeGreaterThan(0);
    });

    it('should recommend browser updates when needed', () => {
      // Mock old Safari
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.3 Safari/605.1.15',
        writable: true
      });

      const oldSafariCompatibility = new BrowserCompatibility();
      const message = oldSafariCompatibility.getRecommendedBrowserMessage();
      
      expect(message).toContain('update Safari');
    });
  });

  describe('Mobile Optimizations', () => {
    it('should provide mobile optimizations for mobile browsers', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
        writable: true
      });

      const mobileCompatibility = new BrowserCompatibility();
      const optimizations = mobileCompatibility.getMobileOptimizations();
      
      expect(optimizations.length).toBeGreaterThan(0);
      expect(optimizations.some(opt => 
        opt.includes('Touch interactions are optimized')
      )).toBe(true);
    });

    it('should return empty optimizations for desktop', () => {
      // Create a new compatibility instance with desktop user agent
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        writable: true
      });
      
      const desktopCompatibility = new BrowserCompatibility();
      const optimizations = desktopCompatibility.getMobileOptimizations();
      expect(optimizations).toEqual([]);
    });
  });

  describe('API Creation', () => {
    it('should create speech recognition instance', () => {
      const recognition = compatibility.createSpeechRecognition();
      expect(recognition).toBeDefined();
    });

    it('should return null for unsupported speech recognition', () => {
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;

      const limitedCompatibility = new BrowserCompatibility();
      const recognition = limitedCompatibility.createSpeechRecognition();
      
      expect(recognition).toBeNull();
    });

    it('should create audio context instance', () => {
      const audioContext = compatibility.createAudioContext();
      expect(audioContext).toBeDefined();
    });

    it('should return null for unsupported audio context', () => {
      delete (window as any).AudioContext;
      delete (window as any).webkitAudioContext;

      const limitedCompatibility = new BrowserCompatibility();
      const audioContext = limitedCompatibility.createAudioContext();
      
      expect(audioContext).toBeNull();
    });
  });

  describe('Permission Requests', () => {
    it('should request microphone permissions', async () => {
      mockNavigator.mediaDevices.getUserMedia.mockResolvedValue({
        getTracks: () => [{ stop: vi.fn() }]
      });

      const permissions = await compatibility.requestPermissions();
      
      expect(permissions.microphone).toBe(true);
      expect(mockNavigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({ audio: true });
    });

    it('should handle permission denials gracefully', async () => {
      mockNavigator.mediaDevices.getUserMedia.mockRejectedValue(new Error('Permission denied'));

      const permissions = await compatibility.requestPermissions();
      
      expect(permissions.microphone).toBe(false);
    });

    it('should request notification permissions', async () => {
      // Mock Notification properly
      Object.defineProperty(window, 'Notification', {
        value: {
          requestPermission: vi.fn().mockResolvedValue('granted')
        },
        writable: true
      });

      const permissions = await compatibility.requestPermissions();
      
      expect(permissions.notifications).toBe(true);
    });
  });

  describe('Issue Filtering', () => {
    it('should filter issues by severity', () => {
      delete (window as any).Audio; // Create a critical issue

      const limitedCompatibility = new BrowserCompatibility();
      const criticalIssues = limitedCompatibility.getIssuesBySeverity('critical');
      const warningIssues = limitedCompatibility.getIssuesBySeverity('warning');
      
      expect(criticalIssues.length).toBeGreaterThan(0);
      expect(criticalIssues.every(issue => issue.severity === 'critical')).toBe(true);
      expect(warningIssues.every(issue => issue.severity === 'warning')).toBe(true);
    });

    it('should check specific feature support', () => {
      const speechSupported = compatibility.isFeatureSupported('webSpeechAPI');
      const audioSupported = compatibility.isFeatureSupported('audioAPI');
      
      expect(typeof speechSupported).toBe('boolean');
      expect(typeof audioSupported).toBe('boolean');
    });
  });
});