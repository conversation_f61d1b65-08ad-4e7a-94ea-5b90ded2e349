import { lazy, ComponentType } from 'react';

/**
 * Enhanced lazy loading utility with mobile optimizations
 */

// Preload strategy for mobile
export const preloadComponent = (componentImport: () => Promise<{ default: ComponentType<any> }>) => {
  // Only preload on mobile if user is on WiFi or has good connection
  if (typeof navigator !== 'undefined' && 'connection' in navigator) {
    const connection = (navigator as any).connection;
    if (connection && (connection.effectiveType === '4g' || connection.type === 'wifi')) {
      componentImport();
    }
  }
};

// Lazy load with retry logic for mobile networks
export const lazyWithRetry = (componentImport: () => Promise<{ default: ComponentType<any> }>) => {
  return lazy(() => 
    componentImport().catch(error => {
      console.warn('Component lazy loading failed, retrying...', error);
      // Retry once after a short delay
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(componentImport());
        }, 1000);
      });
    })
  );
};

// Mobile-optimized lazy loading with connection awareness
export const mobileOptimizedLazy = (
  componentImport: () => Promise<{ default: ComponentType<any> }>,
  options: {
    preload?: boolean;
    priority?: 'high' | 'low';
  } = {}
) => {
  const { preload = false, priority = 'low' } = options;

  // Preload if requested and conditions are met
  if (preload) {
    // Delay preloading for low priority components
    const delay = priority === 'low' ? 2000 : 500;
    setTimeout(() => preloadComponent(componentImport), delay);
  }

  return lazyWithRetry(componentImport);
};

// Check if device is likely mobile
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
};

// Check network conditions for mobile optimization
export const getNetworkInfo = () => {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) {
    return { effectiveType: 'unknown', saveData: false };
  }

  const connection = (navigator as any).connection;
  return {
    effectiveType: connection.effectiveType || 'unknown',
    saveData: connection.saveData || false,
    downlink: connection.downlink || 0,
    rtt: connection.rtt || 0
  };
};

// Determine if we should use aggressive optimization
export const shouldUseAggressiveOptimization = (): boolean => {
  const networkInfo = getNetworkInfo();
  const isMobile = isMobileDevice();
  
  return isMobile && (
    networkInfo.saveData ||
    networkInfo.effectiveType === 'slow-2g' ||
    networkInfo.effectiveType === '2g' ||
    networkInfo.effectiveType === '3g'
  );
};