import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NotificationSystem } from '../components/NotificationSystem';
import type { ApiErrorNotification } from '../services/ApiErrorHandler';

// Mock setTimeout and clearTimeout

describe('NotificationSystem', () => {
  let mockOnNotificationReceived: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.useFakeTimers();
    mockOnNotificationReceived = vi.fn();
  });

  afterEach(() => {
    vi.useRealTimers();
    delete (window as any).__addNotification;
  });

  it('should render nothing when no notifications', () => {
    const { container } = render(<NotificationSystem />);
    expect(container.firstChild).toBeNull();
  });

  it('should expose global addNotification method', () => {
    render(<NotificationSystem />);
    expect((window as any).__addNotification).toBeDefined();
    expect(typeof (window as any).__addNotification).toBe('function');
  });

  it('should display notification when added', () => {
    render(<NotificationSystem onNotificationReceived={mockOnNotificationReceived} />);
    
    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'Test Error',
      message: 'This is a test error message'
    };

    // Add notification through global method
    (window as any).__addNotification(notification);

    expect(screen.getByText('Test Error')).toBeInTheDocument();
    expect(screen.getByText('This is a test error message')).toBeInTheDocument();
    expect(mockOnNotificationReceived).toHaveBeenCalledWith(notification);
  });

  it('should display multiple notifications', () => {
    render(<NotificationSystem />);
    
    const notification1: ApiErrorNotification = {
      type: 'error',
      title: 'Error 1',
      message: 'First error'
    };

    const notification2: ApiErrorNotification = {
      type: 'warning',
      title: 'Warning 1',
      message: 'First warning'
    };

    (window as any).__addNotification(notification1);
    (window as any).__addNotification(notification2);

    expect(screen.getByText('Error 1')).toBeInTheDocument();
    expect(screen.getByText('Warning 1')).toBeInTheDocument();
  });

  it('should apply correct CSS classes for different notification types', () => {
    render(<NotificationSystem />);
    
    const errorNotification: ApiErrorNotification = {
      type: 'error',
      title: 'Error',
      message: 'Error message'
    };

    const warningNotification: ApiErrorNotification = {
      type: 'warning',
      title: 'Warning',
      message: 'Warning message'
    };

    const infoNotification: ApiErrorNotification = {
      type: 'info',
      title: 'Info',
      message: 'Info message'
    };

    (window as any).__addNotification(errorNotification);
    (window as any).__addNotification(warningNotification);
    (window as any).__addNotification(infoNotification);

    const notifications = screen.getAllByRole('alert');
    expect(notifications[0]).toHaveClass('error');
    expect(notifications[1]).toHaveClass('warning');
    expect(notifications[2]).toHaveClass('info');
  });

  it('should remove notification when close button is clicked', () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'Test Error',
      message: 'This will be closed'
    };

    (window as any).__addNotification(notification);

    expect(screen.getByText('Test Error')).toBeInTheDocument();

    const closeButton = screen.getByLabelText('Close notification');
    fireEvent.click(closeButton);

    expect(screen.queryByText('Test Error')).not.toBeInTheDocument();
  });

  it('should auto-dismiss notification after specified duration', async () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'info',
      title: 'Auto Dismiss',
      message: 'This will auto-dismiss',
      duration: 3000
    };

    (window as any).__addNotification(notification);

    expect(screen.getByText('Auto Dismiss')).toBeInTheDocument();

    // Fast-forward time
    vi.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.queryByText('Auto Dismiss')).not.toBeInTheDocument();
    });
  });

  it('should not auto-dismiss notification with duration 0', () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'No Auto Dismiss',
      message: 'This will not auto-dismiss',
      duration: 0
    };

    (window as any).__addNotification(notification);

    expect(screen.getByText('No Auto Dismiss')).toBeInTheDocument();

    // Fast-forward time
    vi.advanceTimersByTime(10000);

    expect(screen.getByText('No Auto Dismiss')).toBeInTheDocument();
  });

  it('should not auto-dismiss notification without duration', () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'No Duration',
      message: 'This has no duration'
    };

    (window as any).__addNotification(notification);

    expect(screen.getByText('No Duration')).toBeInTheDocument();

    // Fast-forward time
    vi.advanceTimersByTime(10000);

    expect(screen.getByText('No Duration')).toBeInTheDocument();
  });

  it('should display and handle action button', () => {
    render(<NotificationSystem />);
    
    const mockActionHandler = vi.fn();
    const notification: ApiErrorNotification = {
      type: 'warning',
      title: 'Action Test',
      message: 'This has an action',
      action: {
        label: 'Retry',
        handler: mockActionHandler
      }
    };

    (window as any).__addNotification(notification);

    const actionButton = screen.getByText('Retry');
    expect(actionButton).toBeInTheDocument();

    fireEvent.click(actionButton);

    expect(mockActionHandler).toHaveBeenCalled();
    // Notification should be removed after action
    expect(screen.queryByText('Action Test')).not.toBeInTheDocument();
  });

  it('should show progress bar for notifications with duration', () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'info',
      title: 'Progress Test',
      message: 'This has progress',
      duration: 5000
    };

    (window as any).__addNotification(notification);

    // Check for progress bar elements
    const progressBar = document.querySelector('.progressBar');
    const progress = document.querySelector('.progress');
    
    expect(progressBar).toBeInTheDocument();
    expect(progress).toBeInTheDocument();
    expect(progress).toHaveStyle('animation-duration: 5000ms');
  });

  it('should not show progress bar for notifications without duration', () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'No Progress',
      message: 'This has no progress'
    };

    (window as any).__addNotification(notification);

    const progress = document.querySelector('.progress');
    expect(progress).not.toBeInTheDocument();
  });

  it('should handle multiple notifications with different durations', async () => {
    render(<NotificationSystem />);
    
    const shortNotification: ApiErrorNotification = {
      type: 'info',
      title: 'Short',
      message: 'Short duration',
      duration: 1000
    };

    const longNotification: ApiErrorNotification = {
      type: 'info',
      title: 'Long',
      message: 'Long duration',
      duration: 5000
    };

    (window as any).__addNotification(shortNotification);
    (window as any).__addNotification(longNotification);

    expect(screen.getByText('Short')).toBeInTheDocument();
    expect(screen.getByText('Long')).toBeInTheDocument();

    // Fast-forward 1 second
    vi.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.queryByText('Short')).not.toBeInTheDocument();
    });
    expect(screen.getByText('Long')).toBeInTheDocument();

    // Fast-forward another 4 seconds
    vi.advanceTimersByTime(4000);

    await waitFor(() => {
      expect(screen.queryByText('Long')).not.toBeInTheDocument();
    });
  });

  it('should have proper accessibility attributes', () => {
    render(<NotificationSystem />);
    
    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'Accessibility Test',
      message: 'Testing accessibility'
    };

    (window as any).__addNotification(notification);

    const notificationElements = screen.getAllByRole('alert');
    expect(notificationElements[0]).toHaveAttribute('aria-live', 'polite');

    const closeButton = screen.getByLabelText('Close notification');
    expect(closeButton).toBeInTheDocument();
  });

  it('should clean up global method on unmount', () => {
    const { unmount } = render(<NotificationSystem />);
    
    expect((window as any).__addNotification).toBeDefined();
    
    unmount();
    
    expect((window as any).__addNotification).toBeUndefined();
  });
});