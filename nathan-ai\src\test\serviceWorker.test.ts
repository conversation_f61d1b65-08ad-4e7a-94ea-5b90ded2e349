import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  registerServiceWorker,
  unregisterServiceWorker,
  updateServiceWorker,
  getServiceWorkerStatus,
  skipWaiting,
  clearCache,
  precacheResources
} from '../utils/serviceWorker';

// Mock service worker registration
const mockRegistration = {
  installing: null,
  waiting: null,
  active: null,
  scope: 'http://localhost:3000/',
  update: vi.fn(),
  unregister: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
};

const mockServiceWorker = {
  scriptURL: 'http://localhost:3000/sw.js',
  state: 'activated',
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  postMessage: vi.fn(),
};

// Mock navigator.serviceWorker
Object.defineProperty(navigator, 'serviceWorker', {
  writable: true,
  value: {
    register: vi.fn(),
    getRegistration: vi.fn(),
    getRegistrations: vi.fn(),
    ready: Promise.resolve(mockRegistration),
    controller: mockServiceWorker,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  },
});

describe('Service Worker Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset mock implementations
    vi.mocked(navigator.serviceWorker.register).mockResolvedValue(mockRegistration as any);
    vi.mocked(navigator.serviceWorker.getRegistration).mockResolvedValue(mockRegistration as any);
    vi.mocked(mockRegistration.unregister).mockResolvedValue(true);
    vi.mocked(mockRegistration.update).mockResolvedValue();
    
    // Reset console mocks
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('registerServiceWorker', () => {
    it('registers service worker successfully', async () => {
      const result = await registerServiceWorker('/sw.js');

      expect(navigator.serviceWorker.register).toHaveBeenCalledWith('/sw.js', {
        scope: '/',
      });
      expect(result.success).toBe(true);
      expect(result.registration).toBe(mockRegistration);
    });

    it('registers service worker with custom scope', async () => {
      await registerServiceWorker('/sw.js', { scope: '/app/' });

      expect(navigator.serviceWorker.register).toHaveBeenCalledWith('/sw.js', {
        scope: '/app/',
      });
    });

    it('handles registration failure', async () => {
      const error = new Error('Registration failed');
      vi.mocked(navigator.serviceWorker.register).mockRejectedValue(error);

      const result = await registerServiceWorker('/sw.js');

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(console.error).toHaveBeenCalledWith('Service worker registration failed:', error);
    });

    it('handles unsupported browsers', async () => {
      Object.defineProperty(navigator, 'serviceWorker', {
        writable: true,
        value: undefined,
      });

      const result = await registerServiceWorker('/sw.js');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Service workers are not supported');
    });

    it('sets up update listeners', async () => {
      await registerServiceWorker('/sw.js');

      expect(mockRegistration.addEventListener).toHaveBeenCalledWith(
        'updatefound',
        expect.any(Function)
      );
    });

    it('handles update found event', async () => {
      let updateFoundCallback: Function;
      vi.mocked(mockRegistration.addEventListener).mockImplementation((event, callback) => {
        if (event === 'updatefound') {
          updateFoundCallback = callback as Function;
        }
      });

      await registerServiceWorker('/sw.js');

      // Simulate update found
      mockRegistration.installing = mockServiceWorker as any;
      updateFoundCallback();

      expect(mockServiceWorker.addEventListener).toHaveBeenCalledWith(
        'statechange',
        expect.any(Function)
      );
    });
  });

  describe('unregisterServiceWorker', () => {
    it('unregisters service worker successfully', async () => {
      const result = await unregisterServiceWorker();

      expect(navigator.serviceWorker.getRegistration).toHaveBeenCalled();
      expect(mockRegistration.unregister).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('handles no registration found', async () => {
      vi.mocked(navigator.serviceWorker.getRegistration).mockResolvedValue(undefined);

      const result = await unregisterServiceWorker();

      expect(result.success).toBe(true);
      expect(console.log).toHaveBeenCalledWith('No service worker registration found');
    });

    it('handles unregistration failure', async () => {
      const error = new Error('Unregistration failed');
      vi.mocked(mockRegistration.unregister).mockRejectedValue(error);

      const result = await unregisterServiceWorker();

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(console.error).toHaveBeenCalledWith('Service worker unregistration failed:', error);
    });

    it('handles unsupported browsers', async () => {
      Object.defineProperty(navigator, 'serviceWorker', {
        writable: true,
        value: undefined,
      });

      const result = await unregisterServiceWorker();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Service workers are not supported');
    });
  });

  describe('updateServiceWorker', () => {
    it('updates service worker successfully', async () => {
      const result = await updateServiceWorker();

      expect(navigator.serviceWorker.getRegistration).toHaveBeenCalled();
      expect(mockRegistration.update).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('handles no registration found', async () => {
      vi.mocked(navigator.serviceWorker.getRegistration).mockResolvedValue(undefined);

      const result = await updateServiceWorker();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('No service worker registration found');
    });

    it('handles update failure', async () => {
      const error = new Error('Update failed');
      vi.mocked(mockRegistration.update).mockRejectedValue(error);

      const result = await updateServiceWorker();

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(console.error).toHaveBeenCalledWith('Service worker update failed:', error);
    });
  });

  describe('getServiceWorkerStatus', () => {
    it('returns service worker status', async () => {
      const status = await getServiceWorkerStatus();

      expect(status.isSupported).toBe(true);
      expect(status.isRegistered).toBe(true);
      expect(status.isControlling).toBe(true);
      expect(status.registration).toBe(mockRegistration);
      expect(status.controller).toBe(mockServiceWorker);
    });

    it('handles unsupported browsers', async () => {
      Object.defineProperty(navigator, 'serviceWorker', {
        writable: true,
        value: undefined,
      });

      const status = await getServiceWorkerStatus();

      expect(status.isSupported).toBe(false);
      expect(status.isRegistered).toBe(false);
      expect(status.isControlling).toBe(false);
    });

    it('handles no registration', async () => {
      vi.mocked(navigator.serviceWorker.getRegistration).mockResolvedValue(undefined);

      const status = await getServiceWorkerStatus();

      expect(status.isSupported).toBe(true);
      expect(status.isRegistered).toBe(false);
      expect(status.isControlling).toBe(false);
    });

    it('handles no controller', async () => {
      Object.defineProperty(navigator.serviceWorker, 'controller', {
        writable: true,
        value: null,
      });

      const status = await getServiceWorkerStatus();

      expect(status.isSupported).toBe(true);
      expect(status.isRegistered).toBe(true);
      expect(status.isControlling).toBe(false);
    });
  });

  describe('skipWaiting', () => {
    it('sends skip waiting message to service worker', async () => {
      const result = await skipWaiting();

      expect(mockServiceWorker.postMessage).toHaveBeenCalledWith({
        type: 'SKIP_WAITING',
      });
      expect(result.success).toBe(true);
    });

    it('handles no controller', async () => {
      Object.defineProperty(navigator.serviceWorker, 'controller', {
        writable: true,
        value: null,
      });

      const result = await skipWaiting();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('No service worker controller found');
    });

    it('handles unsupported browsers', async () => {
      Object.defineProperty(navigator, 'serviceWorker', {
        writable: true,
        value: undefined,
      });

      const result = await skipWaiting();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Service workers are not supported');
    });
  });

  describe('clearCache', () => {
    it('sends clear cache message to service worker', async () => {
      const result = await clearCache();

      expect(mockServiceWorker.postMessage).toHaveBeenCalledWith({
        type: 'CLEAR_CACHE',
      });
      expect(result.success).toBe(true);
    });

    it('clears specific cache when name provided', async () => {
      await clearCache('api-cache');

      expect(mockServiceWorker.postMessage).toHaveBeenCalledWith({
        type: 'CLEAR_CACHE',
        cacheName: 'api-cache',
      });
    });

    it('handles no controller', async () => {
      Object.defineProperty(navigator.serviceWorker, 'controller', {
        writable: true,
        value: null,
      });

      const result = await clearCache();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('No service worker controller found');
    });
  });

  describe('precacheResources', () => {
    it('sends precache message with resources', async () => {
      const resources = ['/app.js', '/app.css', '/manifest.json'];
      const result = await precacheResources(resources);

      expect(mockServiceWorker.postMessage).toHaveBeenCalledWith({
        type: 'PRECACHE_RESOURCES',
        resources,
      });
      expect(result.success).toBe(true);
    });

    it('handles empty resources array', async () => {
      const result = await precacheResources([]);

      expect(result.success).toBe(true);
      expect(mockServiceWorker.postMessage).toHaveBeenCalledWith({
        type: 'PRECACHE_RESOURCES',
        resources: [],
      });
    });

    it('handles no controller', async () => {
      Object.defineProperty(navigator.serviceWorker, 'controller', {
        writable: true,
        value: null,
      });

      const result = await precacheResources(['/app.js']);

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('No service worker controller found');
    });
  });

  describe('error handling', () => {
    it('handles service worker registration errors gracefully', async () => {
      const networkError = new Error('Network error');
      vi.mocked(navigator.serviceWorker.register).mockRejectedValue(networkError);

      const result = await registerServiceWorker('/sw.js');

      expect(result.success).toBe(false);
      expect(result.error).toBe(networkError);
      expect(console.error).toHaveBeenCalled();
    });

    it('handles service worker update errors gracefully', async () => {
      const updateError = new Error('Update error');
      vi.mocked(mockRegistration.update).mockRejectedValue(updateError);

      const result = await updateServiceWorker();

      expect(result.success).toBe(false);
      expect(result.error).toBe(updateError);
      expect(console.error).toHaveBeenCalled();
    });

    it('handles message posting errors gracefully', async () => {
      const postMessageError = new Error('PostMessage error');
      vi.mocked(mockServiceWorker.postMessage).mockImplementation(() => {
        throw postMessageError;
      });

      const result = await skipWaiting();

      expect(result.success).toBe(false);
      expect(result.error).toBe(postMessageError);
    });
  });
});