.messageContainer {
  display: flex;
  align-items: flex-end;
  margin-bottom: 16px;
  gap: 8px;
  max-width: 100%;
}

.userMessage {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.nathanMessage {
  flex-direction: row;
  justify-content: flex-start;
}

.avatarContainer {
  flex-shrink: 0;
  display: flex;
  align-items: flex-end;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  transition: all 0.3s ease;
}

.avatar.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar.neutral {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.avatar.happy {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  animation: happyPulse 2s ease-in-out infinite;
}

.avatar.thoughtful {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  animation: thoughtfulGlow 3s ease-in-out infinite;
}

.avatar.empathetic {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  animation: empatheticWarm 2.5s ease-in-out infinite;
}

@keyframes happyPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes thoughtfulGlow {
  0%, 100% { box-shadow: 0 0 0 rgba(250, 112, 154, 0.4); }
  50% { box-shadow: 0 0 20px rgba(250, 112, 154, 0.6); }
}

@keyframes empatheticWarm {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.1); }
}

.messageContent {
  flex: 1;
  max-width: 70%;
  min-width: 0;
}

.messageBubble {
  position: relative;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.userMessage .messageBubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.nathanMessage .messageBubble {
  background: #f1f3f4;
  color: #333;
  border-bottom-left-radius: 4px;
  border: 1px solid #e0e0e0;
}

.messageText {
  flex: 1;
  line-height: 1.4;
  font-size: 14px;
}

.emotionIndicator {
  font-size: 16px;
  flex-shrink: 0;
  opacity: 0.8;
}

.messageMetadata {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
  font-size: 11px;
  color: #666;
}

.userMessage .messageMetadata {
  justify-content: flex-end;
}

.nathanMessage .messageMetadata {
  justify-content: flex-start;
}

.timestamp {
  opacity: 0.7;
}

.audioIndicator {
  opacity: 0.6;
  cursor: help;
  font-size: 12px;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .messageContainer {
    margin-bottom: 12px;
    gap: 6px;
  }
  
  .messageContent {
    max-width: 85%;
    min-width: 0; /* Prevent overflow */
  }
  
  .messageBubble {
    padding: 10px 14px;
    border-radius: 16px;
    /* Improve text wrapping */
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }
  
  .messageText {
    font-size: 14px; /* Slightly larger for mobile readability */
    line-height: 1.5;
  }
  
  .avatar {
    width: 32px; /* Keep standard touch target size */
    height: 32px;
    font-size: 12px;
    flex-shrink: 0;
  }
  
  .emotionIndicator {
    font-size: 14px;
  }
  
  .messageMetadata {
    font-size: 10px;
    margin-top: 2px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .nathanMessage .messageBubble {
    background: #2d2d2d;
    color: #e0e0e0;
    border-color: #404040;
  }
  
  .messageMetadata {
    color: #999;
  }
  
  .messageBubble {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}@media 
(max-width: 480px) {
  .messageContainer {
    gap: 4px;
    margin-bottom: 10px;
  }
  
  .messageContent {
    max-width: 90%; /* More space on small screens */
  }
  
  .messageBubble {
    padding: 8px 12px;
    border-radius: 14px;
    max-width: calc(100vw - 80px); /* Prevent overflow */
  }
  
  .messageText {
    font-size: 13px;
  }
  
  .avatar {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
  
  .emotionIndicator {
    font-size: 12px;
  }
  
  .messageMetadata {
    font-size: 9px;
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 500px) {
  .messageContainer {
    margin-bottom: 8px;
    gap: 4px;
  }
  
  .messageBubble {
    padding: 6px 10px;
    border-radius: 12px;
  }
  
  .messageText {
    font-size: 12px;
    line-height: 1.4;
  }
  
  .avatar {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
  
  .emotionIndicator {
    font-size: 11px;
  }
  
  .messageMetadata {
    font-size: 8px;
    margin-top: 1px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .messageBubble {
    /* Improve touch interaction */
    -webkit-tap-highlight-color: transparent;
    user-select: text;
    -webkit-user-select: text;
  }
  
  /* Remove hover animations on touch devices */
  .avatar.happy,
  .avatar.thoughtful,
  .avatar.empathetic {
    animation-play-state: paused;
  }
  
  /* Add touch feedback for interactive elements */
  .audioIndicator {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .messageBubble {
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.1);
  }
  
  .nathanMessage .messageBubble {
    border-width: 0.5px;
  }
}

/* Accessibility Improvements */
.messageBubble:focus {
  outline: 2px solid var(--primary-color, #007bff);
  outline-offset: 2px;
}

/* Long message handling */
.messageText {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

/* Prevent text selection issues on mobile */
@media (max-width: 768px) {
  .messageText {
    -webkit-user-select: text;
    user-select: text;
  }
  
  .messageMetadata {
    -webkit-user-select: none;
    user-select: none;
  }
}

/* Animation performance optimization for mobile */
@media (max-width: 768px) and (prefers-reduced-motion: no-preference) {
  .avatar.happy {
    animation: happyPulse 3s ease-in-out infinite;
  }
  
  .avatar.thoughtful {
    animation: thoughtfulGlow 4s ease-in-out infinite;
  }
  
  .avatar.empathetic {
    animation: empatheticWarm 3.5s ease-in-out infinite;
  }
}

@media (prefers-reduced-motion: reduce) {
  .avatar.happy,
  .avatar.thoughtful,
  .avatar.empathetic {
    animation: none;
  }
}