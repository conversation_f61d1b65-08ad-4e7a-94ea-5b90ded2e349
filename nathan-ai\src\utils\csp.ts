// Content Security Policy utilities
import { config } from './env';

export interface CSPDirectives {
  'default-src': string[];
  'script-src': string[];
  'style-src': string[];
  'connect-src': string[];
  'img-src': string[];
  'font-src': string[];
  'media-src': string[];
  'worker-src': string[];
  'frame-src': string[];
  'object-src': string[];
  'base-uri': string[];
  'form-action': string[];
}

// Generate CSP directives based on environment
export const generateCSPDirectives = (): CSPDirectives => {
  const isDevelopment = config.isDevelopment;
  
  return {
    'default-src': ["'self'"],
    'script-src': isDevelopment 
      ? ["'self'", "'unsafe-inline'", "'unsafe-eval'"] 
      : ["'self'"],
    'style-src': ["'self'", "'unsafe-inline'"], // Needed for CSS modules
    'connect-src': [
      "'self'",
      'https://api-inference.huggingface.co',
      'https://api.elevenlabs.io',
      ...(isDevelopment ? ['ws:', 'wss:'] : [])
    ],
    'img-src': ["'self'", 'data:', 'blob:'],
    'font-src': ["'self'", 'data:'],
    'media-src': ["'self'", 'blob:'],
    'worker-src': ["'self'", 'blob:'],
    'frame-src': ["'none'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
  };
};

// Convert CSP directives to header string
export const generateCSPHeader = (): string => {
  const directives = generateCSPDirectives();
  
  return Object.entries(directives)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
};

// Validate CSP compliance for dynamic content
export const validateCSPCompliance = (content: string): {
  isCompliant: boolean;
  violations: string[];
} => {
  const violations: string[] = [];

  // Check for inline scripts
  if (/<script[^>]*>/.test(content)) {
    violations.push('Inline scripts are not allowed by CSP');
  }

  // Check for javascript: URLs
  if (/javascript:/i.test(content)) {
    violations.push('javascript: URLs are not allowed by CSP');
  }

  // Check for data: URLs in scripts
  if (/src\s*=\s*["']data:/i.test(content)) {
    violations.push('data: URLs in script sources are not allowed by CSP');
  }

  // Check for eval-like functions
  const evalPatterns = [
    /\beval\s*\(/,
    /\bFunction\s*\(/,
    /\bsetTimeout\s*\(\s*["']/,
    /\bsetInterval\s*\(\s*["']/,
  ];

  for (const pattern of evalPatterns) {
    if (pattern.test(content)) {
      violations.push('eval-like functions are not allowed by CSP');
      break;
    }
  }

  // Check for inline event handlers
  const eventHandlerPattern = /\bon\w+\s*=/i;
  if (eventHandlerPattern.test(content)) {
    violations.push('Inline event handlers are not allowed by CSP');
  }

  return {
    isCompliant: violations.length === 0,
    violations,
  };
};

// Security headers configuration
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    'Content-Security-Policy': generateCSPHeader(),
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': [
      'camera=()',
      'microphone=(self)',
      'geolocation=()',
      'payment=()',
      'usb=()',
    ].join(', '),
    ...(config.isProduction && {
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    }),
  };
};

// Apply security headers to fetch requests
export const applySecurityHeaders = (headers: HeadersInit = {}): HeadersInit => {
  const securityHeaders = getSecurityHeaders();
  
  return {
    ...headers,
    ...securityHeaders,
  };
};

// Validate external URLs for CSP compliance
export const validateExternalURL = (url: string): {
  isAllowed: boolean;
  reason?: string;
} => {
  try {
    const urlObj = new URL(url);
    const directives = generateCSPDirectives();
    
    // Check against connect-src for API calls
    const allowedHosts = directives['connect-src']
      .filter(src => src.startsWith('https://'))
      .map(src => new URL(src).hostname);
    
    if (allowedHosts.includes(urlObj.hostname)) {
      return { isAllowed: true };
    }
    
    // Check for localhost in development
    if (config.isDevelopment && urlObj.hostname === 'localhost') {
      return { isAllowed: true };
    }
    
    return {
      isAllowed: false,
      reason: `Host ${urlObj.hostname} is not allowed by CSP connect-src directive`,
    };
    
  } catch (error) {
    return {
      isAllowed: false,
      reason: 'Invalid URL format',
    };
  }
};

// CSP violation reporting (for development)
export const handleCSPViolation = (violationEvent: SecurityPolicyViolationEvent): void => {
  if (config.isDevelopment) {
    console.warn('CSP Violation:', {
      blockedURI: violationEvent.blockedURI,
      violatedDirective: violationEvent.violatedDirective,
      originalPolicy: violationEvent.originalPolicy,
      sourceFile: violationEvent.sourceFile,
      lineNumber: violationEvent.lineNumber,
    });
  }
  
  // In production, you might want to send this to a logging service
  if (config.isProduction) {
    // Example: Send to logging service
    // logCSPViolation(violationEvent);
  }
};

// Initialize CSP violation reporting
export const initializeCSPReporting = (): void => {
  if (typeof window !== 'undefined') {
    window.addEventListener('securitypolicyviolation', handleCSPViolation);
  }
};