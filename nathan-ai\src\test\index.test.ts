import { describe, it, expect } from 'vitest';

// Test all main exports to ensure they're properly exposed
describe('Module Exports', () => {
  describe('Types exports', () => {
    it('exports all type definitions', async () => {
      const types = await import('../types');
      
      // Check that main type categories are exported
      expect(types).toBeDefined();
      
      // Test individual type imports
      const messageTypes = await import('../types/message');
      const personalityTypes = await import('../types/personality');
      const conversationTypes = await import('../types/conversation');
      const apiTypes = await import('../types/api');
      const componentTypes = await import('../types/components');
      const utilTypes = await import('../types/utils');
      
      expect(messageTypes).toBeDefined();
      expect(personalityTypes).toBeDefined();
      expect(conversationTypes).toBeDefined();
      expect(apiTypes).toBeDefined();
      expect(componentTypes).toBeDefined();
      expect(utilTypes).toBeDefined();
    });
  });

  describe('Components exports', () => {
    it('exports all components', async () => {
      const components = await import('../components');
      
      // Core components
      expect(components.MessageBubble).toBeDefined();
      expect(components.Avatar).toBeDefined();
      expect(components.ChatInterface).toBeDefined();
      expect(components.TextInput).toBeDefined();
      expect(components.VoiceInput).toBeDefined();
      expect(components.InputController).toBeDefined();
      expect(components.AudioPlayer).toBeDefined();
      expect(components.AudioPlayerDefault).toBeDefined();
      
      // Error boundary components
      expect(components.ErrorBoundary).toBeDefined();
      expect(components.ApiErrorBoundary).toBeDefined();
      expect(components.FeatureErrorBoundary).toBeDefined();
      expect(components.AsyncErrorBoundary).toBeDefined();
      expect(components.ErrorRecovery).toBeDefined();
      expect(components.NotificationSystem).toBeDefined();
      expect(components.CompatibilityWarning).toBeDefined();
    });
  });

  describe('Hooks exports', () => {
    it('exports all custom hooks', async () => {
      const hooks = await import('../hooks');
      
      expect(hooks.useConversation).toBeDefined();
      expect(hooks.usePersonality).toBeDefined();
    });
  });

  describe('Utils exports', () => {
    it('exports all utility functions', async () => {
      const utils = await import('../utils');
      
      // Environment utilities
      expect(utils.getEnvVar).toBeDefined();
      expect(utils.validateEnvironment).toBeDefined();
      expect(utils.config).toBeDefined();
      
      // Personality utilities
      expect(utils.loadPersonality).toBeDefined();
      expect(utils.savePersonality).toBeDefined();
      expect(utils.validatePersonalityConfig).toBeDefined();
      expect(utils.mergePersonalityConfigs).toBeDefined();
      
      // State persistence utilities
      expect(utils.savePersonalityPreferences).toBeDefined();
      expect(utils.loadPersonalityPreferences).toBeDefined();
      expect(utils.clearPersonalityPreferences).toBeDefined();
      expect(utils.saveUIPreferences).toBeDefined();
      expect(utils.loadUIPreferences).toBeDefined();
      expect(utils.clearUIPreferences).toBeDefined();
      expect(utils.saveConversationPreferences).toBeDefined();
      expect(utils.loadConversationPreferences).toBeDefined();
      expect(utils.clearConversationPreferences).toBeDefined();
      expect(utils.clearAllPreferences).toBeDefined();
      expect(utils.isStorageAvailable).toBeDefined();
      expect(utils.getStorageInfo).toBeDefined();
    });
  });

  describe('Context exports', () => {
    it('exports all context providers', async () => {
      const conversationContext = await import('../context/ConversationContext');
      const personalityContext = await import('../context/PersonalityContext');
      
      expect(conversationContext.ConversationProvider).toBeDefined();
      expect(conversationContext.useConversationContext).toBeDefined();
      
      expect(personalityContext.PersonalityProvider).toBeDefined();
      expect(personalityContext.usePersonalityContext).toBeDefined();
    });
  });

  describe('Services exports', () => {
    it('exports all service classes', async () => {
      const aiService = await import('../services/AIService');
      const hfClient = await import('../services/HuggingFaceClient');
      const elClient = await import('../services/ElevenLabsClient');
      const apiErrorHandler = await import('../services/ApiErrorHandler');
      const browserCompatibility = await import('../services/BrowserCompatibility');
      
      expect(aiService.AIService).toBeDefined();
      expect(hfClient.HuggingFaceClient).toBeDefined();
      expect(elClient.ElevenLabsClient).toBeDefined();
      expect(apiErrorHandler.ApiErrorHandler).toBeDefined();
      expect(browserCompatibility.BrowserCompatibility).toBeDefined();
    });
  });

  describe('App export', () => {
    it('exports main App component', async () => {
      const app = await import('../App');
      
      expect(app.default).toBeDefined();
      expect(typeof app.default).toBe('function');
    });
  });
});

describe('Module Structure Validation', () => {
  it('has consistent export patterns', async () => {
    // Test that all modules follow consistent export patterns
    const modules = [
      '../types',
      '../components',
      '../hooks',
      '../utils',
      '../services',
    ];

    for (const modulePath of modules) {
      const module = await import(modulePath);
      expect(module).toBeDefined();
      expect(typeof module).toBe('object');
    }
  });

  it('has no circular dependencies', async () => {
    // This test ensures that importing all modules doesn't cause circular dependency issues
    const imports = await Promise.all([
      import('../types'),
      import('../components'),
      import('../hooks'),
      import('../utils'),
      import('../services'),
      import('../context/ConversationContext'),
      import('../context/PersonalityContext'),
      import('../App'),
    ]);

    imports.forEach(module => {
      expect(module).toBeDefined();
    });
  });

  it('has proper TypeScript definitions', async () => {
    // Test that TypeScript definitions are properly exported
    const typeModules = [
      '../types/message',
      '../types/personality',
      '../types/conversation',
      '../types/api',
      '../types/components',
      '../types/utils',
    ];

    for (const modulePath of typeModules) {
      const module = await import(modulePath);
      expect(module).toBeDefined();
    }
  });
});