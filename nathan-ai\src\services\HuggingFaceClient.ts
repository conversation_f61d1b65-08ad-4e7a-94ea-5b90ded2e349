import { config } from '../utils/env';
import { 
  checkApiRateLimit, 
  sanitizeInput, 
  validateInput, 
  secureLog, 
  sanitizeError 
} from '../utils/security';
import { 
  GenerationOptions, 
  HuggingFaceRequest, 
  HuggingFaceResponse, 
  HFError 
} from '../types/api';
import { PersonalityConfig } from '../types/personality';
import { cacheManager } from '../utils/cache';
import { ApiPerformanceTracker } from '../utils/performance';

export class HuggingFaceClient {
  private readonly apiKey: string;
  private readonly baseUrl: string = 'https://api-inference.huggingface.co/models';
  private readonly model: string;
  private readonly maxRetries: number = 3;
  private readonly retryDelay: number = 1000; // 1 second

  constructor() {
    this.apiKey = config.huggingFace.apiKey;
    this.model = config.huggingFace.model;
    
    if (!this.apiKey) {
      throw new Error('Hugging Face API key is required');
    }
  }

  /**
   * Generate text response using Hugging Face API
   */
  async generateText(
    prompt: string, 
    options: GenerationOptions = {}
  ): Promise<string> {
    // Check cache first
    const cacheKey = cacheManager.apiCache.generateKey('huggingface/generate', { prompt, options });
    const cachedResponse = cacheManager.apiCache.get('huggingface/generate', { prompt, options });
    
    if (cachedResponse) {
      secureLog('HuggingFace cache hit', { promptLength: prompt.length });
      return cachedResponse;
    }

    // Rate limiting check
    if (!checkApiRateLimit('huggingface')) {
      throw new HFError('Rate limit exceeded. Please wait before making another request.', 429);
    }

    // Input validation and sanitization
    const validation = validateInput(prompt);
    if (!validation.isValid) {
      throw new HFError(`Invalid input: ${validation.errors.join(', ')}`, 400);
    }

    const sanitizedPrompt = sanitizeInput(prompt);
    if (!sanitizedPrompt) {
      throw new HFError('Input cannot be empty after sanitization', 400);
    }

    secureLog('HuggingFace API request initiated', { 
      promptLength: sanitizedPrompt.length,
      options: { ...options, apiKey: undefined }
    });

    const requestBody: HuggingFaceRequest = {
      inputs: sanitizedPrompt,
      parameters: {
        max_length: Math.min(options.max_length || 150, 500), // Cap max length for security
        temperature: Math.max(0.1, Math.min(options.temperature || 0.7, 1.0)), // Clamp temperature
        top_p: Math.max(0.1, Math.min(options.top_p || 0.9, 1.0)), // Clamp top_p
        do_sample: options.do_sample !== false,
        ...options
      }
    };

    // Start performance tracking
    const endTracking = ApiPerformanceTracker.startTracking('huggingface');

    try {
      const result = await this.makeRequestWithRetry(requestBody);
      
      // Cache the successful response
      cacheManager.apiCache.set('huggingface/generate', { prompt, options }, result);
      
      return result;
    } finally {
      endTracking();
    }
  }

  /**
   * Generate response with personality integration
   */
  async generatePersonalityResponse(
    userMessage: string,
    personality: PersonalityConfig,
    conversationHistory: string[] = [],
    options: GenerationOptions = {}
  ): Promise<string> {
    // Validate and sanitize user message
    const validation = validateInput(userMessage);
    if (!validation.isValid) {
      throw new HFError(`Invalid user message: ${validation.errors.join(', ')}`, 400);
    }

    const sanitizedMessage = sanitizeInput(userMessage);
    if (!sanitizedMessage) {
      throw new HFError('User message cannot be empty after sanitization', 400);
    }

    // Sanitize conversation history
    const sanitizedHistory = conversationHistory
      .map(msg => sanitizeInput(msg))
      .filter(msg => msg.length > 0)
      .slice(-10); // Limit history to last 10 messages for security

    const personalityPrompt = this.formatPersonalityPrompt(
      sanitizedMessage, 
      personality, 
      sanitizedHistory
    );
    
    return this.generateText(personalityPrompt, options);
  }

  /**
   * Format prompt with personality context
   */
  private formatPersonalityPrompt(
    userMessage: string,
    personality: PersonalityConfig,
    conversationHistory: string[] = []
  ): string {
    const { name, pronouns, personality: config } = personality;
    
    // Build personality context
    const personalityContext = [
      `You are ${name}, using ${pronouns} pronouns.`,
      `Your role: ${config.role}`,
      `Your tone: ${config.tone}`,
      `Speech style: ${config.style.speech}`,
      `Humor style: ${config.style.humor}`,
      `Conversation depth: ${config.style.depth}`,
    ];

    if (config.hobbies.length > 0) {
      personalityContext.push(`Your hobbies: ${config.hobbies.join(', ')}`);
    }

    if (config.boundaries.safe_topics.length > 0) {
      personalityContext.push(`Preferred topics: ${config.boundaries.safe_topics.join(', ')}`);
    }

    if (config.boundaries.avoid.length > 0) {
      personalityContext.push(`Avoid discussing: ${config.boundaries.avoid.join(', ')}`);
    }

    // Add dynamic traits
    const traits = [];
    if (config.dynamic_traits.adaptive_empathy) {
      traits.push('Be empathetic and adapt to the user\'s emotional state');
    }
    if (config.dynamic_traits.mirroring_style) {
      traits.push('Mirror the user\'s communication style appropriately');
    }
    if (config.dynamic_traits.emotionally_available) {
      traits.push('Be emotionally available and supportive');
    }

    if (traits.length > 0) {
      personalityContext.push(`Behavioral traits: ${traits.join(', ')}`);
    }

    // Build conversation context
    let conversationContext = '';
    if (conversationHistory.length > 0) {
      const recentHistory = conversationHistory.slice(-6); // Last 6 messages
      conversationContext = `\n\nRecent conversation:\n${recentHistory.join('\n')}`;
    }

    // Construct final prompt
    const prompt = [
      personalityContext.join('\n'),
      conversationContext,
      `\nUser: ${userMessage}`,
      `${name}:`
    ].join('\n');

    return prompt;
  }

  /**
   * Make API request with retry logic
   */
  private async makeRequestWithRetry(
    requestBody: HuggingFaceRequest,
    attempt: number = 1
  ): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/${this.model}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createHFError(response.status, errorData);
      }

      const data: HuggingFaceResponse[] = await response.json();
      
      if (!data || !Array.isArray(data) || data.length === 0) {
        throw this.createHFError(500, { error: 'Invalid response format' });
      }

      const generatedText = data[0].generated_text;
      
      // Clean up the response by removing the input prompt
      const cleanedText = this.cleanGeneratedText(generatedText, requestBody.inputs);
      
      return cleanedText;

    } catch (error) {
      if (attempt < this.maxRetries && this.isRetryableError(error)) {
        await this.delay(this.retryDelay * attempt);
        return this.makeRequestWithRetry(requestBody, attempt + 1);
      }
      
      throw this.handleApiError(error);
    }
  }

  /**
   * Clean generated text by removing input prompt and formatting
   */
  private cleanGeneratedText(generatedText: string, inputPrompt: string): string {
    // Remove the input prompt from the beginning
    let cleaned = generatedText.replace(inputPrompt, '').trim();
    
    // Remove any leading colons or whitespace
    cleaned = cleaned.replace(/^[:]\s*/, '').trim();
    
    // Remove "Nathan:" prefix if present
    cleaned = cleaned.replace(/^Nathan:\s*/, '').trim();
    
    // Ensure the response doesn't include "User:" or similar patterns
    cleaned = cleaned.split('\nUser:')[0].trim();
    cleaned = cleaned.split('\nHuman:')[0].trim();
    
    return cleaned || 'I apologize, but I couldn\'t generate a proper response. Could you try rephrasing your message?';
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (error instanceof HFError) {
      // Retry on server errors and rate limiting
      return error.status === 503 || error.status === 429 || error.status === 500;
    }
    
    // Retry on network errors
    return error.name === 'TypeError' || error.code === 'NETWORK_ERROR';
  }

  /**
   * Create standardized HF error
   */
  private createHFError(status: number, errorData: any): HFError {
    return new HFError(errorData.error || `HTTP ${status} error`, status);
  }

  /**
   * Handle API errors with proper error types
   */
  private handleApiError(error: any): never {
    if (error instanceof HFError) {
      secureLog('HuggingFace API error', { 
        status: error.status, 
        message: sanitizeError(error) 
      });
      throw error;
    }

    // Handle network errors
    if (error.name === 'TypeError' || !navigator.onLine) {
      const networkError = new HFError('Network connection failed', 0);
      secureLog('HuggingFace network error', { error: sanitizeError(networkError) });
      throw networkError;
    }

    // Handle unknown errors
    const unknownError = new HFError(sanitizeError(error), 500);
    secureLog('HuggingFace unknown error', { error: sanitizeError(unknownError) });
    throw unknownError;
  }

  /**
   * Utility delay function for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}