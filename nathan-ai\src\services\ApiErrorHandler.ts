import { HF<PERSON><PERSON>r, <PERSON><PERSON><PERSON>r, NetworkError, ErrorType } from '../types/api';

export interface ApiErrorNotification {
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  action?: {
    label: string;
    handler: () => void;
  };
  duration?: number; // Auto-dismiss after ms, 0 = manual dismiss
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableStatuses: number[];
}

export interface OfflineQueueItem {
  id: string;
  timestamp: number;
  type: 'text' | 'audio';
  data: any;
  retryCount: number;
}

export class ApiErrorHandler {
  private notificationCallbacks: ((notification: ApiErrorNotification) => void)[] = [];
  private offlineQueue: OfflineQueueItem[] = [];
  private isOnline: boolean = navigator.onLine;
  private retryConfig: RetryConfig;

  constructor(retryConfig?: Partial<RetryConfig>) {
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      retryableStatuses: [429, 500, 502, 503, 504],
      ...retryConfig
    };

    this.setupOfflineDetection();
  }

  /**
   * Setup online/offline detection
   */
  private setupOfflineDetection(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.showNotification({
        type: 'info',
        title: 'Connection Restored',
        message: 'Back online! Processing queued requests...',
        duration: 3000
      });
      this.processOfflineQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showNotification({
        type: 'warning',
        title: 'Connection Lost',
        message: 'You\'re offline. Messages will be queued until connection is restored.',
        duration: 5000
      });
    });
  }

  /**
   * Register notification callback
   */
  onNotification(callback: (notification: ApiErrorNotification) => void): void {
    this.notificationCallbacks.push(callback);
  }

  /**
   * Remove notification callback
   */
  offNotification(callback: (notification: ApiErrorNotification) => void): void {
    const index = this.notificationCallbacks.indexOf(callback);
    if (index > -1) {
      this.notificationCallbacks.splice(index, 1);
    }
  }

  /**
   * Show notification to user
   */
  private showNotification(notification: ApiErrorNotification): void {
    this.notificationCallbacks.forEach(callback => callback(notification));
  }

  /**
   * Handle Hugging Face API errors
   */
  handleHuggingFaceError(error: HFError): void {
    console.error('Hugging Face API Error:', error);

    let notification: ApiErrorNotification;

    switch (error.status) {
      case 401:
        notification = {
          type: 'error',
          title: 'Authentication Error',
          message: 'Invalid API key. Please check your Hugging Face configuration.',
          duration: 0
        };
        break;
      case 429:
        notification = {
          type: 'warning',
          title: 'Rate Limited',
          message: 'Too many requests. Nathan will retry automatically in a moment.',
          duration: 5000
        };
        break;
      case 503:
        notification = {
          type: 'warning',
          title: 'Service Unavailable',
          message: 'Hugging Face service is temporarily unavailable. Retrying...',
          action: {
            label: 'Retry Now',
            handler: () => this.triggerRetry('huggingface')
          },
          duration: 8000
        };
        break;
      case 0:
        notification = {
          type: 'error',
          title: 'Connection Failed',
          message: 'Unable to connect to AI service. Check your internet connection.',
          action: {
            label: 'Retry',
            handler: () => this.triggerRetry('huggingface')
          },
          duration: 0
        };
        break;
      default:
        notification = {
          type: 'error',
          title: 'AI Service Error',
          message: 'Nathan encountered an issue generating a response. Please try again.',
          action: {
            label: 'Retry',
            handler: () => this.triggerRetry('huggingface')
          },
          duration: 8000
        };
    }

    this.showNotification(notification);
  }

  /**
   * Handle Eleven Labs API errors
   */
  handleElevenLabsError(error: ELError): void {
    console.error('Eleven Labs API Error:', error);

    let notification: ApiErrorNotification;

    switch (error.status) {
      case 401:
        notification = {
          type: 'error',
          title: 'Voice Authentication Error',
          message: 'Invalid Eleven Labs API key. Voice responses are disabled.',
          duration: 0
        };
        break;
      case 429:
        notification = {
          type: 'warning',
          title: 'Voice Rate Limited',
          message: 'Voice generation rate limited. Text responses will continue.',
          duration: 5000
        };
        break;
      case 503:
        notification = {
          type: 'warning',
          title: 'Voice Service Unavailable',
          message: 'Voice synthesis temporarily unavailable. Text responses will continue.',
          action: {
            label: 'Retry Voice',
            handler: () => this.triggerRetry('elevenlabs')
          },
          duration: 8000
        };
        break;
      case 0:
        notification = {
          type: 'warning',
          title: 'Voice Connection Failed',
          message: 'Unable to connect to voice service. Text responses will continue.',
          action: {
            label: 'Retry Voice',
            handler: () => this.triggerRetry('elevenlabs')
          },
          duration: 8000
        };
        break;
      default:
        notification = {
          type: 'warning',
          title: 'Voice Generation Error',
          message: 'Voice synthesis failed. Text response is available.',
          action: {
            label: 'Retry Voice',
            handler: () => this.triggerRetry('elevenlabs')
          },
          duration: 5000
        };
    }

    this.showNotification(notification);
  }

  /**
   * Handle network errors
   */
  handleNetworkError(error: NetworkError): void {
    console.error('Network Error:', error);

    const notification: ApiErrorNotification = {
      type: 'error',
      title: 'Network Error',
      message: 'Connection failed. Please check your internet connection.',
      action: {
        label: 'Retry',
        handler: () => this.triggerRetry('network')
      },
      duration: 0
    };

    this.showNotification(notification);
  }

  /**
   * Show user-friendly error message based on error type
   */
  showUserFriendlyMessage(errorType: ErrorType): void {
    const messages = {
      api: {
        type: 'error' as const,
        title: 'Service Error',
        message: 'Nathan encountered a service issue. Please try again.',
        duration: 5000
      },
      network: {
        type: 'error' as const,
        title: 'Connection Error',
        message: 'Unable to connect. Please check your internet connection.',
        duration: 8000
      },
      validation: {
        type: 'warning' as const,
        title: 'Input Error',
        message: 'Please check your input and try again.',
        duration: 5000
      },
      browser: {
        type: 'warning' as const,
        title: 'Browser Compatibility',
        message: 'Some features may not work in your browser. Try updating or switching browsers.',
        duration: 8000
      },
      unknown: {
        type: 'error' as const,
        title: 'Unexpected Error',
        message: 'Something went wrong. Please try again.',
        duration: 5000
      }
    };

    this.showNotification(messages[errorType]);
  }

  /**
   * Execute retry with exponential backoff
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string = 'operation'
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry if not retryable
        if (!this.isRetryableError(error)) {
          throw error;
        }
        
        // Don't retry on last attempt
        if (attempt === this.retryConfig.maxRetries) {
          break;
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt),
          this.retryConfig.maxDelay
        );
        
        console.log(`${context} failed (attempt ${attempt + 1}), retrying in ${delay}ms...`);
        await this.delay(delay);
      }
    }
    
    throw lastError!;
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors are retryable
    if (error.name === 'TypeError' || error.code === 'NETWORK_ERROR') {
      return true;
    }
    
    // API errors with retryable status codes
    if (error instanceof HFError || error instanceof ELError) {
      return this.retryConfig.retryableStatuses.includes(error.status || 0);
    }
    
    return false;
  }

  /**
   * Add request to offline queue
   */
  addToOfflineQueue(type: 'text' | 'audio', data: any): string {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const item: OfflineQueueItem = {
      id,
      timestamp: Date.now(),
      type,
      data,
      retryCount: 0
    };
    
    this.offlineQueue.push(item);
    
    this.showNotification({
      type: 'info',
      title: 'Message Queued',
      message: 'Your message has been queued and will be sent when connection is restored.',
      duration: 3000
    });
    
    return id;
  }

  /**
   * Process offline queue when connection is restored
   */
  private async processOfflineQueue(): Promise<void> {
    if (!this.isOnline || this.offlineQueue.length === 0) {
      return;
    }

    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const item of queue) {
      try {
        // This would need to be implemented based on the specific retry logic
        // For now, we'll just log that we're processing
        console.log(`Processing queued ${item.type} request:`, item.id);
        
        // In a real implementation, you'd call the appropriate service method
        // await this.processQueuedItem(item);
        
      } catch (error) {
        console.error(`Failed to process queued item ${item.id}:`, error);
        
        // Re-queue if retry count is below threshold
        if (item.retryCount < this.retryConfig.maxRetries) {
          item.retryCount++;
          this.offlineQueue.push(item);
        }
      }
    }

    if (this.offlineQueue.length > 0) {
      this.showNotification({
        type: 'warning',
        title: 'Some Messages Failed',
        message: `${this.offlineQueue.length} messages couldn't be processed. They'll be retried.`,
        duration: 5000
      });
    }
  }

  /**
   * Get offline queue status
   */
  getOfflineQueueStatus(): { count: number; items: OfflineQueueItem[] } {
    return {
      count: this.offlineQueue.length,
      items: [...this.offlineQueue]
    };
  }

  /**
   * Clear offline queue
   */
  clearOfflineQueue(): void {
    this.offlineQueue = [];
  }

  /**
   * Check if currently online
   */
  isCurrentlyOnline(): boolean {
    return this.isOnline;
  }

  /**
   * Trigger manual retry (placeholder for UI integration)
   */
  private triggerRetry(service: string): void {
    console.log(`Manual retry triggered for ${service}`);
    // This would be implemented to trigger specific retry logic
    // based on the service and current context
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get retry configuration
   */
  getRetryConfig(): RetryConfig {
    return { ...this.retryConfig };
  }

  /**
   * Update retry configuration
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config };
  }
}