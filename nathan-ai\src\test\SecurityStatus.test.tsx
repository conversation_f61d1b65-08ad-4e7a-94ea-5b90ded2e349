import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { SecurityStatus } from '../components/SecurityStatus';

// Mock the environment and security utilities
vi.mock('../utils/env', () => ({
  getEnvironmentValidation: vi.fn(),
}));

vi.mock('../utils/security', () => ({
  getRemainingApiRequests: vi.fn(),
}));

import { getEnvironmentValidation } from '../utils/env';
import { getRemainingApiRequests } from '../utils/security';

const mockGetEnvironmentValidation = getEnvironmentValidation as any;
const mockGetRemainingApiRequests = getRemainingApiRequests as any;

describe('SecurityStatus Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    mockGetEnvironmentValidation.mockReturnValue({
      isValid: true,
      errors: [],
      warnings: [],
      securityIssues: [],
    });
    
    mockGetRemainingApiRequests.mockReturnValue({
      perMinute: 50,
      perHour: 800,
    });
  });

  describe('Valid Environment', () => {
    it('should not render when environment is valid and showDetails is false', () => {
      const { container } = render(<SecurityStatus showDetails={false} />);
      expect(container.firstChild).toBeNull();
    });

    it('should show success status when environment is valid and showDetails is true', () => {
      render(<SecurityStatus showDetails={true} />);
      
      expect(screen.getByText('✅ Security Status: OK')).toBeInTheDocument();
      expect(screen.getByText('All security checks passed successfully.')).toBeInTheDocument();
    });

    it('should show API rate limits when showDetails is true', () => {
      render(<SecurityStatus showDetails={true} />);
      
      expect(screen.getByText('📊 API Rate Limits')).toBeInTheDocument();
      expect(screen.getByText('Hugging Face:')).toBeInTheDocument();
      expect(screen.getByText('Eleven Labs:')).toBeInTheDocument();
      expect(screen.getAllByText('50 requests/min remaining')).toHaveLength(2);
      expect(screen.getAllByText('800 requests/hour remaining')).toHaveLength(2);
    });
  });

  describe('Environment with Errors', () => {
    beforeEach(() => {
      mockGetEnvironmentValidation.mockReturnValue({
        isValid: false,
        errors: ['Missing required environment variable: VITE_HUGGING_FACE_API_KEY'],
        warnings: ['Optional environment variable not set: VITE_HUGGING_FACE_MODEL'],
        securityIssues: ['Environment variable appears to be a placeholder value'],
      });
    });

    it('should always render when there are errors', () => {
      render(<SecurityStatus showDetails={false} />);
      
      expect(screen.getByText('⚠️ Security Issues Detected')).toBeInTheDocument();
    });

    it('should display errors', () => {
      render(<SecurityStatus showDetails={false} />);
      
      expect(screen.getByText('Errors:')).toBeInTheDocument();
      expect(screen.getByText('Missing required environment variable: VITE_HUGGING_FACE_API_KEY')).toBeInTheDocument();
    });

    it('should display security issues', () => {
      render(<SecurityStatus showDetails={false} />);
      
      expect(screen.getByText('Security Issues:')).toBeInTheDocument();
      expect(screen.getByText('Environment variable appears to be a placeholder value')).toBeInTheDocument();
    });

    it('should display warnings when showDetails is true', () => {
      render(<SecurityStatus showDetails={true} />);
      
      expect(screen.getByText('⚡ Warnings')).toBeInTheDocument();
      expect(screen.getByText('Optional environment variable not set: VITE_HUGGING_FACE_MODEL')).toBeInTheDocument();
    });

    it('should not display warnings when showDetails is false', () => {
      render(<SecurityStatus showDetails={false} />);
      
      expect(screen.queryByText('⚡ Warnings')).not.toBeInTheDocument();
    });
  });

  describe('Environment with Only Warnings', () => {
    beforeEach(() => {
      mockGetEnvironmentValidation.mockReturnValue({
        isValid: true,
        errors: [],
        warnings: ['Optional environment variable not set: VITE_HUGGING_FACE_MODEL'],
        securityIssues: [],
      });
    });

    it('should not render when only warnings exist and showDetails is false', () => {
      const { container } = render(<SecurityStatus showDetails={false} />);
      expect(container.firstChild).toBeNull();
    });

    it('should show warnings when showDetails is true', () => {
      render(<SecurityStatus showDetails={true} />);
      
      expect(screen.getByText('⚡ Warnings')).toBeInTheDocument();
      expect(screen.getByText('Optional environment variable not set: VITE_HUGGING_FACE_MODEL')).toBeInTheDocument();
    });
  });

  describe('Rate Limit Display', () => {
    it('should display different rate limits for different APIs', () => {
      mockGetRemainingApiRequests
        .mockReturnValueOnce({ perMinute: 30, perHour: 400 }) // huggingface
        .mockReturnValueOnce({ perMinute: 25, perHour: 300 }); // elevenlabs
      
      render(<SecurityStatus showDetails={true} />);
      
      expect(screen.getByText('30 requests/min remaining')).toBeInTheDocument();
      expect(screen.getByText('400 requests/hour remaining')).toBeInTheDocument();
      expect(screen.getByText('25 requests/min remaining')).toBeInTheDocument();
      expect(screen.getByText('300 requests/hour remaining')).toBeInTheDocument();
    });

    it('should handle zero remaining requests', () => {
      mockGetRemainingApiRequests.mockReturnValue({
        perMinute: 0,
        perHour: 0,
      });
      
      render(<SecurityStatus showDetails={true} />);
      
      expect(screen.getAllByText('0 requests/min remaining')).toHaveLength(2);
      expect(screen.getAllByText('0 requests/hour remaining')).toHaveLength(2);
    });
  });

  describe('Custom Styling', () => {
    it('should apply custom className', () => {
      mockGetEnvironmentValidation.mockReturnValue({
        isValid: false,
        errors: ['Test error'],
        warnings: [],
        securityIssues: [],
      });
      
      const { container } = render(<SecurityStatus className="custom-class" />);
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('API Calls', () => {
    it('should call getEnvironmentValidation once', () => {
      render(<SecurityStatus showDetails={true} />);
      
      expect(mockGetEnvironmentValidation).toHaveBeenCalledTimes(1);
    });

    it('should call getRemainingApiRequests for both APIs when showDetails is true', () => {
      render(<SecurityStatus showDetails={true} />);
      
      expect(mockGetRemainingApiRequests).toHaveBeenCalledWith('huggingface');
      expect(mockGetRemainingApiRequests).toHaveBeenCalledWith('elevenlabs');
      expect(mockGetRemainingApiRequests).toHaveBeenCalledTimes(2);
    });

    it('should call getRemainingApiRequests for both APIs even when showDetails is false but there are errors', () => {
      mockGetEnvironmentValidation.mockReturnValue({
        isValid: false,
        errors: ['Test error'],
        warnings: [],
        securityIssues: [],
      });
      
      render(<SecurityStatus showDetails={false} />);
      
      expect(mockGetRemainingApiRequests).toHaveBeenCalledWith('huggingface');
      expect(mockGetRemainingApiRequests).toHaveBeenCalledWith('elevenlabs');
    });
  });
});