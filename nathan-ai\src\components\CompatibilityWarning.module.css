.compatibilityWarning {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  max-width: 600px;
  width: 90%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  border: 2px solid;
  animation: slideDown 0.3s ease-out;
}

.compatibilityWarning.good {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.compatibilityWarning.warning {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.compatibilityWarning.critical {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.titleSection {
  flex: 1;
}

.title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 12px;
}

.score {
  font-size: 14px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}

.good .score {
  background: #10b981;
  color: white;
}

.warning .score {
  background: #f59e0b;
  color: white;
}

.critical .score {
  background: #ef4444;
  color: white;
}

.browserInfo {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.dismissButton {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dismissButton:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}

.content {
  padding: 0 20px;
  max-height: 400px;
  overflow-y: auto;
}

.issueSection {
  margin-bottom: 16px;
}

.issueTitle {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 6px;
}

.criticalIcon,
.warningIcon,
.infoIcon,
.mobileIcon {
  font-size: 16px;
}

.issueList {
  margin: 0;
  padding: 0;
  list-style: none;
}

.issueItem {
  margin-bottom: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.fallback {
  margin-top: 4px;
  font-size: 12px;
  color: #059669;
  font-weight: 500;
}

.guidance {
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.recommendation {
  margin: 16px 0;
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  border-radius: 6px;
  font-size: 13px;
  color: #1e40af;
}

.permissionSection {
  margin: 16px 0;
  padding: 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.permissionButton {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.permissionButton:hover {
  background: #2563eb;
}

.permissionStatus h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.permissionStatus ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.permissionStatus li {
  font-size: 12px;
  margin-bottom: 4px;
  color: #6b7280;
}

.permissionLoading {
  font-size: 13px;
  color: #6b7280;
  font-style: italic;
}

.actions {
  padding: 16px 20px 20px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
}

.continueButton {
  background: #6b7280;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.continueButton:hover {
  background: #4b5563;
}

@keyframes slideDown {
  from {
    transform: translateX(-50%) translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .compatibilityWarning {
    top: 10px;
    width: 95%;
    max-width: none;
  }
  
  .header {
    padding: 16px 16px 0 16px;
  }
  
  .content {
    padding: 0 16px;
    max-height: 300px;
  }
  
  .actions {
    padding: 12px 16px 16px 16px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .score {
    font-size: 12px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .compatibilityWarning {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .compatibilityWarning.good {
    background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
  }
  
  .compatibilityWarning.warning {
    background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
  }
  
  .compatibilityWarning.critical {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  }
  
  .title {
    color: #f9fafb;
  }
  
  .browserInfo {
    color: #d1d5db;
  }
  
  .issueTitle {
    color: #e5e7eb;
  }
  
  .issueItem {
    background: rgba(0, 0, 0, 0.3);
    color: #e5e7eb;
  }
  
  .guidance {
    color: #9ca3af;
  }
  
  .recommendation {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
  }
  
  .permissionSection {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .permissionStatus h5 {
    color: #e5e7eb;
  }
  
  .permissionStatus li {
    color: #d1d5db;
  }
}