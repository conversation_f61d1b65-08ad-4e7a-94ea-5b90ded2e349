import React from 'react';
import styles from './ErrorRecovery.module.css';

interface ErrorRecoveryProps {
  error: Error;
  onRetry?: () => void;
  onReset?: () => void;
  showDetails?: boolean;
  title?: string;
  message?: string;
}

export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onReset,
  showDetails = false,
  title = 'Something went wrong',
  message = 'An error occurred while processing your request.'
}) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className={styles.errorRecovery}>
      <div className={styles.content}>
        <div className={styles.icon}>⚠️</div>
        <h3 className={styles.title}>{title}</h3>
        <p className={styles.message}>{message}</p>
        
        {showDetails && (
          <details className={styles.details}>
            <summary>Error Details</summary>
            <pre className={styles.errorText}>
              {error.message}
              {error.stack && (
                <>
                  {'\n\nStack Trace:\n'}
                  {error.stack}
                </>
              )}
            </pre>
          </details>
        )}
        
        <div className={styles.actions}>
          {onRetry && (
            <button onClick={onRetry} className={styles.primaryButton}>
              Try Again
            </button>
          )}
          {onReset && (
            <button onClick={onReset} className={styles.secondaryButton}>
              Reset
            </button>
          )}
          <button onClick={handleRefresh} className={styles.tertiaryButton}>
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
};