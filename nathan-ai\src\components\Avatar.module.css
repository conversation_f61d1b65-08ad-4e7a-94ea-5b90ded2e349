.avatar {
  position: relative;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Size variants */
.small {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.medium {
  width: 48px;
  height: 48px;
  font-size: 20px;
}

.large {
  width: 64px;
  height: 64px;
  font-size: 24px;
}

/* Emotion-based backgrounds */
.neutral {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.happy {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.thoughtful {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.empathetic {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.avatarContent {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.animationOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.pulseRing {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 2px solid transparent;
}

.glowEffect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0;
}

/* Animation classes */
.neutralAnimation {
  animation: neutralBreathing 4s ease-in-out infinite;
}

.neutralAnimation .pulseRing {
  border-color: rgba(79, 172, 254, 0.3);
  animation: neutralPulse 4s ease-in-out infinite;
}

.neutralAnimation .glowEffect {
  background: radial-gradient(circle, rgba(79, 172, 254, 0.2) 0%, transparent 70%);
  animation: neutralGlow 4s ease-in-out infinite;
}

.happyAnimation {
  animation: happyBounce 2s ease-in-out infinite;
}

.happyAnimation .pulseRing {
  border-color: rgba(67, 233, 123, 0.4);
  animation: happyPulse 2s ease-in-out infinite;
}

.happyAnimation .glowEffect {
  background: radial-gradient(circle, rgba(67, 233, 123, 0.3) 0%, transparent 70%);
  animation: happyGlow 2s ease-in-out infinite;
}

.thoughtfulAnimation {
  animation: thoughtfulSway 3s ease-in-out infinite;
}

.thoughtfulAnimation .pulseRing {
  border-color: rgba(250, 112, 154, 0.3);
  animation: thoughtfulPulse 3s ease-in-out infinite;
}

.thoughtfulAnimation .glowEffect {
  background: radial-gradient(circle, rgba(250, 112, 154, 0.2) 0%, transparent 70%);
  animation: thoughtfulGlow 3s ease-in-out infinite;
}

.empatheticAnimation {
  animation: empatheticWarm 2.5s ease-in-out infinite;
}

.empatheticAnimation .pulseRing {
  border-color: rgba(168, 237, 234, 0.4);
  animation: empatheticPulse 2.5s ease-in-out infinite;
}

.empatheticAnimation .glowEffect {
  background: radial-gradient(circle, rgba(168, 237, 234, 0.3) 0%, transparent 70%);
  animation: empatheticGlow 2.5s ease-in-out infinite;
}

/* Keyframe animations */
@keyframes neutralBreathing {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes neutralPulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

@keyframes neutralGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.4; }
}

@keyframes happyBounce {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.05) rotate(-2deg); }
  75% { transform: scale(1.05) rotate(2deg); }
}

@keyframes happyPulse {
  0%, 100% { transform: scale(1); opacity: 0.4; }
  50% { transform: scale(1.15); opacity: 0.7; }
}

@keyframes happyGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.6; }
}

@keyframes thoughtfulSway {
  0%, 100% { transform: scale(1) rotate(0deg); }
  33% { transform: scale(1.02) rotate(-1deg); }
  66% { transform: scale(1.02) rotate(1deg); }
}

@keyframes thoughtfulPulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.08); opacity: 0.5; }
}

@keyframes thoughtfulGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.3; }
}

@keyframes empatheticWarm {
  0%, 100% { transform: scale(1); filter: brightness(1); }
  50% { transform: scale(1.03); filter: brightness(1.1); }
}

@keyframes empatheticPulse {
  0%, 100% { transform: scale(1); opacity: 0.4; }
  50% { transform: scale(1.12); opacity: 0.6; }
}

@keyframes empatheticGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.5; }
}

/* Hover effects for interactive avatars */
.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.avatar:hover .pulseRing {
  animation-duration: 1s;
}

.avatar:hover .glowEffect {
  opacity: 0.3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .small {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .medium {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .large {
    width: 56px;
    height: 56px;
    font-size: 20px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .avatar,
  .pulseRing,
  .glowEffect {
    animation: none !important;
  }
  
  .avatar {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .avatar {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .avatar:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}