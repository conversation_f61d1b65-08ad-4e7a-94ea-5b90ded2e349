import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { MessageBubble } from '../components/MessageBubble';
import type { Message } from '../types/message';
import type { PersonalityConfig } from '../types/personality';

const mockPersonality: PersonalityConfig = {
  name: '<PERSON>',
  pronouns: 'he/him',
  personality: {
    tone: 'friendly',
    role: 'companion',
    hobbies: ['coding', 'music'],
    style: {
      speech: 'casual',
      humor: 'light',
      depth: 'thoughtful'
    },
    boundaries: {
      avoid: ['harmful content'],
      safe_topics: ['technology', 'music']
    },
    dynamic_traits: {
      adaptive_empathy: true,
      mirroring_style: true,
      emotionally_available: true
    }
  },
  conversation_tips: {
    starter_prompts: ['Hello!', 'How are you?']
  },
  version: '1.0.0'
};

const createMockMessage = (overrides: Partial<Message> = {}): Message => ({
  id: '1',
  content: 'Hello there!',
  timestamp: new Date('2024-01-01T12:00:00Z'),
  sender: 'nathan',
  ...overrides
});

describe('MessageBubble', () => {
  it('renders message content correctly', () => {
    const message = createMockMessage({ content: 'Test message content' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('Test message content')).toBeInTheDocument();
  });

  it('displays timestamp in correct format', () => {
    const message = createMockMessage({
      timestamp: new Date('2024-01-01T14:30:00Z')
    });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    // Check that timestamp is displayed (format may vary by locale)
    expect(screen.getByText(/\d{1,2}:\d{2}/)).toBeInTheDocument();
  });

  it('shows avatar for Nathan when showAvatar is true', () => {
    const message = createMockMessage();
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('N')).toBeInTheDocument();
  });

  it('shows user avatar when isUser is true and showAvatar is true', () => {
    const message = createMockMessage({ sender: 'user' });
    
    render(
      <MessageBubble
        message={message}
        isUser={true}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('U')).toBeInTheDocument();
  });

  it('hides avatar when showAvatar is false', () => {
    const message = createMockMessage();
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={false}
        personality={mockPersonality}
      />
    );
    
    expect(screen.queryByText('N')).not.toBeInTheDocument();
  });

  it('displays emotion indicator for happy emotion', () => {
    const message = createMockMessage({ emotion: 'happy' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('😊')).toBeInTheDocument();
  });

  it('displays emotion indicator for thoughtful emotion', () => {
    const message = createMockMessage({ emotion: 'thoughtful' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('🤔')).toBeInTheDocument();
  });

  it('displays emotion indicator for empathetic emotion', () => {
    const message = createMockMessage({ emotion: 'empathetic' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('💙')).toBeInTheDocument();
  });

  it('does not display emotion indicator for neutral emotion', () => {
    const message = createMockMessage({ emotion: 'neutral' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.queryByText('😊')).not.toBeInTheDocument();
    expect(screen.queryByText('🤔')).not.toBeInTheDocument();
    expect(screen.queryByText('💙')).not.toBeInTheDocument();
  });

  it('shows audio indicator when audioUrl is present', () => {
    const message = createMockMessage({ audioUrl: 'https://example.com/audio.mp3' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText('🔊')).toBeInTheDocument();
    expect(screen.getByTitle('Audio available')).toBeInTheDocument();
  });

  it('does not show audio indicator when audioUrl is not present', () => {
    const message = createMockMessage();
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.queryByText('🔊')).not.toBeInTheDocument();
  });

  it('applies correct CSS classes for user messages', () => {
    const message = createMockMessage({ sender: 'user' });
    
    const { container } = render(
      <MessageBubble
        message={message}
        isUser={true}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    // Check that the container has the user message styling
    const messageContainer = container.firstChild as HTMLElement;
    expect(messageContainer.className).toContain('userMessage');
  });

  it('applies correct CSS classes for Nathan messages', () => {
    const message = createMockMessage();
    
    const { container } = render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    // Check that the container has the Nathan message styling
    const messageContainer = container.firstChild as HTMLElement;
    expect(messageContainer.className).toContain('nathanMessage');
  });

  it('handles long message content without breaking layout', () => {
    const longMessage = 'This is a very long message that should wrap properly and not break the layout of the message bubble component even when it contains a lot of text content.';
    const message = createMockMessage({ content: longMessage });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    expect(screen.getByText(longMessage)).toBeInTheDocument();
  });

  it('handles empty message content gracefully', () => {
    const message = createMockMessage({ content: '' });
    
    render(
      <MessageBubble
        message={message}
        isUser={false}
        showAvatar={true}
        personality={mockPersonality}
      />
    );
    
    // Should still render the component structure
    expect(screen.getByText('N')).toBeInTheDocument();
  });
});