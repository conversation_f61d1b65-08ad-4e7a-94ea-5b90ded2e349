import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AIService } from '../services/AIService';
import { useAudioConversation } from '../hooks/useAudioConversation';
import { renderHook, act, waitFor } from '@testing-library/react';
import type { PersonalityConfig } from '../types/personality';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  },
  getEnvVar: vi.fn().mockImplementation((key: string, defaultValue?: string) => {
    const mockEnv: Record<string, string> = {
      'VITE_HUGGING_FACE_API_KEY': 'test-hf-key',
      'VITE_ELEVEN_LABS_API_KEY': 'test-el-key',
      'VITE_ELEVEN_LABS_VOICE_ID': 'test-voice-id'
    };
    return mockEnv[key] || defaultValue || '';
  })
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock personality hook
vi.mock('../hooks/usePersonality', () => ({
  usePersonality: () => ({
    personality: {
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly and supportive',
        role: 'conversational AI companion',
        hobbies: ['technology', 'learning', 'helping others'],
        style: {
          speech: 'natural and conversational',
          humor: 'light and appropriate',
          depth: 'adaptable to context'
        },
        boundaries: {
          avoid: ['harmful content', 'inappropriate topics'],
          safe_topics: ['technology', 'general conversation', 'learning']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: [
          'Hello! How are you doing today?',
          'What would you like to talk about?',
          'I\'m here to help with anything you need!'
        ]
      },
      version: '1.0.0'
    } as PersonalityConfig,
    isPersonalityReady: true
  })
}));

// Mock conversation hook
vi.mock('../hooks/useConversation', () => ({
  useConversation: () => ({
    messages: [],
    isLoading: false,
    isListening: false,
    isPlaying: false,
    inputMode: 'text' as const,
    visualMode: 'visual' as const,
    error: null,
    addUserMessage: vi.fn().mockReturnValue({
      id: 'user-msg-1',
      content: 'Hello Nathan',
      timestamp: new Date(),
      sender: 'user' as const
    }),
    addNathanMessage: vi.fn().mockReturnValue({
      id: 'nathan-msg-1',
      content: 'Hello! How can I help you today?',
      timestamp: new Date(),
      sender: 'nathan' as const,
      emotion: 'happy' as const
    }),
    addMessage: vi.fn(),
    setLoading: vi.fn(),
    setListening: vi.fn(),
    setPlaying: vi.fn(),
    setInputMode: vi.fn(),
    setVisualMode: vi.fn(),
    setError: vi.fn(),
    clearMessages: vi.fn()
  })
}));

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-audio-url');
global.URL.revokeObjectURL = vi.fn();

describe('End-to-End Workflow Tests', () => {
  let mockAIService: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
      healthCheck: vi.fn().mockResolvedValue({
        online: true,
        huggingFace: true,
        elevenLabs: true,
        overall: true
      })
    };
    (AIService as any).mockImplementation(() => mockAIService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Conversation Workflows', () => {
    it('should handle complete text-to-speech conversation workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValueOnce({
        text: 'Hello! Nice to meet you!',
        emotion: 'happy',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Send message and expect full workflow
      await act(async () => {
        await result.current.sendMessage('Hello Nathan!');
      });

      // Verify AI service was called with proper parameters
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'Hello Nathan!',
        expect.objectContaining({
          name: 'Nathan',
          personality: expect.objectContaining({
            tone: 'friendly and supportive',
            role: 'conversational AI companion'
          })
        }),
        { max_length: 150, temperature: 0.8 },
        { voice_settings: { stability: 0.75, similarity_boost: 0.75 } }
      );

      // Verify audio was processed
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0]).toMatchObject({
        id: 'nathan-msg-1',
        audioUrl: 'blob:mock-audio-url',
        text: 'Hello! Nice to meet you!',
        priority: 1
      });

      // Verify audio URL was created
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(expect.any(Blob));
    });

    it('should handle multi-turn conversation with context preservation', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'Hello! How can I help you today?',
          emotion: 'friendly',
          audio: mockAudioBuffer
        })
        .mockResolvedValueOnce({
          text: 'I\'d be happy to help you learn React! What specific aspect interests you?',
          emotion: 'helpful',
          audio: mockAudioBuffer
        })
        .mockResolvedValueOnce({
          text: 'Great choice! React hooks are powerful. Let me explain useState first...',
          emotion: 'enthusiastic',
          audio: mockAudioBuffer
        });

      const { result } = renderHook(() => useAudioConversation());

      // First exchange
      await act(async () => {
        await result.current.sendMessage('Hi Nathan!');
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(1);
      expect(result.current.audioQueue).toHaveLength(1);

      // Second exchange
      await act(async () => {
        await result.current.sendMessage('Can you help me learn React?');
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
      
      // Third exchange
      await act(async () => {
        await result.current.sendMessage('I want to learn about hooks');
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);

      // Verify conversation context is maintained
      const thirdCall = mockAIService.generateResponse.mock.calls[2];
      expect(thirdCall[0]).toBe('I want to learn about hooks');
      expect(thirdCall[1]).toMatchObject({
        name: 'Nathan',
        personality: expect.objectContaining({
          tone: 'friendly and supportive'
        })
      });

      // Should have processed all audio
      expect(result.current.audioQueue).toHaveLength(1); // Only latest due to interruption
    });

    it('should handle error recovery in complete workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      // First call fails, second succeeds
      mockAIService.generateResponse
        .mockRejectedValueOnce(new Error('Service temporarily unavailable'))
        .mockResolvedValueOnce({
          text: 'Sorry about that! I\'m back online now.',
          emotion: 'apologetic',
          audio: mockAudioBuffer
        });

      const { result } = renderHook(() => useAudioConversation());

      // First attempt should fail
      await expect(async () => {
        await act(async () => {
          await result.current.sendMessage('Hello Nathan!');
        });
      }).rejects.toThrow('Service temporarily unavailable');

      expect(result.current.audioQueue).toHaveLength(0);

      // Second attempt should succeed
      await act(async () => {
        await result.current.sendMessage('Hello Nathan!');
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0].text).toBe('Sorry about that! I\'m back online now.');
    });

    it('should handle audio-only generation workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      const existingMessage = {
        id: 'existing-msg',
        content: 'Generate audio for this message',
        timestamp: new Date(),
        sender: 'nathan' as const,
        emotion: 'neutral' as const
      };

      mockAIService.generateAudio.mockResolvedValue(mockAudioBuffer);

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.generateAudioForMessage(existingMessage);
      });

      // Verify audio generation was called
      expect(mockAIService.generateAudio).toHaveBeenCalledWith(
        'Generate audio for this message',
        { voice_settings: { stability: 0.75, similarity_boost: 0.75 } }
      );

      // Verify audio was queued
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0]).toMatchObject({
        id: 'existing-msg',
        audioUrl: 'blob:mock-audio-url',
        text: 'Generate audio for this message',
        priority: 1
      });
    });

    it('should handle conversation clearing workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'This conversation will be cleared',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Add some conversation and audio
      await act(async () => {
        await result.current.sendMessage('Test message');
      });

      expect(result.current.audioQueue).toHaveLength(1);
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(1);

      // Clear conversation
      act(() => {
        result.current.clearConversation();
      });

      // Verify cleanup
      expect(result.current.audioQueue).toHaveLength(0);
      expect(mockAIService.clearConversationHistory).toHaveBeenCalled();
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-audio-url');
    });
  });

  describe('Audio Playback Workflows', () => {
    it('should handle complete audio playback workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Audio playback test message',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Send message with audio
      await act(async () => {
        await result.current.sendMessage('Play audio test');
      });

      expect(result.current.audioQueue).toHaveLength(1);

      // Simulate audio playback start
      act(() => {
        result.current.handleAudioPlayingChange(true);
      });

      // Simulate audio completion
      act(() => {
        result.current.handleAudioComplete('nathan-msg-1');
      });

      // Audio should be removed from queue and URL cleaned up
      expect(result.current.audioQueue).toHaveLength(0);
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-audio-url');
    });

    it('should handle audio interruption workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'First message with audio',
          emotion: 'neutral',
          audio: mockAudioBuffer
        })
        .mockResolvedValueOnce({
          text: 'Second message interrupts first',
          emotion: 'neutral',
          audio: mockAudioBuffer
        });

      const { result } = renderHook(() => useAudioConversation());

      // Send first message
      await act(async () => {
        await result.current.sendMessage('First message');
      });

      expect(result.current.audioQueue).toHaveLength(1);
      const firstAudioUrl = result.current.audioQueue[0].audioUrl;

      // Send second message (should interrupt first)
      await act(async () => {
        await result.current.sendMessage('Second message');
      });

      // Should have new audio and cleaned up old
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0].text).toBe('Second message interrupts first');
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith(firstAudioUrl);
    });

    it('should handle audio error workflow', async () => {
      const { result } = renderHook(() => useAudioConversation());

      // Add audio to queue
      act(() => {
        result.current.audioQueue.push({
          id: 'test-audio',
          audioUrl: 'blob:test-url',
          text: 'Test audio',
          priority: 1
        });
      });

      expect(result.current.audioQueue).toHaveLength(1);

      // Simulate audio error
      act(() => {
        result.current.handleAudioError('Audio playback failed');
      });

      // Should handle error gracefully (implementation dependent)
      expect(result.current.handleAudioError).toBeDefined();
    });
  });

  describe('Service Integration Workflows', () => {
    it('should handle service health check workflow', async () => {
      mockAIService.healthCheck.mockResolvedValue({
        online: true,
        huggingFace: true,
        elevenLabs: true,
        overall: true
      });

      const { result } = renderHook(() => useAudioConversation());

      // Access AI service for health check
      const aiService = result.current.aiService;
      const health = await aiService.healthCheck();

      expect(health).toEqual({
        online: true,
        huggingFace: true,
        elevenLabs: true,
        overall: true
      });

      expect(mockAIService.healthCheck).toHaveBeenCalled();
    });

    it('should handle service failure and recovery workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      // Service fails then recovers
      mockAIService.generateResponse
        .mockRejectedValueOnce(new Error('Service down'))
        .mockRejectedValueOnce(new Error('Service down'))
        .mockResolvedValueOnce({
          text: 'Service recovered successfully',
          emotion: 'relieved',
          audio: mockAudioBuffer
        });

      const { result } = renderHook(() => useAudioConversation());

      // First attempt fails
      await expect(async () => {
        await act(async () => {
          await result.current.sendMessage('Test message');
        });
      }).rejects.toThrow('Service down');

      // Second attempt fails
      await expect(async () => {
        await act(async () => {
          await result.current.sendMessage('Test message');
        });
      }).rejects.toThrow('Service down');

      // Third attempt succeeds
      await act(async () => {
        await result.current.sendMessage('Test message');
      });

      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);
      expect(result.current.audioQueue).toHaveLength(1);
      expect(result.current.audioQueue[0].text).toBe('Service recovered successfully');
    });

    it('should handle partial service failure workflow', async () => {
      // Text generation works, but TTS fails
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Text response without audio',
        emotion: 'neutral',
        audio: null // TTS failed
      });

      const { result } = renderHook(() => useAudioConversation());

      await act(async () => {
        await result.current.sendMessage('Test partial failure');
      });

      // Should get text response but no audio
      expect(mockAIService.generateResponse).toHaveBeenCalled();
      expect(result.current.audioQueue).toHaveLength(0); // No audio generated
    });
  });

  describe('Memory Management Workflows', () => {
    it('should handle memory cleanup on unmount', () => {
      const { result, unmount } = renderHook(() => useAudioConversation());

      // Add audio URLs to cache
      act(() => {
        result.current.audioQueue.push(
          {
            id: 'audio-1',
            audioUrl: 'blob:url-1',
            text: 'Audio 1',
            priority: 1
          },
          {
            id: 'audio-2',
            audioUrl: 'blob:url-2',
            text: 'Audio 2',
            priority: 1
          }
        );
      });

      expect(result.current.audioQueue).toHaveLength(2);

      // Unmount should clean up
      unmount();

      // Should have cleaned up all URLs
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:url-1');
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:url-2');
    });

    it('should handle rapid message sending workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Rapid response',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Send multiple messages rapidly
      const promises = [
        act(async () => await result.current.sendMessage('Message 1')),
        act(async () => await result.current.sendMessage('Message 2')),
        act(async () => await result.current.sendMessage('Message 3'))
      ];

      await Promise.all(promises);

      // Should handle all messages
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(3);
      
      // Should only have latest audio due to interruption
      expect(result.current.audioQueue).toHaveLength(1);
    });

    it('should handle conversation state persistence workflow', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Persistent conversation message',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Build up conversation state
      await act(async () => {
        await result.current.sendMessage('Message 1');
      });

      await act(async () => {
        await result.current.sendMessage('Message 2');
      });

      // Verify state is maintained
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
      expect(result.current.audioQueue).toHaveLength(1);

      // Clear and verify cleanup
      act(() => {
        result.current.clearConversation();
      });

      expect(result.current.audioQueue).toHaveLength(0);
      expect(mockAIService.clearConversationHistory).toHaveBeenCalled();
    });
  });

  describe('Performance Workflows', () => {
    it('should handle high-frequency audio generation', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateAudio.mockResolvedValue(mockAudioBuffer);

      const { result } = renderHook(() => useAudioConversation());

      // Generate audio for multiple messages rapidly
      const messages = Array.from({ length: 10 }, (_, i) => ({
        id: `msg-${i}`,
        content: `Message ${i}`,
        timestamp: new Date(),
        sender: 'nathan' as const,
        emotion: 'neutral' as const
      }));

      const promises = messages.map(msg => 
        act(async () => await result.current.generateAudioForMessage(msg))
      );

      await Promise.all(promises);

      // Should handle all audio generation requests
      expect(mockAIService.generateAudio).toHaveBeenCalledTimes(10);
      
      // Should have queued all audio (or latest due to interruption logic)
      expect(result.current.audioQueue.length).toBeGreaterThan(0);
    });

    it('should handle concurrent conversation operations', async () => {
      const mockAudioBuffer = new ArrayBuffer(1024);
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Concurrent response',
        emotion: 'neutral',
        audio: mockAudioBuffer
      });

      const { result } = renderHook(() => useAudioConversation());

      // Perform concurrent operations
      const operations = [
        act(async () => await result.current.sendMessage('Concurrent 1')),
        act(async () => await result.current.sendMessage('Concurrent 2')),
        act(() => result.current.interruptAudio()),
        act(() => result.current.handleAudioPlayingChange(true)),
        act(() => result.current.handleAudioPlayingChange(false))
      ];

      await Promise.all(operations);

      // Should handle all operations without errors
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
    });
  });
});