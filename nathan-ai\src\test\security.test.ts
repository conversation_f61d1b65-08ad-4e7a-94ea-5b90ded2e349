import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  sanitizeInput,
  validateInput,
  checkApiRateLimit,
  getRemainingApiRequests,
  rateLimiter,
  maskApiKey,
  sanitizeError,
  secureLog,
  securityConfig,
} from '../utils/security';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    isProduction: false,
    isDevelopment: true,
  },
}));

describe('Security Utils', () => {
  beforeEach(() => {
    // Reset rate limiter before each test
    rateLimiter.reset();
    
    // Clear console mocks
    vi.clearAllMocks();
  });

  describe('sanitizeInput', () => {
    it('should sanitize basic input correctly', () => {
      const input = 'Hello world!';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello world!');
    });

    it('should remove HTML tags', () => {
      const input = 'Hello <script>alert("xss")</script> world';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello alert("xss") world');
    });

    it('should remove dangerous protocols', () => {
      const input = 'Click javascript:alert("xss") here';
      const result = sanitizeInput(input);
      expect(result).toBe('Click alert("xss") here');
    });

    it('should handle empty input', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput(null as any)).toBe('');
      expect(sanitizeInput(undefined as any)).toBe('');
    });

    it('should trim and limit length', () => {
      const longInput = 'a'.repeat(3000);
      const result = sanitizeInput(longInput);
      expect(result.length).toBeLessThanOrEqual(securityConfig.validation.maxInputLength);
    });

    it('should handle various dangerous patterns', () => {
      const inputs = [
        'data:text/html,<script>alert(1)</script>',
        'vbscript:msgbox("xss")',
        'onload=alert(1)',
        'onerror=alert(1)',
      ];

      inputs.forEach(input => {
        const result = sanitizeInput(input);
        expect(result).not.toContain('data:');
        expect(result).not.toContain('vbscript:');
        expect(result).not.toContain('javascript:');
      });
    });
  });

  describe('validateInput', () => {
    it('should validate correct input', () => {
      const result = validateInput('Hello world!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty input', () => {
      const result = validateInput('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Input must be a non-empty string');
    });

    it('should reject non-string input', () => {
      const result = validateInput(123 as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Input must be a non-empty string');
    });

    it('should reject input that is too long', () => {
      const longInput = 'a'.repeat(securityConfig.validation.maxInputLength + 1);
      const result = validateInput(longInput);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('exceeds maximum length'))).toBe(true);
    });

    it('should detect XSS patterns', () => {
      const xssInputs = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        'data:text/html,<script>alert(1)</script>',
        'onload=alert(1)',
      ];

      xssInputs.forEach(input => {
        const result = validateInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(error => error.includes('malicious content'))).toBe(true);
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within limits', () => {
      expect(checkApiRateLimit('huggingface')).toBe(true);
      expect(checkApiRateLimit('elevenlabs')).toBe(true);
    });

    it('should track remaining requests correctly', () => {
      // Make a few requests
      checkApiRateLimit('huggingface');
      checkApiRateLimit('huggingface');
      
      const remaining = getRemainingApiRequests('huggingface');
      expect(remaining.perMinute).toBeLessThan(securityConfig.apiRateLimit.maxRequestsPerMinute);
      expect(remaining.perHour).toBeLessThan(securityConfig.apiRateLimit.maxRequestsPerHour);
    });

    it('should enforce rate limits', () => {
      const maxRequests = securityConfig.apiRateLimit.maxRequestsPerMinute;
      
      // Make requests up to the limit
      for (let i = 0; i < maxRequests; i++) {
        expect(checkApiRateLimit('huggingface')).toBe(true);
      }
      
      // Next request should be blocked
      expect(checkApiRateLimit('huggingface')).toBe(false);
    });

    it('should handle different API types separately', () => {
      // Fill up huggingface limit
      const maxRequests = securityConfig.apiRateLimit.maxRequestsPerMinute;
      for (let i = 0; i < maxRequests; i++) {
        checkApiRateLimit('huggingface');
      }
      
      // ElevenLabs should still be available
      expect(checkApiRateLimit('elevenlabs')).toBe(true);
      expect(checkApiRateLimit('huggingface')).toBe(false);
    });
  });

  describe('maskApiKey', () => {
    it('should mask API keys correctly', () => {
      const key = 'hf_1234567890abcdef1234567890abcdef';
      const masked = maskApiKey(key);
      expect(masked).toBe('hf_1************************cdef');
      expect(masked).not.toContain('1234567890abcdef1234567890ab');
    });

    it('should handle short keys', () => {
      const shortKey = 'short';
      const masked = maskApiKey(shortKey);
      expect(masked).toBe('[INVALID]');
    });

    it('should handle empty keys', () => {
      expect(maskApiKey('')).toBe('[INVALID]');
      expect(maskApiKey(null as any)).toBe('[INVALID]');
    });
  });

  describe('sanitizeError', () => {
    beforeEach(() => {
      // Mock production environment for some tests
      vi.doMock('../utils/env', () => ({
        config: {
          isProduction: true,
          isDevelopment: false,
        },
      }));
    });

    it('should sanitize errors in production', () => {
      const error = { status: 401, message: 'Invalid API key: hf_secret123' };
      const sanitized = sanitizeError(error);
      expect(sanitized).toBe('Authentication failed. Please check your API configuration.');
      expect(sanitized).not.toContain('hf_secret123');
    });

    it('should handle rate limit errors', () => {
      const error = { status: 429, message: 'Too many requests' };
      const sanitized = sanitizeError(error);
      expect(sanitized).toBe('Too many requests. Please wait a moment and try again.');
    });

    it('should handle server errors', () => {
      const error = { status: 500, message: 'Internal server error' };
      const sanitized = sanitizeError(error);
      expect(sanitized).toBe('Service temporarily unavailable. Please try again later.');
    });

    it('should provide generic message for unknown errors', () => {
      const error = { status: 418, message: 'I am a teapot' };
      const sanitized = sanitizeError(error);
      expect(sanitized).toBe('An error occurred while processing your request.');
    });
  });

  describe('secureLog', () => {
    beforeEach(() => {
      vi.spyOn(console, 'log').mockImplementation(() => {});
    });

    it('should log messages without sensitive data', () => {
      const data = {
        apiKey: 'hf_secret123',
        authorization: 'Bearer token123',
        message: 'test message',
      };

      secureLog('Test log', data);

      expect(console.log).toHaveBeenCalledWith('Test log', {
        apiKey: 'hf_s************************t123',
        authorization: '[MASKED]',
        message: 'test message',
      });
    });

    it('should log simple messages', () => {
      secureLog('Simple message');
      expect(console.log).toHaveBeenCalledWith('Simple message');
    });

    it('should handle undefined data', () => {
      secureLog('Message with no data', undefined);
      expect(console.log).toHaveBeenCalledWith('Message with no data');
    });
  });

  describe('Security Configuration', () => {
    it('should have appropriate rate limits', () => {
      expect(securityConfig.apiRateLimit.maxRequestsPerMinute).toBeGreaterThan(0);
      expect(securityConfig.apiRateLimit.maxRequestsPerHour).toBeGreaterThan(0);
      expect(securityConfig.apiRateLimit.maxRequestsPerHour).toBeGreaterThan(
        securityConfig.apiRateLimit.maxRequestsPerMinute
      );
    });

    it('should have security headers configured', () => {
      expect(securityConfig.headers.contentSecurityPolicy).toBeTruthy();
      expect(securityConfig.headers.xFrameOptions).toBe('DENY');
      expect(securityConfig.headers.xContentTypeOptions).toBe('nosniff');
      expect(securityConfig.headers.referrerPolicy).toBeTruthy();
    });

    it('should have input validation configured', () => {
      expect(securityConfig.validation.maxInputLength).toBeGreaterThan(0);
      expect(securityConfig.validation.allowedCharacters).toBeInstanceOf(RegExp);
    });
  });
});