import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  savePersonalityPreferences,
  loadPersonalityPreferences,
  clearPersonalityPreferences,
  saveUIPreferences,
  loadUIPreferences,
  clearUIPreferences,
  saveConversationPreferences,
  loadConversationPreferences,
  clearConversationPreferences,
  clearAllPreferences,
  isStorageAvailable,
  getStorageInfo,
} from '../utils/statePersistence';
import type { PersonalityConfig } from '../types/personality';

// Simple localStorage mock
let mockStorage: Record<string, string> = {};

const localStorageMock = {
  getItem: vi.fn((key: string) => mockStorage[key] || null),
  setItem: vi.fn((key: string, value: string) => {
    mockStorage[key] = value;
  }),
  removeItem: vi.fn((key: string) => {
    delete mockStorage[key];
  }),
  clear: vi.fn(() => {
    mockStorage = {};
  }),
  get length() {
    return Object.keys(mockStorage).length;
  },
  key: vi.fn((index: number) => {
    const keys = Object.keys(mockStorage);
    return keys[index] || null;
  }),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('statePersistence', () => {
  beforeEach(() => {
    mockStorage = {};
    vi.clearAllMocks();
  });

  describe('Personality Preferences', () => {
    const mockPersonality: PersonalityConfig = {
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'AI companion',
        hobbies: ['coding', 'music'],
        style: {
          speech: 'casual',
          humor: 'witty',
          depth: 'thoughtful'
        },
        boundaries: {
          avoid: ['politics'],
          safe_topics: ['technology', 'hobbies']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: ['Hello!', 'How are you?']
      },
      version: '1.0.0'
    };

    it('should save personality preferences', () => {
      savePersonalityPreferences(mockPersonality);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'nathan-personality-preferences',
        expect.stringContaining('"name":"Nathan"')
      );
    });

    it('should load personality preferences', () => {
      savePersonalityPreferences(mockPersonality);
      const loaded = loadPersonalityPreferences();

      expect(loaded).toBeDefined();
      expect(loaded?.name).toBe('Nathan');
      expect(loaded?.personality?.tone).toBe('friendly');
    });

    it('should return null when no preferences exist', () => {
      const loaded = loadPersonalityPreferences();
      expect(loaded).toBeNull();
    });

    it('should handle invalid JSON gracefully', () => {
      mockStorage['nathan-personality-preferences'] = 'invalid json';
      const loaded = loadPersonalityPreferences();
      expect(loaded).toBeNull();
    });

    it('should clear personality preferences', () => {
      savePersonalityPreferences(mockPersonality);
      clearPersonalityPreferences();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-personality-preferences');
      expect(loadPersonalityPreferences()).toBeNull();
    });
  });

  describe('UI Preferences', () => {
    it('should save UI preferences', () => {
      saveUIPreferences({ inputMode: 'voice', visualMode: 'minimal' });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'nathan-ui-preferences',
        expect.stringContaining('"inputMode":"voice"')
      );
    });

    it('should load UI preferences with defaults', () => {
      const preferences = loadUIPreferences();

      expect(preferences.inputMode).toBe('text');
      expect(preferences.visualMode).toBe('visual');
      expect(preferences.theme).toBe('auto');
    });

    it('should merge saved preferences with defaults', () => {
      saveUIPreferences({ inputMode: 'voice' });
      const preferences = loadUIPreferences();

      expect(preferences.inputMode).toBe('voice');
      expect(preferences.visualMode).toBe('visual'); // default
      expect(preferences.theme).toBe('auto'); // default
    });

    it('should clear UI preferences', () => {
      saveUIPreferences({ inputMode: 'voice' });
      clearUIPreferences();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-ui-preferences');
      
      const preferences = loadUIPreferences();
      expect(preferences.inputMode).toBe('text'); // back to default
    });
  });

  describe('Conversation Preferences', () => {
    it('should save conversation preferences', () => {
      saveConversationPreferences({ autoPlayAudio: false, messageHistoryLimit: 50 });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'nathan-conversation-preferences',
        expect.stringContaining('"autoPlayAudio":false')
      );
    });

    it('should load conversation preferences with defaults', () => {
      const preferences = loadConversationPreferences();

      expect(preferences.autoPlayAudio).toBe(true);
      expect(preferences.voiceInputEnabled).toBe(true);
      expect(preferences.messageHistoryLimit).toBe(100);
    });

    it('should merge saved preferences with defaults', () => {
      saveConversationPreferences({ autoPlayAudio: false });
      const preferences = loadConversationPreferences();

      expect(preferences.autoPlayAudio).toBe(false);
      expect(preferences.voiceInputEnabled).toBe(true); // default
      expect(preferences.messageHistoryLimit).toBe(100); // default
    });

    it('should clear conversation preferences', () => {
      saveConversationPreferences({ autoPlayAudio: false });
      clearConversationPreferences();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-conversation-preferences');
      
      const preferences = loadConversationPreferences();
      expect(preferences.autoPlayAudio).toBe(true); // back to default
    });
  });

  describe('Utility Functions', () => {
    it('should clear all preferences', () => {
      savePersonalityPreferences({} as PersonalityConfig);
      saveUIPreferences({ inputMode: 'voice' });
      saveConversationPreferences({ autoPlayAudio: false });

      clearAllPreferences();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-personality-preferences');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-ui-preferences');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-conversation-preferences');
    });

    it('should detect storage availability', () => {
      expect(isStorageAvailable()).toBe(true);
    });

    it('should handle storage unavailability', () => {
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage not available');
      });

      expect(isStorageAvailable()).toBe(false);

      localStorageMock.setItem.mockImplementation(originalSetItem);
    });

    it('should get storage info', () => {
      const mockPersonality: PersonalityConfig = {
        name: 'Test',
        pronouns: 'they/them',
        personality: {
          tone: 'test',
          role: 'test',
          hobbies: [],
          style: { speech: 'test', humor: 'test', depth: 'test' },
          boundaries: { avoid: [], safe_topics: [] },
          dynamic_traits: { adaptive_empathy: true, mirroring_style: true, emotionally_available: true }
        },
        conversation_tips: { starter_prompts: [] },
        version: '1.0.0'
      };
      
      savePersonalityPreferences(mockPersonality);
      saveUIPreferences({ inputMode: 'voice' });

      const info = getStorageInfo();

      expect(info.isAvailable).toBe(true);
      expect(info.hasPersonalityPreferences).toBe(true);
      expect(info.hasUIPreferences).toBe(true);
      expect(info.hasConversationPreferences).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors when saving', () => {
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage full');
      });

      expect(() => {
        savePersonalityPreferences({} as PersonalityConfig);
      }).toThrow('Failed to save personality preferences to local storage');

      localStorageMock.setItem.mockImplementation(originalSetItem);
    });

    it('should handle localStorage errors when loading', () => {
      const originalGetItem = localStorageMock.getItem;
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const result = loadPersonalityPreferences();
      expect(result).toBeNull();

      localStorageMock.getItem.mockImplementation(originalGetItem);
    });

    it('should handle invalid personality structure', () => {
      mockStorage['nathan-personality-preferences'] = JSON.stringify({
        invalid: 'structure'
      });

      const result = loadPersonalityPreferences();
      expect(result).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('nathan-personality-preferences');
    });
  });
});