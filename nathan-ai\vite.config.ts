import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Optimize JSX runtime
      jsxRuntime: 'automatic'
    })
  ],
  build: {
    // Optimize bundle size for mobile
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks for better caching
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            return 'vendor';
          }
          
          // AI services chunk
          if (id.includes('/services/') && (
            id.includes('AIService') || 
            id.includes('HuggingFaceClient') || 
            id.includes('ElevenLabsClient')
          )) {
            return 'ai-services';
          }
          
          // Audio components chunk
          if (id.includes('AudioPlayer') || id.includes('useAudioConversation')) {
            return 'audio-components';
          }
          
          // Voice components chunk
          if (id.includes('VoiceInput') || id.includes('BrowserCompatibility')) {
            return 'voice-components';
          }
          
          // Utils chunk
          if (id.includes('/utils/')) {
            return 'utils';
          }
          
          // Components chunk
          if (id.includes('/components/')) {
            return 'components';
          }
        },
        // Optimize chunk names for caching
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name!.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    // Optimize for modern browsers with mobile support
    target: ['es2020', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn']
      }
    },
    // Reduce chunk size warning for mobile
    chunkSizeWarningLimit: 500,
    // Enable source maps in development only
    sourcemap: process.env.NODE_ENV === 'development',
    // Optimize CSS
    cssCodeSplit: true,
    // Enable CSS minification
    cssMinify: true,
    // Optimize assets
    assetsInlineLimit: 4096, // Inline assets smaller than 4KB
    // Enable tree shaking
    treeshake: {
      moduleSideEffects: false,
      propertyReadSideEffects: false,
      tryCatchDeoptimization: false
    }
  },
  // Optimize dev server
  server: {
    host: true, // Allow external connections for mobile testing
    port: 3000,
    // Optimize HMR
    hmr: {
      overlay: true
    },
    // Optimize static file serving
    fs: {
      strict: true
    }
  },
  // Optimize preview server
  preview: {
    port: 3000,
    host: true
  },
  // Environment variables
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    '__DEV__': process.env.NODE_ENV === 'development'
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom'
    ],
    exclude: [
      // Exclude large dependencies that should be loaded on demand
    ]
  },
  // CSS optimization
  css: {
    modules: {
      // Optimize CSS module class names
      generateScopedName: process.env.NODE_ENV === 'production' 
        ? '[hash:base64:5]' 
        : '[name]__[local]__[hash:base64:5]'
    },
    preprocessorOptions: {
      // Add any CSS preprocessor options here
    }
  },
  // Asset optimization
  assetsInclude: ['**/*.woff2', '**/*.woff'],
  // Worker optimization
  worker: {
    format: 'es'
  }
})
