import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import App from '../App';

// Mock the environment validation
vi.mock('../utils/env', () => ({
  validateEnvironment: vi.fn(() => ({
    isValid: true,
    errors: [],
    warnings: [],
  })),
  getEnvVar: vi.fn((key: string, defaultValue?: string) => defaultValue || 'mock-value'),
  config: {
    huggingFace: {
      apiKey: 'mock-hf-key',
      model: 'mock-model',
    },
    elevenLabs: {
      apiKey: 'mock-el-key',
      voiceId: 'mock-voice-id',
    },
    isDevelopment: true,
    isProduction: false,
  },
}));

// Mock the personality loading
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn(() => Promise.resolve({
    name: '<PERSON>',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'companion',
      hobbies: ['coding', 'learning'],
      style: {
        speech: 'casual',
        humor: 'light',
        depth: 'thoughtful',
      },
      boundaries: {
        avoid: ['harmful content'],
        safe_topics: ['technology', 'learning'],
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true,
      },
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How are you?'],
    },
    version: '1.0.0',
  })),
  savePersonality: vi.fn(() => Promise.resolve()),
}));

// Mock all components used in App
vi.mock('../components/ChatInterface', () => ({
  ChatInterface: ({ mode }: { mode: string }) => (
    <div data-testid="chat-interface" data-mode={mode}>
      Chat Interface - {mode} mode
    </div>
  ),
}));

vi.mock('../components/InputController', () => ({
  InputController: ({ inputMode, onModeChange, onMessageSend, isListening }: any) => (
    <div data-testid="input-controller" data-input-mode={inputMode}>
      Input Controller - {inputMode} mode
      <button onClick={() => onModeChange('text')}>Switch to Text</button>
      <button onClick={() => onMessageSend('test message')}>Send Message</button>
    </div>
  ),
}));

vi.mock('../components/AudioPlayer', () => ({
  AudioPlayer: ({ audioQueue, isPlaying, onPlayingChange, onAudioComplete, onAudioError }: any) => (
    <div data-testid="audio-player" data-playing={isPlaying}>
      Audio Player
    </div>
  ),
}));

vi.mock('../hooks/useAudioConversation', () => ({
  useAudioConversation: vi.fn(() => ({
    inputMode: 'text',
    isListening: false,
    isLoading: false,
    isPlaying: false,
    error: null,
    audioQueue: [],
    isGeneratingAudio: false,
    setInputMode: vi.fn(),
    setError: vi.fn(),
    sendMessageWithAudio: vi.fn(),
    interruptAudio: vi.fn(),
    handleAudioPlayingChange: vi.fn(),
    handleAudioComplete: vi.fn(),
    handleAudioError: vi.fn(),
  })),
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock matchMedia for theme detection
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe('App Component', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    // Reset environment validation to success by default
    const { validateEnvironment } = await import('../utils/env');
    vi.mocked(validateEnvironment).mockReturnValue({
      isValid: true,
      errors: [],
      warnings: [],
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading screen initially', async () => {
    render(<App />);
    
    // Should show loading initially (very briefly)
    // Since mocks are synchronous, we need to check for either loading or main interface
    const loadingText = screen.queryByText('Initializing Nathan...');
    const mainInterface = screen.queryByText('Nathan');
    
    // Either loading screen is shown or main interface loads immediately
    expect(loadingText || mainInterface).toBeTruthy();
    
    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });
  });

  it('renders main interface after initialization', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });
    
    expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
    expect(screen.getByLabelText('Theme:')).toBeInTheDocument();
    expect(screen.getByLabelText('Mode:')).toBeInTheDocument();
  });

  it('applies default theme and mode', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('Auto')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Visual')).toBeInTheDocument();
    });
    
    const chatInterface = screen.getByTestId('chat-interface');
    expect(chatInterface).toHaveAttribute('data-mode', 'visual');
  });

  it('loads saved preferences from localStorage', async () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'nathan-theme') return 'dark';
      if (key === 'nathan-visual-mode') return 'minimal';
      return null;
    });

    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('Dark')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Minimal')).toBeInTheDocument();
    });
    
    const chatInterface = screen.getByTestId('chat-interface');
    expect(chatInterface).toHaveAttribute('data-mode', 'minimal');
  });

  it('handles theme changes', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Theme:')).toBeInTheDocument();
    });
    
    const themeSelect = screen.getByLabelText('Theme:');
    fireEvent.change(themeSelect, { target: { value: 'dark' } });
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith('nathan-theme', 'dark');
    expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
  });

  it('handles visual mode changes', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Mode:')).toBeInTheDocument();
    });
    
    const modeSelect = screen.getByLabelText('Mode:');
    fireEvent.change(modeSelect, { target: { value: 'minimal' } });
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith('nathan-visual-mode', 'minimal');
    
    const chatInterface = screen.getByTestId('chat-interface');
    expect(chatInterface).toHaveAttribute('data-mode', 'minimal');
  });

  it('applies auto theme based on system preference', async () => {
    // Mock system preference for dark mode
    window.matchMedia = vi.fn().mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));

    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('Auto')).toBeInTheDocument();
    });
    
    expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
  });

  it('handles initialization errors gracefully', async () => {
    const { validateEnvironment } = await import('../utils/env');
    vi.mocked(validateEnvironment).mockReturnValue({
      isValid: false,
      errors: ['Missing API key'],
      warnings: [],
    });

    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Initialization Failed')).toBeInTheDocument();
      expect(screen.getByText('Environment validation failed: Missing API key')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('provides retry functionality on initialization error', async () => {
    const { validateEnvironment } = await import('../utils/env');
    vi.mocked(validateEnvironment).mockReturnValue({
      isValid: false,
      errors: ['Missing API key'],
      warnings: [],
    });

    // Mock window.location.reload
    const reloadMock = vi.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: reloadMock },
      writable: true,
    });

    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Retry'));
    expect(reloadMock).toHaveBeenCalled();
  });

  it('applies correct CSS classes for visual modes', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Mode:')).toBeInTheDocument();
    });
    
    // Use the CSS module class names
    const appContainer = document.querySelector('[class*="app"]');
    expect(appContainer).toHaveAttribute('class', expect.stringContaining('visual'));
    
    const modeSelect = screen.getByLabelText('Mode:');
    fireEvent.change(modeSelect, { target: { value: 'minimal' } });
    
    expect(appContainer).toHaveAttribute('class', expect.stringContaining('minimal'));
  });

  it('handles personality loading errors', async () => {
    const { loadPersonality } = await import('../utils/personality');
    vi.mocked(loadPersonality).mockRejectedValue(new Error('Failed to load personality'));

    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });
    
    // App should still render even if personality loading fails
    expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });

  it('maintains responsive design classes', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Nathan')).toBeInTheDocument();
    });
    
    // Use CSS module class names
    const header = document.querySelector('[class*="header"]');
    const main = document.querySelector('[class*="main"]');
    
    expect(header).toBeInTheDocument();
    expect(main).toBeInTheDocument();
  });

  it('preserves theme attribute on document element', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Theme:')).toBeInTheDocument();
    });
    
    // Test light theme
    const themeSelect = screen.getByLabelText('Theme:');
    fireEvent.change(themeSelect, { target: { value: 'light' } });
    
    expect(document.documentElement.getAttribute('data-theme')).toBe('light');
    
    // Test dark theme
    fireEvent.change(themeSelect, { target: { value: 'dark' } });
    expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
  });
});