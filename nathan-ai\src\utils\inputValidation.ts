// Input validation utilities
import type { PersonalityConfig } from '../types/personality';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedValue?: any;
}

// Validate user message input
export const validateUserMessage = (message: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Basic validation
  if (!message || typeof message !== 'string') {
    errors.push('Input must be a non-empty string');
    return { isValid: false, errors, warnings };
  }

  // Length validation
  if (message.length > 2000) {
    errors.push('Input exceeds maximum length of 2000 characters');
  }

  // Check for XSS patterns
  const xssPatterns = [
    /<script/i,
    /javascript:/i,
    /data:text\/html/i,
    /vbscript:/i,
    /onload=/i,
    /onerror=/i,
    /onclick=/i,
  ];
  
  for (const pattern of xssPatterns) {
    if (pattern.test(message)) {
      errors.push('Input contains potentially malicious content');
      break;
    }
  }

  // Additional message-specific validation
  if (message && message.length < 2) {
    warnings.push('Message is very short and may not generate meaningful responses');
  }

  if (message && message.length > 1000) {
    warnings.push('Very long messages may be truncated or cause processing delays');
  }

  // Check for repeated characters (potential spam)
  const repeatedCharPattern = /(.)\1{10,}/;
  if (repeatedCharPattern.test(message)) {
    errors.push('Message contains excessive repeated characters');
  }

  // Check for excessive special characters
  const specialCharCount = (message.match(/[^\w\s]/g) || []).length;
  const specialCharRatio = specialCharCount / message.length;
  if (specialCharRatio > 0.5) {
    warnings.push('Message contains many special characters which may affect processing');
  }

  // Simple sanitization
  let sanitizedValue = message.trim();
  sanitizedValue = sanitizedValue.replace(/<[^>]*>/g, ''); // Remove HTML tags
  sanitizedValue = sanitizedValue.replace(/javascript:/gi, ''); // Remove javascript: protocol
  sanitizedValue = sanitizedValue.replace(/data:/gi, ''); // Remove data: protocol
  sanitizedValue = sanitizedValue.replace(/vbscript:/gi, ''); // Remove vbscript: protocol

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    sanitizedValue,
  };
};

// Validate personality configuration
export const validatePersonalityConfig = (config: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!config || typeof config !== 'object') {
    errors.push('Personality configuration must be an object');
    return { isValid: false, errors, warnings };
  }

  // Required fields validation
  const requiredFields = ['name', 'pronouns', 'personality', 'version'];
  for (const field of requiredFields) {
    if (!config[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Name validation
  if (config.name) {
    if (typeof config.name !== 'string') {
      errors.push('Name must be a string');
    } else if (config.name.length < 1 || config.name.length > 50) {
      errors.push('Name must be between 1 and 50 characters');
    } else {
      const sanitizedName = config.name.replace(/<[^>]*>/g, '');
      if (sanitizedName !== config.name) {
        warnings.push('Name contains characters that will be sanitized');
      }
    }
  }

  // Pronouns validation
  if (config.pronouns) {
    if (typeof config.pronouns !== 'string') {
      errors.push('Pronouns must be a string');
    } else if (config.pronouns.length > 20) {
      errors.push('Pronouns must be 20 characters or less');
    }
  }

  // Personality object validation
  if (config.personality) {
    if (typeof config.personality !== 'object') {
      errors.push('Personality must be an object');
    } else {
      const personalityErrors = validatePersonalityObject(config.personality);
      errors.push(...personalityErrors.errors);
      warnings.push(...personalityErrors.warnings);
    }
  }

  // Version validation
  if (config.version) {
    if (typeof config.version !== 'string') {
      errors.push('Version must be a string');
    } else if (!/^\d+\.\d+(\.\d+)?$/.test(config.version)) {
      warnings.push('Version should follow semantic versioning (e.g., "1.0.0")');
    }
  }

  // Conversation tips validation
  if (config.conversation_tips) {
    const tipsErrors = validateConversationTips(config.conversation_tips);
    errors.push(...tipsErrors.errors);
    warnings.push(...tipsErrors.warnings);
  }

  // Create sanitized version
  const sanitizedConfig = sanitizePersonalityConfig(config);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    sanitizedValue: sanitizedConfig,
  };
};

// Validate personality object
const validatePersonalityObject = (personality: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const requiredFields = ['tone', 'role'];
  for (const field of requiredFields) {
    if (!personality[field]) {
      errors.push(`Missing required personality field: ${field}`);
    }
  }

  // Validate string fields
  const stringFields = ['tone', 'role'];
  for (const field of stringFields) {
    if (personality[field] && typeof personality[field] !== 'string') {
      errors.push(`Personality ${field} must be a string`);
    } else if (personality[field] && personality[field].length > 200) {
      errors.push(`Personality ${field} must be 200 characters or less`);
    }
  }

  // Validate hobbies array
  if (personality.hobbies) {
    if (!Array.isArray(personality.hobbies)) {
      errors.push('Hobbies must be an array');
    } else {
      if (personality.hobbies.length > 20) {
        warnings.push('Too many hobbies may affect response quality');
      }
      
      for (let i = 0; i < personality.hobbies.length; i++) {
        const hobby = personality.hobbies[i];
        if (typeof hobby !== 'string') {
          errors.push(`Hobby at index ${i} must be a string`);
        } else if (hobby.length > 50) {
          errors.push(`Hobby at index ${i} must be 50 characters or less`);
        }
      }
    }
  }

  // Validate style object
  if (personality.style) {
    const styleErrors = validateStyleObject(personality.style);
    errors.push(...styleErrors.errors);
    warnings.push(...styleErrors.warnings);
  }

  // Validate boundaries object
  if (personality.boundaries) {
    const boundariesErrors = validateBoundariesObject(personality.boundaries);
    errors.push(...boundariesErrors.errors);
    warnings.push(...boundariesErrors.warnings);
  }

  // Validate dynamic traits
  if (personality.dynamic_traits) {
    const traitsErrors = validateDynamicTraits(personality.dynamic_traits);
    errors.push(...traitsErrors.errors);
    warnings.push(...traitsErrors.warnings);
  }

  return { isValid: errors.length === 0, errors, warnings };
};

// Validate style object
const validateStyleObject = (style: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const stringFields = ['speech', 'humor', 'depth'];
  for (const field of stringFields) {
    if (style[field] && typeof style[field] !== 'string') {
      errors.push(`Style ${field} must be a string`);
    } else if (style[field] && style[field].length > 100) {
      errors.push(`Style ${field} must be 100 characters or less`);
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
};

// Validate boundaries object
const validateBoundariesObject = (boundaries: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const arrayFields = ['avoid', 'safe_topics'];
  for (const field of arrayFields) {
    if (boundaries[field]) {
      if (!Array.isArray(boundaries[field])) {
        errors.push(`Boundaries ${field} must be an array`);
      } else {
        if (boundaries[field].length > 50) {
          warnings.push(`Too many items in boundaries ${field} may affect performance`);
        }
        
        for (let i = 0; i < boundaries[field].length; i++) {
          const item = boundaries[field][i];
          if (typeof item !== 'string') {
            errors.push(`Boundaries ${field} item at index ${i} must be a string`);
          } else if (item.length > 100) {
            errors.push(`Boundaries ${field} item at index ${i} must be 100 characters or less`);
          }
        }
      }
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
};

// Validate dynamic traits
const validateDynamicTraits = (traits: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const booleanFields = ['adaptive_empathy', 'mirroring_style', 'emotionally_available'];
  for (const field of booleanFields) {
    if (traits[field] !== undefined && typeof traits[field] !== 'boolean') {
      errors.push(`Dynamic trait ${field} must be a boolean`);
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
};

// Validate conversation tips
const validateConversationTips = (tips: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (tips.starter_prompts) {
    if (!Array.isArray(tips.starter_prompts)) {
      errors.push('Starter prompts must be an array');
    } else {
      if (tips.starter_prompts.length > 20) {
        warnings.push('Too many starter prompts may affect performance');
      }
      
      for (let i = 0; i < tips.starter_prompts.length; i++) {
        const prompt = tips.starter_prompts[i];
        if (typeof prompt !== 'string') {
          errors.push(`Starter prompt at index ${i} must be a string`);
        } else if (prompt.length > 200) {
          errors.push(`Starter prompt at index ${i} must be 200 characters or less`);
        }
      }
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
};

// Simple sanitization function
const simpleSanitize = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  let sanitized = input.trim();
  sanitized = sanitized.replace(/<[^>]*>/g, ''); // Remove HTML tags
  sanitized = sanitized.replace(/javascript:/gi, ''); // Remove javascript: protocol
  sanitized = sanitized.replace(/data:/gi, ''); // Remove data: protocol
  sanitized = sanitized.replace(/vbscript:/gi, ''); // Remove vbscript: protocol
  return sanitized;
};

// Sanitize personality configuration
const sanitizePersonalityConfig = (config: any): PersonalityConfig => {
  const sanitized: any = {};

  // Sanitize basic fields
  if (config.name) {
    sanitized.name = simpleSanitize(config.name);
  }
  
  if (config.pronouns) {
    sanitized.pronouns = simpleSanitize(config.pronouns);
  }

  if (config.version) {
    sanitized.version = simpleSanitize(config.version);
  }

  // Sanitize personality object
  if (config.personality) {
    sanitized.personality = {
      tone: config.personality.tone ? simpleSanitize(config.personality.tone) : '',
      role: config.personality.role ? simpleSanitize(config.personality.role) : '',
      hobbies: Array.isArray(config.personality.hobbies) 
        ? config.personality.hobbies.map((hobby: string) => simpleSanitize(hobby)).filter(Boolean)
        : [],
      style: {
        speech: config.personality.style?.speech ? simpleSanitize(config.personality.style.speech) : '',
        humor: config.personality.style?.humor ? simpleSanitize(config.personality.style.humor) : '',
        depth: config.personality.style?.depth ? simpleSanitize(config.personality.style.depth) : '',
      },
      boundaries: {
        avoid: Array.isArray(config.personality.boundaries?.avoid)
          ? config.personality.boundaries.avoid.map((item: string) => simpleSanitize(item)).filter(Boolean)
          : [],
        safe_topics: Array.isArray(config.personality.boundaries?.safe_topics)
          ? config.personality.boundaries.safe_topics.map((item: string) => simpleSanitize(item)).filter(Boolean)
          : [],
      },
      dynamic_traits: {
        adaptive_empathy: Boolean(config.personality.dynamic_traits?.adaptive_empathy),
        mirroring_style: Boolean(config.personality.dynamic_traits?.mirroring_style),
        emotionally_available: Boolean(config.personality.dynamic_traits?.emotionally_available),
      },
    };
  }

  // Sanitize conversation tips
  if (config.conversation_tips) {
    sanitized.conversation_tips = {
      starter_prompts: Array.isArray(config.conversation_tips.starter_prompts)
        ? config.conversation_tips.starter_prompts.map((prompt: string) => simpleSanitize(prompt)).filter(Boolean)
        : [],
    };
  }

  return sanitized as PersonalityConfig;
};

// Validate file upload (for personality files)
export const validateFileUpload = (file: File): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // File type validation
  if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
    errors.push('File must be a JSON file');
  }

  // File size validation (limit to 1MB)
  const maxSize = 1024 * 1024; // 1MB
  if (file.size > maxSize) {
    errors.push('File size must be less than 1MB');
  }

  // File name validation
  if (file.name.length > 100) {
    warnings.push('File name is very long');
  }

  const sanitizedName = simpleSanitize(file.name);
  if (sanitizedName !== file.name) {
    warnings.push('File name contains characters that will be sanitized');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    sanitizedValue: sanitizedName,
  };
};