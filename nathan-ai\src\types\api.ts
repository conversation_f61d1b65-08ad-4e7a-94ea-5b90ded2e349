// Hugging Face API Types
export interface GenerationOptions {
  max_length?: number;
  temperature?: number;
  top_p?: number;
  do_sample?: boolean;
}

export interface HuggingFaceResponse {
  generated_text: string;
}

export interface HuggingFaceRequest {
  inputs: string;
  parameters?: GenerationOptions;
}

// Eleven Labs API Types
export interface TTSOptions {
  voice_id?: string;
  model_id?: string;
  voice_settings?: {
    stability: number;
    similarity_boost: number;
  };
}

export interface ElevenLabsRequest {
  text: string;
  model_id?: string;
  voice_settings?: {
    stability: number;
    similarity_boost: number;
  };
}

export interface ElevenLabsResponse {
  audio: ArrayBuffer;
}

// Error Types
export class HFError extends Error {
  public status?: number;
  
  constructor(message: string, status?: number) {
    super(message);
    this.name = 'HFError';
    this.status = status;
  }
}

export class ELError extends Error {
  public status?: number;
  public detail?: {
    status: string;
    message: string;
  };
  
  constructor(message: string, status?: number, detail?: { status: string; message: string }) {
    super(message);
    this.name = 'ELError';
    this.status = status;
    this.detail = detail;
  }
}

export interface NetworkError {
  message: string;
  code?: string;
}

export type ErrorType = 'api' | 'network' | 'validation' | 'browser' | 'unknown';

export interface ApiErrorHandler {
  handleHuggingFaceError(error: HFError): void;
  handleElevenLabsError(error: ELError): void;
  handleNetworkError(error: NetworkError): void;
  showUserFriendlyMessage(errorType: ErrorType): void;
}
