import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';

// Mock React DOM
const mockRender = vi.fn();
vi.mock('react-dom/client', () => ({
  createRoot: vi.fn(() => ({
    render: mockRender,
  })),
}));

// Mock App component
vi.mock('../App', () => ({
  default: () => <div data-testid="app">App Component</div>,
}));

// Mock CSS imports
vi.mock('../index.css', () => ({}));

describe('main.tsx', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock document.getElementById
    const mockElement = document.createElement('div');
    mockElement.id = 'root';
    vi.spyOn(document, 'getElementById').mockReturnValue(mockElement);
  });

  it('renders App component to root element', async () => {
    // Import main to trigger the rendering
    await import('../main');

    expect(document.getElementById).toHaveBeenCalledWith('root');
    expect(mockRender).toHaveBeenCalled();
  });

  it('handles missing root element gracefully', async () => {
    vi.spyOn(document, 'getElementById').mockReturnValue(null);
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    // This should not throw an error
    await expect(import('../main')).resolves.toBeDefined();

    consoleSpy.mockRestore();
  });
});