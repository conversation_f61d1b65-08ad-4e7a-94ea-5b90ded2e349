import React, { useState, useCallback } from 'react';
import type { InputControllerProps } from '../types/components';
import { TextInput } from './TextInput';
import { VoiceInput } from './VoiceInput';
import { FeatureErrorBoundary } from './FeatureErrorBoundary';
import styles from './InputController.module.css';

export const InputController: React.FC<InputControllerProps> = ({
  inputMode,
  onModeChange,
  onMessageSend,
  isListening
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleMessageSend = useCallback(async (message: string) => {
    if (!message.trim()) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onMessageSend(message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  }, [onMessageSend]);

  const handleVoiceTranscript = useCallback((transcript: string) => {
    if (transcript.trim()) {
      handleMessageSend(transcript);
    }
  }, [handleMessageSend]);

  const handleVoiceListeningChange = useCallback((listening: boolean) => {
    // This is handled by the parent component through isListening prop
  }, []);

  const handleVoiceError = useCallback((errorMessage: string) => {
    setError(errorMessage);
  }, []);

  const handleModeToggle = useCallback(() => {
    const newMode = inputMode === 'voice' ? 'text' : 'voice';
    onModeChange(newMode);
    setError(null); // Clear any previous errors when switching modes
  }, [inputMode, onModeChange]);

  return (
    <div className={styles.inputController}>
      {/* Mode Toggle */}
      <div className={styles.modeToggle}>
        <button
          onClick={handleModeToggle}
          className={`${styles.toggleButton} ${styles[inputMode]}`}
          aria-label={`Switch to ${inputMode === 'voice' ? 'text' : 'voice'} input`}
        >
          <div className={styles.toggleTrack}>
            <div className={styles.toggleThumb}>
              {inputMode === 'voice' ? (
                <svg className={styles.toggleIcon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                  <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                </svg>
              ) : (
                <svg className={styles.toggleIcon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
              )}
            </div>
          </div>
          <span className={styles.toggleLabel}>
            {inputMode === 'voice' ? 'Voice' : 'Text'}
          </span>
        </button>
      </div>

      {/* Input Area */}
      <div className={styles.inputArea}>
        {inputMode === 'voice' ? (
          <FeatureErrorBoundary 
            featureName="Voice Input"
            onError={(error, errorInfo) => {
              console.error('Voice input feature error:', error, errorInfo);
              setError('Voice input is temporarily unavailable. Please try text input.');
            }}
          >
            <VoiceInput
              onTranscript={handleVoiceTranscript}
              onListeningChange={handleVoiceListeningChange}
              onError={handleVoiceError}
              isActive={!isLoading}
            />
          </FeatureErrorBoundary>
        ) : (
          <TextInput
            onMessageSend={handleMessageSend}
            isLoading={isLoading}
            placeholder="Type your message to Nathan..."
          />
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className={styles.errorDisplay}>
          <div className={styles.errorMessage}>
            <svg className={styles.errorIcon} viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className={styles.errorDismiss}
              aria-label="Dismiss error"
            >
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Status Indicators */}
      <div className={styles.statusBar}>
        <div className={styles.statusIndicators}>
          {isLoading && (
            <div className={styles.loadingIndicator}>
              <div className={styles.spinner} />
              <span>Processing...</span>
            </div>
          )}
          
          {inputMode === 'voice' && isListening && (
            <div className={styles.listeningIndicator}>
              <div className={styles.pulseIndicator} />
              <span>Listening...</span>
            </div>
          )}
        </div>

        <div className={styles.modeIndicator}>
          <span className={styles.modeLabel}>
            {inputMode === 'voice' ? '🎤' : '⌨️'} {inputMode.charAt(0).toUpperCase() + inputMode.slice(1)} Mode
          </span>
        </div>
      </div>
    </div>
  );
};