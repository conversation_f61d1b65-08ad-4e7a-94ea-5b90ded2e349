import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useBrowserCompatibility } from '../hooks/useBrowserCompatibility';

// Mock BrowserCompatibility service
vi.mock('../services/BrowserCompatibility', () => ({
  BrowserCompatibility: {
    checkWebSpeechSupport: vi.fn(),
    checkAudioSupport: vi.fn(),
    checkLocalStorageSupport: vi.fn(),
    checkServiceWorkerSupport: vi.fn(),
    getBrowserInfo: vi.fn(),
    getCompatibilityReport: vi.fn(),
    isFeatureSupported: vi.fn(),
  },
}));

import { BrowserCompatibility } from '../services/BrowserCompatibility';

describe('useBrowserCompatibility', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    vi.mocked(BrowserCompatibility.checkWebSpeechSupport).mockReturnValue({
      isSupported: true,
      hasRecognition: true,
      hasSynthesis: true,
      vendor: 'webkit',
    });

    vi.mocked(BrowserCompatibility.checkAudioSupport).mockReturnValue({
      isSupported: true,
      supportedFormats: ['mp3', 'wav', 'ogg'],
      hasWebAudio: true,
      hasAudioContext: true,
    });

    vi.mocked(BrowserCompatibility.checkLocalStorageSupport).mockReturnValue({
      isSupported: true,
      isAvailable: true,
      quota: 10485760,
    });

    vi.mocked(BrowserCompatibility.checkServiceWorkerSupport).mockReturnValue({
      isSupported: true,
      isSecureContext: true,
    });

    vi.mocked(BrowserCompatibility.getBrowserInfo).mockReturnValue({
      name: 'Chrome',
      version: '91.0.4472.124',
      engine: 'Blink',
      os: 'Windows',
      isMobile: false,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    });

    vi.mocked(BrowserCompatibility.getCompatibilityReport).mockReturnValue({
      overallScore: 95,
      criticalIssues: [],
      warnings: [],
      recommendations: [],
      supportedFeatures: ['webSpeech', 'audio', 'localStorage', 'serviceWorker'],
      unsupportedFeatures: [],
    });

    vi.mocked(BrowserCompatibility.isFeatureSupported).mockImplementation((feature) => {
      const supportedFeatures = ['webSpeech', 'audio', 'localStorage', 'serviceWorker'];
      return supportedFeatures.includes(feature);
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('initializes with compatibility data', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.webSpeech.isSupported).toBe(true);
      expect(result.current.audio.isSupported).toBe(true);
      expect(result.current.localStorage.isSupported).toBe(true);
      expect(result.current.serviceWorker.isSupported).toBe(true);
      expect(result.current.browserInfo.name).toBe('Chrome');
      expect(result.current.compatibilityReport.overallScore).toBe(95);
    });

    it('calls all compatibility check methods', () => {
      renderHook(() => useBrowserCompatibility());

      expect(BrowserCompatibility.checkWebSpeechSupport).toHaveBeenCalled();
      expect(BrowserCompatibility.checkAudioSupport).toHaveBeenCalled();
      expect(BrowserCompatibility.checkLocalStorageSupport).toHaveBeenCalled();
      expect(BrowserCompatibility.checkServiceWorkerSupport).toHaveBeenCalled();
      expect(BrowserCompatibility.getBrowserInfo).toHaveBeenCalled();
      expect(BrowserCompatibility.getCompatibilityReport).toHaveBeenCalled();
    });
  });

  describe('feature support checking', () => {
    it('checks if feature is supported', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      const isSupported = result.current.isFeatureSupported('webSpeech');
      expect(isSupported).toBe(true);
      expect(BrowserCompatibility.isFeatureSupported).toHaveBeenCalledWith('webSpeech');
    });

    it('returns false for unsupported features', () => {
      vi.mocked(BrowserCompatibility.isFeatureSupported).mockReturnValue(false);

      const { result } = renderHook(() => useBrowserCompatibility());

      const isSupported = result.current.isFeatureSupported('unsupportedFeature');
      expect(isSupported).toBe(false);
    });
  });

  describe('refresh functionality', () => {
    it('refreshes compatibility data', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      act(() => {
        result.current.refresh();
      });

      // Should call all check methods again
      expect(BrowserCompatibility.checkWebSpeechSupport).toHaveBeenCalledTimes(2);
      expect(BrowserCompatibility.checkAudioSupport).toHaveBeenCalledTimes(2);
      expect(BrowserCompatibility.checkLocalStorageSupport).toHaveBeenCalledTimes(2);
      expect(BrowserCompatibility.checkServiceWorkerSupport).toHaveBeenCalledTimes(2);
      expect(BrowserCompatibility.getBrowserInfo).toHaveBeenCalledTimes(2);
      expect(BrowserCompatibility.getCompatibilityReport).toHaveBeenCalledTimes(2);
    });

    it('updates state after refresh', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      // Change mock return value
      vi.mocked(BrowserCompatibility.getBrowserInfo).mockReturnValue({
        name: 'Firefox',
        version: '89.0',
        engine: 'Gecko',
        os: 'Windows',
        isMobile: false,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      });

      act(() => {
        result.current.refresh();
      });

      expect(result.current.browserInfo.name).toBe('Firefox');
    });
  });

  describe('unsupported browser scenarios', () => {
    it('handles browser with no Web Speech support', () => {
      vi.mocked(BrowserCompatibility.checkWebSpeechSupport).mockReturnValue({
        isSupported: false,
        hasRecognition: false,
        hasSynthesis: false,
        vendor: null,
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.webSpeech.isSupported).toBe(false);
      expect(result.current.webSpeech.hasRecognition).toBe(false);
      expect(result.current.webSpeech.hasSynthesis).toBe(false);
    });

    it('handles browser with limited audio support', () => {
      vi.mocked(BrowserCompatibility.checkAudioSupport).mockReturnValue({
        isSupported: true,
        supportedFormats: ['mp3'],
        hasWebAudio: false,
        hasAudioContext: false,
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.audio.isSupported).toBe(true);
      expect(result.current.audio.supportedFormats).toEqual(['mp3']);
      expect(result.current.audio.hasWebAudio).toBe(false);
    });

    it('handles browser with no localStorage support', () => {
      vi.mocked(BrowserCompatibility.checkLocalStorageSupport).mockReturnValue({
        isSupported: false,
        isAvailable: false,
        quota: 0,
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.localStorage.isSupported).toBe(false);
      expect(result.current.localStorage.isAvailable).toBe(false);
    });

    it('handles browser with no service worker support', () => {
      vi.mocked(BrowserCompatibility.checkServiceWorkerSupport).mockReturnValue({
        isSupported: false,
        isSecureContext: false,
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.serviceWorker.isSupported).toBe(false);
      expect(result.current.serviceWorker.isSecureContext).toBe(false);
    });
  });

  describe('compatibility report', () => {
    it('provides compatibility report with issues', () => {
      vi.mocked(BrowserCompatibility.getCompatibilityReport).mockReturnValue({
        overallScore: 60,
        criticalIssues: ['Web Speech API not supported'],
        warnings: ['Limited audio format support'],
        recommendations: ['Update to a modern browser'],
        supportedFeatures: ['localStorage'],
        unsupportedFeatures: ['webSpeech', 'serviceWorker'],
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.compatibilityReport.overallScore).toBe(60);
      expect(result.current.compatibilityReport.criticalIssues).toContain('Web Speech API not supported');
      expect(result.current.compatibilityReport.warnings).toContain('Limited audio format support');
      expect(result.current.compatibilityReport.recommendations).toContain('Update to a modern browser');
    });

    it('identifies supported and unsupported features', () => {
      vi.mocked(BrowserCompatibility.getCompatibilityReport).mockReturnValue({
        overallScore: 75,
        criticalIssues: [],
        warnings: [],
        recommendations: [],
        supportedFeatures: ['audio', 'localStorage'],
        unsupportedFeatures: ['webSpeech', 'serviceWorker'],
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.compatibilityReport.supportedFeatures).toEqual(['audio', 'localStorage']);
      expect(result.current.compatibilityReport.unsupportedFeatures).toEqual(['webSpeech', 'serviceWorker']);
    });
  });

  describe('browser information', () => {
    it('provides detailed browser information', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.browserInfo).toEqual({
        name: 'Chrome',
        version: '91.0.4472.124',
        engine: 'Blink',
        os: 'Windows',
        isMobile: false,
        userAgent: expect.any(String),
      });
    });

    it('detects mobile browsers', () => {
      vi.mocked(BrowserCompatibility.getBrowserInfo).mockReturnValue({
        name: 'Safari',
        version: '14.0',
        engine: 'WebKit',
        os: 'iOS',
        isMobile: true,
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.browserInfo.isMobile).toBe(true);
      expect(result.current.browserInfo.os).toBe('iOS');
    });
  });

  describe('error handling', () => {
    it('handles errors in compatibility checks gracefully', () => {
      vi.mocked(BrowserCompatibility.checkWebSpeechSupport).mockImplementation(() => {
        throw new Error('Check failed');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useBrowserCompatibility());

      // Should not crash and should provide fallback values
      expect(result.current.webSpeech.isSupported).toBe(false);
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('handles errors in refresh gracefully', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      vi.mocked(BrowserCompatibility.getBrowserInfo).mockImplementation(() => {
        throw new Error('Refresh failed');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      act(() => {
        result.current.refresh();
      });

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('performance', () => {
    it('does not re-run checks unnecessarily', () => {
      const { rerender } = renderHook(() => useBrowserCompatibility());

      const initialCallCount = vi.mocked(BrowserCompatibility.checkWebSpeechSupport).mock.calls.length;

      rerender();

      expect(vi.mocked(BrowserCompatibility.checkWebSpeechSupport)).toHaveBeenCalledTimes(initialCallCount);
    });

    it('memoizes feature support checks', () => {
      const { result } = renderHook(() => useBrowserCompatibility());

      result.current.isFeatureSupported('webSpeech');
      result.current.isFeatureSupported('webSpeech');

      expect(BrowserCompatibility.isFeatureSupported).toHaveBeenCalledTimes(2);
    });
  });

  describe('edge cases', () => {
    it('handles undefined compatibility data', () => {
      vi.mocked(BrowserCompatibility.checkWebSpeechSupport).mockReturnValue(undefined as any);

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.webSpeech.isSupported).toBe(false);
    });

    it('handles null browser info', () => {
      vi.mocked(BrowserCompatibility.getBrowserInfo).mockReturnValue(null as any);

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.browserInfo.name).toBe('Unknown');
    });

    it('handles empty compatibility report', () => {
      vi.mocked(BrowserCompatibility.getCompatibilityReport).mockReturnValue({
        overallScore: 0,
        criticalIssues: [],
        warnings: [],
        recommendations: [],
        supportedFeatures: [],
        unsupportedFeatures: [],
      });

      const { result } = renderHook(() => useBrowserCompatibility());

      expect(result.current.compatibilityReport.overallScore).toBe(0);
      expect(result.current.compatibilityReport.supportedFeatures).toEqual([]);
    });
  });
});