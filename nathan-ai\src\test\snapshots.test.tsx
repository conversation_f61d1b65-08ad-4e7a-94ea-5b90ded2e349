import React from 'react';
import { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { MessageBubble } from '../components/MessageBubble';
import { Avatar } from '../components/Avatar';
import { TextInput } from '../components/TextInput';
import { VoiceInput } from '../components/VoiceInput';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { CompatibilityWarning } from '../components/CompatibilityWarning';
import type { Message } from '../types/message';
import type { PersonalityConfig } from '../types/personality';

// Mock personality config
const mockPersonality: PersonalityConfig = {
  name: '<PERSON>',
  pronouns: 'he/him',
  personality: {
    tone: 'friendly',
    role: 'companion',
    hobbies: ['coding', 'music'],
    style: {
      speech: 'casual',
      humor: 'light',
      depth: 'thoughtful'
    },
    boundaries: {
      avoid: ['harmful content'],
      safe_topics: ['technology', 'music']
    },
    dynamic_traits: {
      adaptive_empathy: true,
      mirroring_style: true,
      emotionally_available: true
    }
  },
  conversation_tips: {
    starter_prompts: ['Hello!', 'How are you?']
  },
  version: '1.0.0'
};

const mockMessage: Message = {
  id: '1',
  content: 'Hello there!',
  timestamp: new Date('2024-01-01T12:00:00Z'),
  sender: 'nathan',
};

describe('Component Snapshots', () => {
  describe('MessageBubble snapshots', () => {
    it('renders Nathan message bubble correctly', () => {
      const { container } = render(
        <MessageBubble
          message={mockMessage}
          isUser={false}
          showAvatar={true}
          personality={mockPersonality}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders user message bubble correctly', () => {
      const userMessage = { ...mockMessage, sender: 'user' as const };
      const { container } = render(
        <MessageBubble
          message={userMessage}
          isUser={true}
          showAvatar={true}
          personality={mockPersonality}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders message with emotion correctly', () => {
      const emotionalMessage = { ...mockMessage, emotion: 'happy' as const };
      const { container } = render(
        <MessageBubble
          message={emotionalMessage}
          isUser={false}
          showAvatar={true}
          personality={mockPersonality}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders message with audio correctly', () => {
      const audioMessage = { ...mockMessage, audioUrl: 'https://example.com/audio.mp3' };
      const { container } = render(
        <MessageBubble
          message={audioMessage}
          isUser={false}
          showAvatar={true}
          personality={mockPersonality}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders minimal mode message correctly', () => {
      const { container } = render(
        <MessageBubble
          message={mockMessage}
          isUser={false}
          showAvatar={false}
          personality={mockPersonality}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('Avatar snapshots', () => {
    it('renders neutral avatar correctly', () => {
      const { container } = render(
        <Avatar
          emotion="neutral"
          isAnimated={false}
          size="medium"
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders happy avatar correctly', () => {
      const { container } = render(
        <Avatar
          emotion="happy"
          isAnimated={true}
          size="large"
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders thoughtful avatar correctly', () => {
      const { container } = render(
        <Avatar
          emotion="thoughtful"
          isAnimated={true}
          size="small"
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders empathetic avatar correctly', () => {
      const { container } = render(
        <Avatar
          emotion="empathetic"
          isAnimated={false}
          size="medium"
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('TextInput snapshots', () => {
    it('renders default text input correctly', () => {
      const { container } = render(
        <TextInput
          value=""
          onChange={vi.fn()}
          onSend={vi.fn()}
          placeholder="Type a message..."
          disabled={false}
          isLoading={false}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders text input with value correctly', () => {
      const { container } = render(
        <TextInput
          value="Hello Nathan!"
          onChange={vi.fn()}
          onSend={vi.fn()}
          placeholder="Type a message..."
          disabled={false}
          isLoading={false}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders disabled text input correctly', () => {
      const { container } = render(
        <TextInput
          value=""
          onChange={vi.fn()}
          onSend={vi.fn()}
          placeholder="Type a message..."
          disabled={true}
          isLoading={false}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders loading text input correctly', () => {
      const { container } = render(
        <TextInput
          value=""
          onChange={vi.fn()}
          onSend={vi.fn()}
          placeholder="Type a message..."
          disabled={false}
          isLoading={true}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('VoiceInput snapshots', () => {
    it('renders inactive voice input correctly', () => {
      const { container } = render(
        <VoiceInput
          isListening={false}
          isSupported={true}
          transcript=""
          onStart={vi.fn()}
          onStop={vi.fn()}
          onTranscript={vi.fn()}
          onError={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders listening voice input correctly', () => {
      const { container } = render(
        <VoiceInput
          isListening={true}
          isSupported={true}
          transcript=""
          onStart={vi.fn()}
          onStop={vi.fn()}
          onTranscript={vi.fn()}
          onError={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders voice input with transcript correctly', () => {
      const { container } = render(
        <VoiceInput
          isListening={false}
          isSupported={true}
          transcript="Hello Nathan, how are you?"
          onStart={vi.fn()}
          onStop={vi.fn()}
          onTranscript={vi.fn()}
          onError={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders unsupported voice input correctly', () => {
      const { container } = render(
        <VoiceInput
          isListening={false}
          isSupported={false}
          transcript=""
          onStart={vi.fn()}
          onStop={vi.fn()}
          onTranscript={vi.fn()}
          onError={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('ErrorBoundary snapshots', () => {
    it('renders error boundary fallback correctly', () => {
      const ThrowError = () => {
        throw new Error('Test error');
      };

      const { container } = render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('CompatibilityWarning snapshots', () => {
    it('renders compatibility warning correctly', () => {
      const { container } = render(
        <CompatibilityWarning
          isVisible={true}
          warnings={['Web Speech API not supported', 'Audio API limited']}
          onDismiss={vi.fn()}
          onLearnMore={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders hidden compatibility warning correctly', () => {
      const { container } = render(
        <CompatibilityWarning
          isVisible={false}
          warnings={[]}
          onDismiss={vi.fn()}
          onLearnMore={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders single warning correctly', () => {
      const { container } = render(
        <CompatibilityWarning
          isVisible={true}
          warnings={['Service Worker not supported']}
          onDismiss={vi.fn()}
          onLearnMore={vi.fn()}
        />
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('Responsive snapshots', () => {
    it('renders components in mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { container } = render(
        <div className="mobile-viewport">
          <MessageBubble
            message={mockMessage}
            isUser={false}
            showAvatar={true}
            personality={mockPersonality}
          />
          <TextInput
            value=""
            onChange={vi.fn()}
            onSend={vi.fn()}
            placeholder="Type a message..."
            disabled={false}
            isLoading={false}
          />
        </div>
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders components in tablet viewport', () => {
      // Mock tablet viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      const { container } = render(
        <div className="tablet-viewport">
          <MessageBubble
            message={mockMessage}
            isUser={false}
            showAvatar={true}
            personality={mockPersonality}
          />
          <Avatar
            emotion="happy"
            isAnimated={true}
            size="large"
          />
        </div>
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders components in desktop viewport', () => {
      // Mock desktop viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });

      const { container } = render(
        <div className="desktop-viewport">
          <MessageBubble
            message={mockMessage}
            isUser={false}
            showAvatar={true}
            personality={mockPersonality}
          />
          <VoiceInput
            isListening={false}
            isSupported={true}
            transcript=""
            onStart={vi.fn()}
            onStop={vi.fn()}
            onTranscript={vi.fn()}
            onError={vi.fn()}
          />
        </div>
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });

  describe('Theme snapshots', () => {
    it('renders components in light theme', () => {
      document.documentElement.setAttribute('data-theme', 'light');

      const { container } = render(
        <div className="light-theme">
          <MessageBubble
            message={mockMessage}
            isUser={false}
            showAvatar={true}
            personality={mockPersonality}
          />
          <Avatar
            emotion="neutral"
            isAnimated={false}
            size="medium"
          />
        </div>
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders components in dark theme', () => {
      document.documentElement.setAttribute('data-theme', 'dark');

      const { container } = render(
        <div className="dark-theme">
          <MessageBubble
            message={mockMessage}
            isUser={false}
            showAvatar={true}
            personality={mockPersonality}
          />
          <TextInput
            value="Hello!"
            onChange={vi.fn()}
            onSend={vi.fn()}
            placeholder="Type a message..."
            disabled={false}
            isLoading={false}
          />
        </div>
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });
});