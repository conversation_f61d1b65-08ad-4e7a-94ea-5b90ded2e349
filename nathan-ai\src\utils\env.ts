// Environment variable utilities with security enhancements
export const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = import.meta.env[key];
  if (!value && !defaultValue) {
    // Don't expose the actual key name in production for security
    const keyName = import.meta.env.PROD ? '[REDACTED]' : key;
    throw new Error(`Required environment variable ${keyName} is not configured`);
  }
  return value || defaultValue || '';
};

// Secure environment variable getter that validates format
export const getSecureEnvVar = (key: string, validator?: (value: string) => boolean): string => {
  const value = getEnvVar(key);
  
  // Basic security checks
  if (value.includes(' ') || value.includes('\n') || value.includes('\t')) {
    throw new Error(`Invalid format for environment variable ${import.meta.env.PROD ? '[REDACTED]' : key}`);
  }
  
  // Custom validation if provided
  if (validator && !validator(value)) {
    throw new Error(`Validation failed for environment variable ${import.meta.env.PROD ? '[REDACTED]' : key}`);
  }
  
  return value;
};

// Environment validation result type
export interface EnvironmentValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  securityIssues: string[];
}

// API key validators
const validateHuggingFaceKey = (key: string): boolean => {
  return key.startsWith('hf_') && key.length >= 37; // HF keys are typically 37+ chars
};

const validateElevenLabsKey = (key: string): boolean => {
  return key.length >= 32 && /^[a-f0-9]+$/i.test(key); // EL keys are hex strings
};

const validateVoiceId = (voiceId: string): boolean => {
  return voiceId.length >= 20 && /^[a-zA-Z0-9_-]+$/.test(voiceId);
};

// Validate environment configuration with enhanced security checks
export const validateEnvironment = (): EnvironmentValidation => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const securityIssues: string[] = [];

  // Required environment variables with validators
  const requiredVars = [
    { name: 'VITE_HUGGING_FACE_API_KEY', validator: validateHuggingFaceKey },
    { name: 'VITE_ELEVEN_LABS_API_KEY', validator: validateElevenLabsKey },
    { name: 'VITE_ELEVEN_LABS_VOICE_ID', validator: validateVoiceId },
  ];

  // Check required variables
  for (const { name: varName, validator } of requiredVars) {
    const value = import.meta.env[varName];
    const displayName = import.meta.env.PROD ? '[REDACTED]' : varName;
    
    if (!value || value.trim() === '') {
      errors.push(`Missing required environment variable: ${displayName}`);
      continue;
    }

    // Security checks
    if (value.includes(' ') || value.includes('\n') || value.includes('\t')) {
      securityIssues.push(`Environment variable ${displayName} contains invalid characters`);
    }

    // Format validation
    if (validator && !validator(value)) {
      errors.push(`Invalid format for environment variable ${displayName}`);
    }

    // Check for common security issues
    if (value === 'your_api_key_here' || value === 'placeholder' || value === 'test') {
      securityIssues.push(`Environment variable ${displayName} appears to be a placeholder value`);
    }

    if (value.length < 10) {
      securityIssues.push(`Environment variable ${displayName} appears to be too short`);
    }
  }

  // Optional variables with warnings
  const optionalVars = [
    'VITE_HUGGING_FACE_MODEL',
  ];

  for (const varName of optionalVars) {
    const value = import.meta.env[varName];
    const displayName = import.meta.env.PROD ? '[REDACTED]' : varName;
    
    if (!value || value.trim() === '') {
      warnings.push(`Optional environment variable not set: ${displayName} (using default)`);
    }
  }

  // Development environment security warnings
  if (import.meta.env.DEV) {
    // Check if .env file might be exposed
    if (import.meta.env.VITE_HUGGING_FACE_API_KEY && 
        import.meta.env.VITE_HUGGING_FACE_API_KEY.includes('example')) {
      securityIssues.push('API keys appear to contain example values - update with real keys');
    }
  }

  // Production environment security checks
  if (import.meta.env.PROD) {
    // Ensure we're not using development keys in production
    const hfKey = import.meta.env.VITE_HUGGING_FACE_API_KEY;
    const elKey = import.meta.env.VITE_ELEVEN_LABS_API_KEY;
    
    if (hfKey && (hfKey.includes('dev') || hfKey.includes('test'))) {
      securityIssues.push('Production environment appears to be using development API keys');
    }
    
    if (elKey && (elKey.includes('dev') || elKey.includes('test'))) {
      securityIssues.push('Production environment appears to be using development API keys');
    }
  }

  return {
    isValid: errors.length === 0 && securityIssues.length === 0,
    errors,
    warnings,
    securityIssues,
  };
};

// Lazy configuration loading to avoid issues during testing
let _config: any = null;
let _environmentValidation: EnvironmentValidation | null = null;

// Secure configuration with validation
export const config = new Proxy({}, {
  get(target, prop) {
    if (!_config) {
      _config = {
        huggingFace: {
          apiKey: getSecureEnvVar('VITE_HUGGING_FACE_API_KEY', validateHuggingFaceKey),
          model: getEnvVar('VITE_HUGGING_FACE_MODEL', 'microsoft/DialoGPT-medium'),
        },
        elevenLabs: {
          apiKey: getSecureEnvVar('VITE_ELEVEN_LABS_API_KEY', validateElevenLabsKey),
          voiceId: getSecureEnvVar('VITE_ELEVEN_LABS_VOICE_ID', validateVoiceId),
        },
        isDevelopment: import.meta.env.DEV,
        isProduction: import.meta.env.PROD,
        nodeEnv: import.meta.env.NODE_ENV || 'development',
      };
    }
    return _config[prop as string];
  }
});

// Initialize environment validation lazily
const initializeEnvironmentValidation = (): EnvironmentValidation => {
  if (_environmentValidation) {
    return _environmentValidation;
  }

  try {
    _environmentValidation = validateEnvironment();
    
    // Log validation results (but not in production or tests)
    if (!import.meta.env.PROD && import.meta.env.NODE_ENV !== 'test') {
      if (_environmentValidation.warnings.length > 0) {
        console.warn('Environment warnings:', _environmentValidation.warnings);
      }
      
      if (_environmentValidation.securityIssues.length > 0) {
        console.warn('Security issues detected:', _environmentValidation.securityIssues);
      }
    }
    
    // Throw error if validation fails (but not in tests)
    if (!_environmentValidation.isValid && import.meta.env.NODE_ENV !== 'test') {
      const allIssues = [..._environmentValidation.errors, ..._environmentValidation.securityIssues];
      throw new Error(`Environment validation failed: ${allIssues.join(', ')}`);
    }
    
  } catch (error) {
    if (import.meta.env.NODE_ENV !== 'test') {
      console.error('Failed to initialize environment configuration:', error);
      throw error;
    }
    // In tests, return a default validation result
    _environmentValidation = {
      isValid: false,
      errors: [error instanceof Error ? error.message : 'Unknown error'],
      warnings: [],
      securityIssues: [],
    };
  }

  return _environmentValidation;
};

// Export validation results for use in components
export const getEnvironmentValidation = (): EnvironmentValidation => {
  return initializeEnvironmentValidation();
};
