import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ApiErrorHandler } from '../services/ApiErrorHandler';
import { HFError, ELError } from '../types/api';

// Mock window.navigator
Object.defineProperty(window, 'navigator', {
  value: {
    onLine: true
  },
  writable: true
});

// Mock window.addEventListener
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();
Object.defineProperty(window, 'addEventListener', {
  value: mockAddEventListener,
  writable: true
});
Object.defineProperty(window, 'removeEventListener', {
  value: mockRemoveEventListener,
  writable: true
});

describe('ApiErrorHandler', () => {
  let errorHandler: ApiErrorHandler;
  let mockNotificationCallback: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    errorHandler = new ApiErrorHandler();
    mockNotificationCallback = vi.fn();
    errorHandler.onNotification(mockNotificationCallback);
  });

  afterEach(() => {
    errorHandler.offNotification(mockNotificationCallback);
  });

  describe('Initialization', () => {
    it('should initialize with default retry configuration', () => {
      const config = errorHandler.getRetryConfig();
      expect(config.maxRetries).toBe(3);
      expect(config.baseDelay).toBe(1000);
      expect(config.maxDelay).toBe(10000);
      expect(config.backoffMultiplier).toBe(2);
      expect(config.retryableStatuses).toEqual([429, 500, 502, 503, 504]);
    });

    it('should accept custom retry configuration', () => {
      const customHandler = new ApiErrorHandler({
        maxRetries: 5,
        baseDelay: 500
      });
      const config = customHandler.getRetryConfig();
      expect(config.maxRetries).toBe(5);
      expect(config.baseDelay).toBe(500);
    });

    it('should setup offline detection listeners', () => {
      expect(mockAddEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(mockAddEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
  });

  describe('Notification System', () => {
    it('should register and call notification callbacks', () => {
      const testNotification = {
        type: 'error' as const,
        title: 'Test Error',
        message: 'Test message'
      };

      // Trigger a notification through error handling
      const error = new HFError('Test error', 500);
      errorHandler.handleHuggingFaceError(error);

      expect(mockNotificationCallback).toHaveBeenCalled();
    });

    it('should remove notification callbacks', () => {
      const callback = vi.fn();
      errorHandler.onNotification(callback);
      errorHandler.offNotification(callback);

      const error = new HFError('Test error', 500);
      errorHandler.handleHuggingFaceError(error);

      expect(callback).not.toHaveBeenCalled();
    });
  });

  describe('Hugging Face Error Handling', () => {
    it('should handle 401 authentication errors', () => {
      const error = new HFError('Unauthorized', 401);
      errorHandler.handleHuggingFaceError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Authentication Error',
          message: expect.stringContaining('Invalid API key')
        })
      );
    });

    it('should handle 429 rate limiting errors', () => {
      const error = new HFError('Rate limited', 429);
      errorHandler.handleHuggingFaceError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Rate Limited',
          message: expect.stringContaining('Too many requests')
        })
      );
    });

    it('should handle 503 service unavailable errors', () => {
      const error = new HFError('Service unavailable', 503);
      errorHandler.handleHuggingFaceError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Service Unavailable',
          message: expect.stringContaining('temporarily unavailable'),
          action: expect.objectContaining({
            label: 'Retry Now'
          })
        })
      );
    });

    it('should handle network connection errors', () => {
      const error = new HFError('Network error', 0);
      errorHandler.handleHuggingFaceError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Connection Failed',
          message: expect.stringContaining('Unable to connect')
        })
      );
    });

    it('should handle unknown errors', () => {
      const error = new HFError('Unknown error', 999);
      errorHandler.handleHuggingFaceError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'AI Service Error',
          message: expect.stringContaining('encountered an issue')
        })
      );
    });
  });

  describe('Eleven Labs Error Handling', () => {
    it('should handle 401 authentication errors', () => {
      const error = new ELError('Unauthorized', 401);
      errorHandler.handleElevenLabsError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Voice Authentication Error',
          message: expect.stringContaining('Invalid Eleven Labs API key')
        })
      );
    });

    it('should handle 429 rate limiting errors', () => {
      const error = new ELError('Rate limited', 429);
      errorHandler.handleElevenLabsError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Voice Rate Limited',
          message: expect.stringContaining('Voice generation rate limited')
        })
      );
    });

    it('should handle 503 service unavailable errors', () => {
      const error = new ELError('Service unavailable', 503);
      errorHandler.handleElevenLabsError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Voice Service Unavailable',
          message: expect.stringContaining('Voice synthesis temporarily unavailable')
        })
      );
    });

    it('should handle network connection errors', () => {
      const error = new ELError('Network error', 0);
      errorHandler.handleElevenLabsError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Voice Connection Failed',
          message: expect.stringContaining('Unable to connect to voice service')
        })
      );
    });
  });

  describe('Network Error Handling', () => {
    it('should handle network errors', () => {
      const error = { message: 'Network failed', code: 'NETWORK_ERROR' };
      errorHandler.handleNetworkError(error);

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Network Error',
          message: expect.stringContaining('Connection failed')
        })
      );
    });
  });

  describe('User-Friendly Error Messages', () => {
    it('should show API error messages', () => {
      errorHandler.showUserFriendlyMessage('api');

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Service Error',
          message: expect.stringContaining('service issue')
        })
      );
    });

    it('should show network error messages', () => {
      errorHandler.showUserFriendlyMessage('network');

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Connection Error',
          message: expect.stringContaining('check your internet connection')
        })
      );
    });

    it('should show validation error messages', () => {
      errorHandler.showUserFriendlyMessage('validation');

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Input Error',
          message: expect.stringContaining('check your input')
        })
      );
    });

    it('should show browser compatibility messages', () => {
      errorHandler.showUserFriendlyMessage('browser');

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Browser Compatibility',
          message: expect.stringContaining('may not work in your browser')
        })
      );
    });

    it('should show unknown error messages', () => {
      errorHandler.showUserFriendlyMessage('unknown');

      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'error',
          title: 'Unexpected Error',
          message: expect.stringContaining('Something went wrong')
        })
      );
    });
  });

  describe('Retry Logic', () => {
    it('should execute operation successfully on first try', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      
      const result = await errorHandler.executeWithRetry(mockOperation);
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const mockOperation = vi.fn()
        .mockRejectedValueOnce(new HFError('Server error', 503))
        .mockResolvedValue('success');
      
      const result = await errorHandler.executeWithRetry(mockOperation);
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should not retry on non-retryable errors', async () => {
      const mockOperation = vi.fn()
        .mockRejectedValue(new HFError('Bad request', 400));
      
      await expect(errorHandler.executeWithRetry(mockOperation))
        .rejects.toThrow('Bad request');
      
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should respect max retry limit', async () => {
      const mockOperation = vi.fn()
        .mockRejectedValue(new HFError('Server error', 503));
      
      await expect(errorHandler.executeWithRetry(mockOperation))
        .rejects.toThrow('Server error');
      
      expect(mockOperation).toHaveBeenCalledTimes(4); // Initial + 3 retries
    }, 10000); // Increase timeout to 10 seconds

    it('should apply exponential backoff', async () => {
      const mockOperation = vi.fn()
        .mockRejectedValueOnce(new HFError('Server error', 503))
        .mockRejectedValueOnce(new HFError('Server error', 503))
        .mockResolvedValue('success');
      
      const startTime = Date.now();
      const result = await errorHandler.executeWithRetry(mockOperation);
      const endTime = Date.now();
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
      // Should have some delay due to backoff (at least 1000ms + 2000ms)
      expect(endTime - startTime).toBeGreaterThan(2500);
    });
  });

  describe('Offline Queue Management', () => {
    beforeEach(() => {
      // Mock navigator.onLine to false
      Object.defineProperty(window.navigator, 'onLine', {
        value: false,
        writable: true
      });
    });

    it('should add items to offline queue', () => {
      const id = errorHandler.addToOfflineQueue('text', { message: 'test' });
      
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      
      const status = errorHandler.getOfflineQueueStatus();
      expect(status.count).toBe(1);
      expect(status.items[0].type).toBe('text');
      expect(status.items[0].data).toEqual({ message: 'test' });
    });

    it('should clear offline queue', () => {
      errorHandler.addToOfflineQueue('text', { message: 'test' });
      errorHandler.clearOfflineQueue();
      
      const status = errorHandler.getOfflineQueueStatus();
      expect(status.count).toBe(0);
    });

    it('should detect online status', () => {
      expect(errorHandler.isCurrentlyOnline()).toBe(false);
      
      // Mock back to online
      Object.defineProperty(window.navigator, 'onLine', {
        value: true,
        writable: true
      });
      
      // Create new handler to pick up the new online status
      const onlineHandler = new ApiErrorHandler();
      expect(onlineHandler.isCurrentlyOnline()).toBe(true);
    });
  });

  describe('Configuration Management', () => {
    it('should update retry configuration', () => {
      errorHandler.updateRetryConfig({
        maxRetries: 5,
        baseDelay: 2000
      });
      
      const config = errorHandler.getRetryConfig();
      expect(config.maxRetries).toBe(5);
      expect(config.baseDelay).toBe(2000);
      expect(config.maxDelay).toBe(10000); // Should keep existing values
    });

    it('should return copy of configuration', () => {
      const config1 = errorHandler.getRetryConfig();
      const config2 = errorHandler.getRetryConfig();
      
      expect(config1).toEqual(config2);
      expect(config1).not.toBe(config2); // Different objects
    });
  });

  describe('Online/Offline Event Handling', () => {
    it('should handle online event', () => {
      // Find the online event listener
      const onlineCall = mockAddEventListener.mock.calls.find(
        call => call[0] === 'online'
      );
      expect(onlineCall).toBeDefined();
      
      const onlineHandler = onlineCall![1];
      
      // Simulate going online
      onlineHandler();
      
      // Should show notification about connection restored
      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'info',
          title: 'Connection Restored',
          message: expect.stringContaining('Back online')
        })
      );
    });

    it('should handle offline event', () => {
      // Find the offline event listener
      const offlineCall = mockAddEventListener.mock.calls.find(
        call => call[0] === 'offline'
      );
      expect(offlineCall).toBeDefined();
      
      const offlineHandler = offlineCall![1];
      
      // Simulate going offline
      offlineHandler();
      
      // Should show notification about connection lost
      expect(mockNotificationCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'warning',
          title: 'Connection Lost',
          message: expect.stringContaining('offline')
        })
      );
    });
  });
});