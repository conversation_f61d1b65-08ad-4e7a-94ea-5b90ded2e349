import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import App from '../App';
import { AIService } from '../services/AIService';
import type { PersonalityConfig } from '../types/personality';

// Mock environment
vi.mock('../utils/env', () => ({
  config: {
    huggingFace: {
      apiKey: 'test-hf-key',
      model: 'test-model'
    },
    elevenLabs: {
      apiKey: 'test-el-key',
      voiceId: 'test-voice-id'
    }
  }
}));

// Mock AI Service
vi.mock('../services/AIService');

// Mock personality utilities
vi.mock('../utils/personality', () => ({
  loadPersonality: vi.fn(),
  savePersonality: vi.fn(),
  validatePersonality: vi.fn().mockReturnValue({ isValid: true, errors: [] }),
  createDefaultPersonality: vi.fn().mockReturnValue({
    name: '<PERSON>',
    pronouns: 'he/him',
    personality: {
      tone: 'friendly',
      role: 'AI companion',
      hobbies: ['technology', 'learning'],
      style: {
        speech: 'natural',
        humor: 'light',
        depth: 'thoughtful'
      },
      boundaries: {
        avoid: ['harmful content'],
        safe_topics: ['technology', 'general conversation']
      },
      dynamic_traits: {
        adaptive_empathy: true,
        mirroring_style: true,
        emotionally_available: true
      }
    },
    conversation_tips: {
      starter_prompts: ['Hello!', 'How can I help?']
    },
    version: '1.0.0'
  })
}));

describe('Personality System Integration Tests', () => {
  let mockAIService: any;
  let user: ReturnType<typeof userEvent.setup>;
  let basePersonality: PersonalityConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    user = userEvent.setup();
    
    basePersonality = {
      name: 'Nathan',
      pronouns: 'he/him',
      personality: {
        tone: 'friendly',
        role: 'AI companion',
        hobbies: ['technology', 'learning', 'helping others'],
        style: {
          speech: 'natural and conversational',
          humor: 'light and appropriate',
          depth: 'thoughtful and engaging'
        },
        boundaries: {
          avoid: ['harmful content', 'inappropriate topics'],
          safe_topics: ['technology', 'general conversation', 'learning']
        },
        dynamic_traits: {
          adaptive_empathy: true,
          mirroring_style: true,
          emotionally_available: true
        }
      },
      conversation_tips: {
        starter_prompts: [
          'Hello! How are you doing today?',
          'What would you like to talk about?',
          'I\'m here to help with anything you need!'
        ]
      },
      version: '1.0.0'
    };

    const { loadPersonality, savePersonality } = await import('../utils/personality');
    vi.mocked(loadPersonality).mockResolvedValue(basePersonality);
    vi.mocked(savePersonality).mockResolvedValue(undefined);
    
    mockAIService = {
      generateResponse: vi.fn(),
      generateTextResponse: vi.fn(),
      generateAudio: vi.fn(),
      clearConversationHistory: vi.fn(),
      healthCheck: vi.fn().mockResolvedValue({
        online: true,
        huggingFace: true,
        elevenLabs: true,
        overall: true
      })
    };
    
    vi.mocked(AIService).mockImplementation(() => mockAIService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Personality Loading and Initialization', () => {
    it('should load personality on app initialization', async () => {
      render(<App />);

      const { loadPersonality } = await import('../utils/personality');
      await waitFor(() => {
        expect(loadPersonality).toHaveBeenCalled();
      });

      await waitFor(() => {
        expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
      });

      // Should display personality characteristics in UI
      expect(screen.getByText(/friendly/i)).toBeInTheDocument();
      expect(screen.getByText(/AI companion/i)).toBeInTheDocument();
    });

    it('should handle personality loading failure with fallback', async () => {
      mockLoadPersonality.mockRejectedValueOnce(new Error('Failed to load personality'));

      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/failed to load personality/i)).toBeInTheDocument();
      });

      // Should show retry option
      const retryButton = screen.getByTestId('retry-personality-button');
      expect(retryButton).toBeInTheDocument();

      // Retry should work
      mockLoadPersonality.mockResolvedValueOnce(basePersonality);
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
      });
    });

    it('should create default personality if none exists', async () => {
      mockLoadPersonality.mockRejectedValueOnce(new Error('Personality file not found'));

      const { createDefaultPersonality } = await import('../utils/personality');
      
      render(<App />);

      await waitFor(() => {
        expect(createDefaultPersonality).toHaveBeenCalled();
      });

      // Should use default personality
      await waitFor(() => {
        expect(screen.getByTestId('personality-name')).toHaveTextContent('Nathan');
      });
    });

    it('should validate personality configuration on load', async () => {
      const invalidPersonality = {
        ...basePersonality,
        personality: {
          ...basePersonality.personality,
          tone: '' // Invalid empty tone
        }
      };

      mockLoadPersonality.mockResolvedValueOnce(invalidPersonality);

      const { validatePersonality } = await import('../utils/personality');
      vi.mocked(validatePersonality).mockReturnValueOnce({
        isValid: false,
        errors: ['Tone cannot be empty']
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/personality configuration invalid/i)).toBeInTheDocument();
        expect(screen.getByText(/tone cannot be empty/i)).toBeInTheDocument();
      });

      // Should show fix personality option
      expect(screen.getByTestId('fix-personality-button')).toBeInTheDocument();
    });
  });

  describe('Personality Integration with Conversations', () => {
    it('should pass personality to AI service for response generation', async () => {
      mockAIService.generateResponse.mockResolvedValue({
        text: 'Hello! I\'m Nathan, your friendly AI companion.',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'Hello Nathan!');
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockAIService.generateResponse).toHaveBeenCalledWith(
          'Hello Nathan!',
          expect.objectContaining({
            name: 'Nathan',
            pronouns: 'he/him',
            personality: expect.objectContaining({
              tone: 'friendly',
              role: 'AI companion',
              hobbies: expect.arrayContaining(['technology', 'learning', 'helping others'])
            })
          })
        );
      });
    });

    it('should adapt responses based on personality traits', async () => {
      // Test with empathetic personality
      const empatheticPersonality: PersonalityConfig = {
        ...basePersonality,
        personality: {
          ...basePersonality.personality,
          tone: 'empathetic and supportive',
          dynamic_traits: {
            adaptive_empathy: true,
            mirroring_style: true,
            emotionally_available: true
          }
        }
      };

      mockLoadPersonality.mockResolvedValueOnce(empatheticPersonality);

      mockAIService.generateResponse.mockResolvedValue({
        text: 'I can sense you might be feeling stressed. I\'m here to listen and support you.',
        emotion: 'empathetic',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'I\'m having a really tough day...');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/I can sense you might be feeling stressed/)).toBeInTheDocument();
      });

      // Verify empathetic personality was used
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'I\'m having a really tough day...',
        expect.objectContaining({
          personality: expect.objectContaining({
            tone: 'empathetic and supportive',
            dynamic_traits: expect.objectContaining({
              adaptive_empathy: true
            })
          })
        })
      );
    });

    it('should respect personality boundaries in conversations', async () => {
      const boundaryPersonality: PersonalityConfig = {
        ...basePersonality,
        personality: {
          ...basePersonality.personality,
          boundaries: {
            avoid: ['politics', 'controversial topics'],
            safe_topics: ['technology', 'hobbies', 'general conversation']
          }
        }
      };

      mockLoadPersonality.mockResolvedValueOnce(boundaryPersonality);

      mockAIService.generateResponse.mockResolvedValue({
        text: 'I prefer not to discuss political topics. How about we talk about technology or your hobbies instead?',
        emotion: 'neutral',
        audio: new ArrayBuffer(512)
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'What do you think about the current political situation?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/I prefer not to discuss political topics/)).toBeInTheDocument();
      });

      // Verify boundaries were passed to AI service
      expect(mockAIService.generateResponse).toHaveBeenCalledWith(
        'What do you think about the current political situation?',
        expect.objectContaining({
          personality: expect.objectContaining({
            boundaries: expect.objectContaining({
              avoid: expect.arrayContaining(['politics', 'controversial topics'])
            })
          })
        })
      );
    });

    it('should use conversation tips for starter prompts', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Should display starter prompts from personality
      expect(screen.getByText('Hello! How are you doing today?')).toBeInTheDocument();
      expect(screen.getByText('What would you like to talk about?')).toBeInTheDocument();
      expect(screen.getByText('I\'m here to help with anything you need!')).toBeInTheDocument();

      // Clicking a starter prompt should send it as a message
      const starterPrompt = screen.getByTestId('starter-prompt-0');
      
      mockAIService.generateResponse.mockResolvedValue({
        text: 'I\'m doing great, thanks for asking! How about you?',
        emotion: 'happy',
        audio: new ArrayBuffer(512)
      });

      await user.click(starterPrompt);

      await waitFor(() => {
        expect(screen.getByText('Hello! How are you doing today?')).toBeInTheDocument();
        expect(screen.getByText('I\'m doing great, thanks for asking! How about you?')).toBeInTheDocument();
      });
    });
  });

  describe('Dynamic Personality Updates', () => {
    it('should update personality configuration in real-time', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Update personality tone
      const toneInput = screen.getByTestId('personality-tone-input');
      await user.clear(toneInput);
      await user.type(toneInput, 'enthusiastic and energetic');

      // Update role
      const roleInput = screen.getByTestId('personality-role-input');
      await user.clear(roleInput);
      await user.type(roleInput, 'motivational coach');

      // Save changes
      const saveButton = screen.getByTestId('save-personality-button');
      await user.click(saveButton);

      // Verify personality was saved
      await waitFor(() => {
        expect(mockSavePersonality).toHaveBeenCalledWith(
          expect.objectContaining({
            personality: expect.objectContaining({
              tone: 'enthusiastic and energetic',
              role: 'motivational coach'
            })
          })
        );
      });

      // Test that updated personality is used in conversations
      mockAIService.generateResponse.mockResolvedValue({
        text: 'YES! That\'s the spirit! I\'m here to help you achieve your goals!',
        emotion: 'excited',
        audio: new ArrayBuffer(512)
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      await user.type(textInput, 'I want to learn something new');
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockAIService.generateResponse).toHaveBeenCalledWith(
          'I want to learn something new',
          expect.objectContaining({
            personality: expect.objectContaining({
              tone: 'enthusiastic and energetic',
              role: 'motivational coach'
            })
          })
        );
      });
    });

    it('should validate personality updates before saving', async () => {
      const { validatePersonality } = await import('../utils/personality');
      
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Try to set invalid tone (empty)
      const toneInput = screen.getByTestId('personality-tone-input');
      await user.clear(toneInput);

      // Mock validation failure
      vi.mocked(validatePersonality).mockReturnValueOnce({
        isValid: false,
        errors: ['Tone cannot be empty']
      });

      // Try to save
      const saveButton = screen.getByTestId('save-personality-button');
      await user.click(saveButton);

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText('Tone cannot be empty')).toBeInTheDocument();
      });

      // Should not save invalid personality
      expect(mockSavePersonality).not.toHaveBeenCalled();
    });

    it('should handle personality update failures gracefully', async () => {
      mockSavePersonality.mockRejectedValueOnce(new Error('Failed to save personality'));

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Make a change
      const toneInput = screen.getByTestId('personality-tone-input');
      await user.clear(toneInput);
      await user.type(toneInput, 'updated tone');

      // Try to save
      const saveButton = screen.getByTestId('save-personality-button');
      await user.click(saveButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/failed to save personality/i)).toBeInTheDocument();
      });

      // Should offer retry option
      const retryButton = screen.getByTestId('retry-save-button');
      expect(retryButton).toBeInTheDocument();

      // Retry should work
      mockSavePersonality.mockResolvedValueOnce(undefined);
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText(/personality saved successfully/i)).toBeInTheDocument();
      });
    });

    it('should revert to previous personality on save failure', async () => {
      mockSavePersonality.mockRejectedValueOnce(new Error('Save failed'));

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Original tone should be displayed
      const toneInput = screen.getByTestId('personality-tone-input');
      expect(toneInput).toHaveValue('friendly');

      // Make a change
      await user.clear(toneInput);
      await user.type(toneInput, 'aggressive');

      // Try to save (will fail)
      const saveButton = screen.getByTestId('save-personality-button');
      await user.click(saveButton);

      // Should revert to original value
      await waitFor(() => {
        expect(toneInput).toHaveValue('friendly');
      });

      // Should show revert notification
      expect(screen.getByText(/changes reverted/i)).toBeInTheDocument();
    });
  });

  describe('Personality Presets and Templates', () => {
    it('should load personality presets', async () => {
      const presets = [
        {
          name: 'Casual Friend',
          personality: {
            ...basePersonality.personality,
            tone: 'casual and friendly',
            role: 'friend'
          }
        },
        {
          name: 'Professional Assistant',
          personality: {
            ...basePersonality.personality,
            tone: 'professional and helpful',
            role: 'assistant'
          }
        }
      ];

      // Mock preset loading
      vi.doMock('../utils/personality', async () => {
        const actual = await vi.importActual('../utils/personality');
        return {
          ...actual,
          loadPersonalityPresets: vi.fn().mockResolvedValue(presets)
        };
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Should show preset options
      expect(screen.getByText('Casual Friend')).toBeInTheDocument();
      expect(screen.getByText('Professional Assistant')).toBeInTheDocument();

      // Select a preset
      const casualPreset = screen.getByTestId('preset-casual-friend');
      await user.click(casualPreset);

      // Should apply preset values
      const toneInput = screen.getByTestId('personality-tone-input');
      expect(toneInput).toHaveValue('casual and friendly');

      const roleInput = screen.getByTestId('personality-role-input');
      expect(roleInput).toHaveValue('friend');
    });

    it('should create custom personality templates', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Customize personality
      const toneInput = screen.getByTestId('personality-tone-input');
      await user.clear(toneInput);
      await user.type(toneInput, 'quirky and humorous');

      const hobbiesInput = screen.getByTestId('personality-hobbies-input');
      await user.clear(hobbiesInput);
      await user.type(hobbiesInput, 'comedy, wordplay, puns');

      // Save as template
      const saveTemplateButton = screen.getByTestId('save-template-button');
      await user.click(saveTemplateButton);

      // Enter template name
      const templateNameInput = screen.getByTestId('template-name-input');
      await user.type(templateNameInput, 'Comedy Bot');

      const confirmSaveButton = screen.getByTestId('confirm-save-template-button');
      await user.click(confirmSaveButton);

      // Should save template
      await waitFor(() => {
        expect(screen.getByText(/template saved successfully/i)).toBeInTheDocument();
      });

      // Template should appear in preset list
      expect(screen.getByText('Comedy Bot')).toBeInTheDocument();
    });
  });

  describe('Personality Consistency', () => {
    it('should maintain personality consistency across conversation', async () => {
      const consistentPersonality: PersonalityConfig = {
        ...basePersonality,
        personality: {
          ...basePersonality.personality,
          tone: 'formal and professional',
          role: 'business consultant',
          style: {
            speech: 'professional and structured',
            humor: 'minimal',
            depth: 'analytical'
          }
        }
      };

      mockLoadPersonality.mockResolvedValueOnce(consistentPersonality);

      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'Good day. I understand you require business consultation. How may I assist you?',
          emotion: 'professional',
          audio: new ArrayBuffer(512)
        })
        .mockResolvedValueOnce({
          text: 'Based on the data you\'ve provided, I recommend a strategic approach focusing on market analysis.',
          emotion: 'analytical',
          audio: new ArrayBuffer(512)
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // First exchange
      await user.type(textInput, 'I need help with my business strategy');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Good day. I understand you require business consultation/)).toBeInTheDocument();
      });

      // Second exchange
      await user.type(textInput, 'Here are my sales figures for the last quarter');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Based on the data you\'ve provided/)).toBeInTheDocument();
      });

      // Both calls should use the same consistent personality
      expect(mockAIService.generateResponse).toHaveBeenNthCalledWith(1,
        'I need help with my business strategy',
        expect.objectContaining({
          personality: expect.objectContaining({
            tone: 'formal and professional',
            role: 'business consultant'
          })
        })
      );

      expect(mockAIService.generateResponse).toHaveBeenNthCalledWith(2,
        'Here are my sales figures for the last quarter',
        expect.objectContaining({
          personality: expect.objectContaining({
            tone: 'formal and professional',
            role: 'business consultant'
          })
        })
      );
    });

    it('should adapt personality based on conversation context while maintaining core traits', async () => {
      const adaptivePersonality: PersonalityConfig = {
        ...basePersonality,
        personality: {
          ...basePersonality.personality,
          dynamic_traits: {
            adaptive_empathy: true,
            mirroring_style: true,
            emotionally_available: true
          }
        }
      };

      mockLoadPersonality.mockResolvedValueOnce(adaptivePersonality);

      mockAIService.generateResponse
        .mockResolvedValueOnce({
          text: 'I\'m so sorry to hear you\'re going through a difficult time. I\'m here to listen.',
          emotion: 'empathetic',
          audio: new ArrayBuffer(512)
        })
        .mockResolvedValueOnce({
          text: 'That\'s fantastic news! I\'m really excited for you!',
          emotion: 'excited',
          audio: new ArrayBuffer(512)
        });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      const textInput = screen.getByTestId('text-input');
      const sendButton = screen.getByTestId('send-button');

      // Sad context
      await user.type(textInput, 'I just lost my job and I\'m feeling really down');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/I\'m so sorry to hear you\'re going through a difficult time/)).toBeInTheDocument();
      });

      // Happy context
      await user.type(textInput, 'Actually, I just got offered an even better position!');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/That\'s fantastic news! I\'m really excited for you!/)).toBeInTheDocument();
      });

      // Both should use adaptive personality but maintain core traits
      expect(mockAIService.generateResponse).toHaveBeenCalledTimes(2);
      
      // Verify adaptive empathy was enabled for both calls
      const calls = mockAIService.generateResponse.mock.calls;
      calls.forEach(call => {
        expect(call[1]).toMatchObject({
          personality: expect.objectContaining({
            dynamic_traits: expect.objectContaining({
              adaptive_empathy: true
            })
          })
        });
      });
    });
  });

  describe('Personality Export and Import', () => {
    it('should export personality configuration', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Click export button
      const exportButton = screen.getByTestId('export-personality-button');
      
      // Mock URL.createObjectURL
      const mockCreateObjectURL = vi.fn().mockReturnValue('blob:mock-url');
      global.URL.createObjectURL = mockCreateObjectURL;

      // Mock link click
      const mockClick = vi.fn();
      const mockLink = { click: mockClick, href: '', download: '' };
      vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any);

      await user.click(exportButton);

      // Should create download link
      expect(mockCreateObjectURL).toHaveBeenCalledWith(expect.any(Blob));
      expect(mockClick).toHaveBeenCalled();
      expect(mockLink.download).toBe('nathan-personality.json');
    });

    it('should import personality configuration', async () => {
      const importedPersonality: PersonalityConfig = {
        ...basePersonality,
        name: 'ImportedBot',
        personality: {
          ...basePersonality.personality,
          tone: 'imported tone',
          role: 'imported role'
        }
      };

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Mock file input
      const fileInput = screen.getByTestId('import-personality-input');
      const file = new File([JSON.stringify(importedPersonality)], 'personality.json', {
        type: 'application/json'
      });

      // Mock FileReader
      const mockFileReader = {
        readAsText: vi.fn(),
        result: JSON.stringify(importedPersonality),
        onload: null as any
      };
      
      vi.spyOn(window, 'FileReader').mockImplementation(() => mockFileReader as any);

      await user.upload(fileInput, file);

      // Simulate file read completion
      act(() => {
        mockFileReader.onload?.({ target: mockFileReader } as any);
      });

      // Should update personality fields
      await waitFor(() => {
        const nameInput = screen.getByTestId('personality-name-input');
        expect(nameInput).toHaveValue('ImportedBot');
        
        const toneInput = screen.getByTestId('personality-tone-input');
        expect(toneInput).toHaveValue('imported tone');
        
        const roleInput = screen.getByTestId('personality-role-input');
        expect(roleInput).toHaveValue('imported role');
      });

      // Should show import success message
      expect(screen.getByText(/personality imported successfully/i)).toBeInTheDocument();
    });

    it('should validate imported personality configuration', async () => {
      const invalidPersonality = {
        name: '', // Invalid empty name
        personality: {
          tone: 'valid tone'
          // Missing required fields
        }
      };

      const { validatePersonality } = await import('../utils/personality');
      vi.mocked(validatePersonality).mockReturnValueOnce({
        isValid: false,
        errors: ['Name cannot be empty', 'Role is required']
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      });

      // Open personality settings
      const settingsButton = screen.getByTestId('settings-button');
      await user.click(settingsButton);

      // Mock file input with invalid personality
      const fileInput = screen.getByTestId('import-personality-input');
      const file = new File([JSON.stringify(invalidPersonality)], 'invalid.json', {
        type: 'application/json'
      });

      const mockFileReader = {
        readAsText: vi.fn(),
        result: JSON.stringify(invalidPersonality),
        onload: null as any
      };
      
      vi.spyOn(window, 'FileReader').mockImplementation(() => mockFileReader as any);

      await user.upload(fileInput, file);

      act(() => {
        mockFileReader.onload?.({ target: mockFileReader } as any);
      });

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText('Name cannot be empty')).toBeInTheDocument();
        expect(screen.getByText('Role is required')).toBeInTheDocument();
      });

      // Should not apply invalid personality
      const nameInput = screen.getByTestId('personality-name-input');
      expect(nameInput).toHaveValue('Nathan'); // Should keep original
    });
  });
});