import React, { useMemo } from 'react';
import type { MessageBubbleProps } from '../types/components';
import { memoize } from '../utils/performance';
import styles from './MessageBubble.module.css';

// Memoized emotion display function
const getEmotionDisplay = memoize((emotion?: string): string => {
  if (!emotion || emotion === 'neutral') return '';
  
  const emotionEmojis = {
    happy: '😊',
    thoughtful: '🤔',
    empathetic: '💙'
  };
  
  return emotionEmojis[emotion as keyof typeof emotionEmojis] || '';
});

// Memoized timestamp formatter
const formatTimestamp = memoize((timestamp: Date): string => {
  return timestamp.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
});

export const MessageBubble: React.FC<MessageBubbleProps> = React.memo(({
  message,
  isUser,
  showAvatar,
  personality
}) => {
  // Memoize expensive computations
  const formattedTimestamp = useMemo(() => 
    formatTimestamp(message.timestamp), 
    [message.timestamp]
  );
  
  const emotionDisplay = useMemo(() => 
    getEmotionDisplay(message.emotion), 
    [message.emotion]
  );
  
  const avatarInitial = useMemo(() => 
    personality.name.charAt(0).toUpperCase(), 
    [personality.name]
  );
  
  const messageClasses = useMemo(() => 
    `${styles.messageContainer} ${isUser ? styles.userMessage : styles.nathanMessage}`,
    [isUser]
  );
  
  const avatarClasses = useMemo(() => 
    `${styles.avatar} ${styles[message.emotion || 'neutral']}`,
    [message.emotion]
  );

  return (
    <div className={messageClasses}>
      {showAvatar && !isUser && (
        <div className={styles.avatarContainer}>
          <div className={avatarClasses}>
            {avatarInitial}
          </div>
        </div>
      )}
      
      <div className={styles.messageContent}>
        <div className={styles.messageBubble}>
          <div className={styles.messageText}>
            {message.content}
          </div>
          
          {message.emotion && message.emotion !== 'neutral' && (
            <div className={styles.emotionIndicator}>
              {emotionDisplay}
            </div>
          )}
        </div>
        
        <div className={styles.messageMetadata}>
          <span className={styles.timestamp}>
            {formattedTimestamp}
          </span>
          
          {message.audioUrl && (
            <span className={styles.audioIndicator} title="Audio available">
              🔊
            </span>
          )}
        </div>
      </div>
      
      {showAvatar && isUser && (
        <div className={styles.avatarContainer}>
          <div className={`${styles.avatar} ${styles.user}`}>
            U
          </div>
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.emotion === nextProps.message.emotion &&
    prevProps.message.audioUrl === nextProps.message.audioUrl &&
    prevProps.isUser === nextProps.isUser &&
    prevProps.showAvatar === nextProps.showAvatar &&
    prevProps.personality.name === nextProps.personality.name
  );
});

export default MessageBubble;