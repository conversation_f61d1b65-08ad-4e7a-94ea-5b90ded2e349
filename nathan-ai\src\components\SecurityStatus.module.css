.securityStatus {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 8px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 0.9rem;
  line-height: 1.4;
}

.errorSection {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 6px;
}

.errorSection h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.errors,
.securityIssues {
  margin: 0.5rem 0;
}

.errors strong,
.securityIssues strong {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.errors ul,
.securityIssues ul,
.warningSection ul {
  margin: 0.25rem 0 0 1rem;
  padding: 0;
}

.errors li,
.securityIssues li,
.warningSection li {
  margin: 0.25rem 0;
}

.warningSection {
  background-color: #fff8e1;
  border: 1px solid #ffcc02;
  color: #b8860b;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 6px;
}

.warningSection h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.statusSection {
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  color: #0066cc;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 6px;
}

.statusSection h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.rateLimits {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.apiStatus {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

.apiStatus strong {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.apiStatus span {
  font-size: 0.85rem;
  margin-left: 0.5rem;
}

.successSection {
  background-color: #f0fff0;
  border: 1px solid #90ee90;
  color: #006400;
  padding: 1rem;
  border-radius: 6px;
}

.successSection h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.successSection p {
  margin: 0;
}

/* Responsive design */
@media (min-width: 768px) {
  .rateLimits {
    flex-direction: row;
    gap: 1rem;
  }
  
  .apiStatus {
    flex: 1;
  }
  
  .apiStatus span {
    display: block;
    margin-left: 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .errorSection {
    background-color: #4a1a1a;
    border-color: #8b3a3a;
    color: #ff6b6b;
  }
  
  .warningSection {
    background-color: #4a3d1a;
    border-color: #8b7a3a;
    color: #ffd93d;
  }
  
  .statusSection {
    background-color: #1a2a4a;
    border-color: #3a5a8b;
    color: #6bb6ff;
  }
  
  .successSection {
    background-color: #1a4a1a;
    border-color: #3a8b3a;
    color: #90ee90;
  }
  
  .apiStatus {
    background-color: rgba(0, 0, 0, 0.3);
  }
}