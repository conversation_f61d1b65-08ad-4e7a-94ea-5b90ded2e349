import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ErrorBoundary } from '../components/ErrorBoundary';

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

// Mock window.location.reload
const reloadMock = vi.fn();
Object.defineProperty(window, 'location', {
  value: { reload: reloadMock },
  writable: true,
});

describe('ErrorBoundary Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console.error to avoid test output noise
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'group').mockImplementation(() => {});
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {});
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('renders error UI when child component throws', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText(/Nathan encountered a display error/)).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
    expect(screen.getByText('Retry in 1s')).toBeInTheDocument();
  });

  it('displays error details in expandable section', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    const detailsElement = screen.getByText('Technical Details');
    expect(detailsElement).toBeInTheDocument();

    // Click to expand details
    fireEvent.click(detailsElement);

    expect(screen.getByText('Error: Test error')).toBeInTheDocument();
  });

  it('calls onError callback when error occurs', () => {
    const onErrorMock = vi.fn();

    render(
      <ErrorBoundary onError={onErrorMock}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(onErrorMock).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    );
  });

  it('renders custom fallback UI when provided', () => {
    const customFallback = <div>Custom error message</div>;

    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Custom error message')).toBeInTheDocument();
    expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
  });

  it('has Try Again button that calls handleReset', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();

    const tryAgainButton = screen.getByText('Try Again');
    expect(tryAgainButton).toBeInTheDocument();
    
    // Just verify the button exists and is clickable
    fireEvent.click(tryAgainButton);
    
    // The error boundary should still be in error state since the child still throws
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('refreshes page when Refresh Page is clicked for app level', () => {
    render(
      <ErrorBoundary level="app">
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByText('Refresh Page'));

    expect(reloadMock).toHaveBeenCalled();
  });

  it('handles errors with no message gracefully', () => {
    const ThrowEmptyError = () => {
      throw new Error('');
    };

    render(
      <ErrorBoundary>
        <ThrowEmptyError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('handles non-Error objects thrown', () => {
    const ThrowString = () => {
      throw 'String error';
    };

    render(
      <ErrorBoundary>
        <ThrowString />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    // Check for CSS module classes
    expect(document.querySelector('[class*="errorBoundary"]')).toBeInTheDocument();
    expect(document.querySelector('[class*="content"]')).toBeInTheDocument();
    expect(document.querySelector('[class*="actions"]')).toBeInTheDocument();
  });

  it('shows component stack in error details', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    const detailsElement = screen.getByText('Technical Details');
    fireEvent.click(detailsElement);

    // Component stack should be visible
    const stackElement = document.querySelector('[class*="stack"]');
    expect(stackElement).toBeInTheDocument();
  });

  it('displays different error messages based on error type', () => {
    const ApiError = () => {
      throw new Error('fetch failed');
    };

    render(
      <ErrorBoundary>
        <ApiError />
      </ErrorBoundary>
    );

    expect(screen.getByText(/trouble connecting to the AI services/)).toBeInTheDocument();
  });

  it('shows error ID and metadata', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    const detailsElement = screen.getByText('Technical Details');
    fireEvent.click(detailsElement);

    expect(screen.getByText(/Error ID:/)).toBeInTheDocument();
    expect(screen.getByText(/Time:/)).toBeInTheDocument();
    expect(screen.getByText(/Level:/)).toBeInTheDocument();
  });

  it('supports different boundary levels', () => {
    render(
      <ErrorBoundary level="app">
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Application Error')).toBeInTheDocument();
    expect(screen.getByText('Refresh Page')).toBeInTheDocument();
  });

  it('resets on resetKeys change', async () => {
    const TestComponent = ({ resetKey }: { resetKey: string }) => (
      <ErrorBoundary resetKeys={[resetKey]}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    const { rerender } = render(<TestComponent resetKey="key1" />);
    
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();

    // Change resetKey to trigger reset
    rerender(<TestComponent resetKey="key2" />);

    // Should reset and show error again since component still throws
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('has retry with delay functionality', () => {
    vi.useFakeTimers();

    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    const retryButton = screen.getByText('Retry in 1s');
    expect(retryButton).toBeInTheDocument();
    
    // Click the retry button
    fireEvent.click(retryButton);

    // Fast-forward time
    vi.advanceTimersByTime(1000);

    // The error boundary should still be in error state since the child still throws
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();

    vi.useRealTimers();
  });

  it('logs structured error information', () => {
    const consoleSpy = vi.spyOn(console, 'group');

    render(
      <ErrorBoundary level="component">
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Error Boundary'));
  });
});