.notificationContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  pointer-events: none;
}

.notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 12px;
  overflow: hidden;
  pointer-events: auto;
  transform: translateX(100%);
  animation: slideIn 0.3s ease-out forwards;
  position: relative;
}

.notification.error {
  border-left: 4px solid #ef4444;
}

.notification.warning {
  border-left: 4px solid #f59e0b;
}

.notification.info {
  border-left: 4px solid #3b82f6;
}

.content {
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.closeButton {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.message {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: #4b5563;
  line-height: 1.4;
}

.actionButton {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.actionButton:hover {
  background: #2563eb;
}

.actionButton:active {
  background: #1d4ed8;
}

.progressBar {
  height: 3px;
  background: #f3f4f6;
  position: relative;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  width: 100%;
  transform: translateX(-100%);
  animation: progressAnimation linear forwards;
}

.notification.error .progress {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.notification.warning .progress {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes progressAnimation {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .notificationContainer {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    margin-bottom: 8px;
  }
  
  .content {
    padding: 12px;
  }
  
  .title {
    font-size: 13px;
  }
  
  .message {
    font-size: 12px;
  }
}