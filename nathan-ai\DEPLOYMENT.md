# Nathan AI - Deployment Guide

This guide covers how to deploy Nathan AI to various hosting platforms and environments.

## Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- Environment variables configured
- API keys for Hugging Face and Eleven Labs

## Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Required API Keys
VITE_HUGGINGFACE_API_KEY=your_huggingface_api_key_here
VITE_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
VITE_ELEVENLABS_VOICE_ID=your_voice_id_here

# Optional Configuration
VITE_APP_TITLE=Nathan AI
VITE_APP_VERSION=1.0.0
NODE_ENV=production
```

## Build Process

### 1. Production Build

Run the optimized build script:

```bash
npm run build:prod
# or
node scripts/build.js
```

This will:
- Clean previous builds
- Run TypeScript type checking
- Execute linting
- Run all tests
- Build the application with optimizations
- Analyze bundle size
- Generate build report

### 2. Manual Build

For a simple build without checks:

```bash
npm run build
```

## Deployment Options

### 1. Netlify (Recommended)

Netlify provides excellent static hosting with automatic deployments.

#### Setup:
1. Connect your GitHub repository to Netlify
2. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
3. Set environment variables in Netlify dashboard
4. Deploy automatically on git push

#### Configuration:
The build script generates `netlify.toml` with optimized settings including:
- Security headers
- Caching rules
- SPA routing
- Performance optimizations

### 2. Vercel

Vercel offers seamless deployment for modern web applications.

#### Setup:
1. Connect your GitHub repository to Vercel
2. Vercel will auto-detect the framework
3. Set environment variables in Vercel dashboard
4. Deploy automatically on git push

#### Configuration:
The build script generates `vercel.json` with:
- Build configuration
- Routing rules
- Security headers
- Caching policies

### 3. GitHub Pages

Deploy directly from your GitHub repository.

#### Setup:
1. Enable GitHub Pages in repository settings
2. The build script generates `.github/workflows/deploy.yml`
3. Push to main branch to trigger deployment
4. Set environment variables as GitHub Secrets

### 4. AWS S3 + CloudFront

For enterprise deployments with AWS infrastructure.

#### Setup:
1. Create S3 bucket for static hosting
2. Configure CloudFront distribution
3. Upload build files to S3
4. Configure custom domain (optional)

#### Commands:
```bash
# Build for production
npm run build

# Upload to S3 (requires AWS CLI)
aws s3 sync dist/ s3://your-bucket-name --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

### 5. Docker Deployment

Deploy using containers for scalable infrastructure.

#### Build Docker Image:
```bash
# Build the application
npm run build

# Build Docker image
docker build -t nathan-ai .

# Run container
docker run -p 3000:80 nathan-ai
```

#### Docker Compose:
```yaml
version: '3.8'
services:
  nathan-ai:
    build: .
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 6. Traditional Web Server

Deploy to Apache, Nginx, or other web servers.

#### Nginx Configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/nathan-ai/dist;
    index index.html;
    
    # SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options nosniff;
    add_header Referrer-Policy strict-origin-when-cross-origin;
}
```

## Performance Optimization

### Bundle Analysis
The build process includes bundle analysis:
- Total bundle size
- Individual chunk sizes
- Dependency analysis
- Performance recommendations

### Caching Strategy
- Static assets: 1 year cache with immutable flag
- HTML files: No cache or short cache
- API responses: Cached in memory with TTL
- Audio files: Cached with LRU eviction

### CDN Configuration
For optimal performance, configure your CDN with:
- Gzip/Brotli compression
- HTTP/2 support
- Edge caching rules
- Geographic distribution

## Monitoring and Health Checks

### Health Check Endpoint
The application includes built-in health checks:
- Browser compatibility
- Memory usage
- Network connectivity
- Performance metrics
- Local storage functionality

Access health status at: `/health`

### Monitoring Setup
```javascript
import { healthCheckManager } from './src/utils/healthCheck';

// Start periodic monitoring
const stopMonitoring = startHealthMonitoring(60000); // Every minute

// Custom health check
healthCheckManager.registerCheck('api-connectivity', async () => {
  // Your custom check logic
});
```

## Security Considerations

### Content Security Policy
The deployment includes a strict CSP:
```
default-src 'self'; 
script-src 'self' 'unsafe-inline'; 
style-src 'self' 'unsafe-inline'; 
img-src 'self' data: https:; 
connect-src 'self' https://api-inference.huggingface.co https://api.elevenlabs.io; 
media-src 'self' blob:;
```

### Security Headers
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin

### API Key Security
- Never commit API keys to version control
- Use environment variables
- Rotate keys regularly
- Monitor API usage

## Troubleshooting

### Common Issues

#### Build Failures
- Check Node.js version (18+ required)
- Clear node_modules and reinstall
- Verify environment variables
- Check for TypeScript errors

#### Runtime Errors
- Verify API keys are set correctly
- Check browser console for errors
- Ensure CORS is configured for APIs
- Verify network connectivity

#### Performance Issues
- Analyze bundle size with build script
- Check memory usage in health checks
- Monitor API response times
- Optimize images and assets

### Debug Mode
Enable debug logging:
```env
VITE_DEBUG=true
NODE_ENV=development
```

### Health Check Debugging
```javascript
// Manual health check
const result = await healthCheckManager.runHealthChecks();
console.log('Health status:', result);
```

## Rollback Strategy

### Quick Rollback
1. Keep previous build artifacts
2. Use deployment platform's rollback feature
3. Monitor health checks after rollback

### Database Migrations
- Nathan AI is stateless (no database)
- User preferences stored in localStorage
- No migration concerns

## Support

For deployment issues:
1. Check the health endpoint: `/health`
2. Review browser console logs
3. Verify environment variables
4. Check API connectivity
5. Monitor performance metrics

## Changelog

Track deployment versions and changes:
- Build reports include version information
- Health checks report application version
- Monitor performance metrics over time