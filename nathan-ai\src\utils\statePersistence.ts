import type { PersonalityConfig } from '../types/personality';
import type { InputMode, VisualMode } from '../types/conversation';

// Keys for localStorage
const STORAGE_KEYS = {
  PERSONALITY_PREFERENCES: 'nathan-personality-preferences',
  UI_PREFERENCES: 'nathan-ui-preferences',
  CONVERSATION_PREFERENCES: 'nathan-conversation-preferences',
} as const;

// Interface for UI preferences
interface UIPreferences {
  inputMode: InputMode;
  visualMode: VisualMode;
  theme?: 'light' | 'dark' | 'auto';
}

// Interface for conversation preferences
interface ConversationPreferences {
  autoPlayAudio: boolean;
  voiceInputEnabled: boolean;
  messageHistoryLimit: number;
}

// Default preferences
const DEFAULT_UI_PREFERENCES: UIPreferences = {
  inputMode: 'text',
  visualMode: 'visual',
  theme: 'auto',
};

const DEFAULT_CONVERSATION_PREFERENCES: ConversationPreferences = {
  autoPlayAudio: true,
  voiceInputEnabled: true,
  messageHistoryLimit: 100,
};

/**
 * Personality Preferences Persistence
 */

// Save personality preferences (only the parts that differ from default)
export function savePersonalityPreferences(personality: PersonalityConfig): void {
  try {
    const preferences = {
      name: personality.name,
      pronouns: personality.pronouns,
      personality: personality.personality,
      conversation_tips: personality.conversation_tips,
      lastUpdated: new Date().toISOString(),
    };
    
    localStorage.setItem(
      STORAGE_KEYS.PERSONALITY_PREFERENCES, 
      JSON.stringify(preferences)
    );
  } catch (error) {
    console.error('Failed to save personality preferences:', error);
    throw new Error('Failed to save personality preferences to local storage');
  }
}

// Load personality preferences
export function loadPersonalityPreferences(): Partial<PersonalityConfig> | null {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.PERSONALITY_PREFERENCES);
    if (!stored) {
      return null;
    }
    
    const preferences = JSON.parse(stored);
    
    // Validate the structure (basic validation)
    if (typeof preferences !== 'object' || !preferences.personality) {
      console.warn('Invalid personality preferences structure, clearing storage');
      clearPersonalityPreferences();
      return null;
    }
    
    return preferences;
  } catch (error) {
    console.error('Failed to load personality preferences:', error);
    clearPersonalityPreferences();
    return null;
  }
}

// Clear personality preferences
export function clearPersonalityPreferences(): void {
  try {
    localStorage.removeItem(STORAGE_KEYS.PERSONALITY_PREFERENCES);
  } catch (error) {
    console.error('Failed to clear personality preferences:', error);
  }
}

/**
 * UI Preferences Persistence
 */

// Save UI preferences
export function saveUIPreferences(preferences: Partial<UIPreferences>): void {
  try {
    const current = loadUIPreferences();
    const updated = { ...current, ...preferences };
    
    localStorage.setItem(
      STORAGE_KEYS.UI_PREFERENCES, 
      JSON.stringify(updated)
    );
  } catch (error) {
    console.error('Failed to save UI preferences:', error);
    throw new Error('Failed to save UI preferences to local storage');
  }
}

// Load UI preferences
export function loadUIPreferences(): UIPreferences {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.UI_PREFERENCES);
    if (!stored) {
      return DEFAULT_UI_PREFERENCES;
    }
    
    const preferences = JSON.parse(stored);
    
    // Merge with defaults to ensure all properties exist
    return {
      ...DEFAULT_UI_PREFERENCES,
      ...preferences,
    };
  } catch (error) {
    console.error('Failed to load UI preferences:', error);
    return DEFAULT_UI_PREFERENCES;
  }
}

// Clear UI preferences
export function clearUIPreferences(): void {
  try {
    localStorage.removeItem(STORAGE_KEYS.UI_PREFERENCES);
  } catch (error) {
    console.error('Failed to clear UI preferences:', error);
  }
}

/**
 * Conversation Preferences Persistence
 */

// Save conversation preferences
export function saveConversationPreferences(preferences: Partial<ConversationPreferences>): void {
  try {
    const current = loadConversationPreferences();
    const updated = { ...current, ...preferences };
    
    localStorage.setItem(
      STORAGE_KEYS.CONVERSATION_PREFERENCES, 
      JSON.stringify(updated)
    );
  } catch (error) {
    console.error('Failed to save conversation preferences:', error);
    throw new Error('Failed to save conversation preferences to local storage');
  }
}

// Load conversation preferences
export function loadConversationPreferences(): ConversationPreferences {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.CONVERSATION_PREFERENCES);
    if (!stored) {
      return DEFAULT_CONVERSATION_PREFERENCES;
    }
    
    const preferences = JSON.parse(stored);
    
    // Merge with defaults to ensure all properties exist
    return {
      ...DEFAULT_CONVERSATION_PREFERENCES,
      ...preferences,
    };
  } catch (error) {
    console.error('Failed to load conversation preferences:', error);
    return DEFAULT_CONVERSATION_PREFERENCES;
  }
}

// Clear conversation preferences
export function clearConversationPreferences(): void {
  try {
    localStorage.removeItem(STORAGE_KEYS.CONVERSATION_PREFERENCES);
  } catch (error) {
    console.error('Failed to clear conversation preferences:', error);
  }
}

/**
 * Utility Functions
 */

// Clear all preferences
export function clearAllPreferences(): void {
  clearPersonalityPreferences();
  clearUIPreferences();
  clearConversationPreferences();
}

// Check if localStorage is available
export function isStorageAvailable(): boolean {
  try {
    const test = '__storage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

// Get storage usage information
export function getStorageInfo(): {
  isAvailable: boolean;
  hasPersonalityPreferences: boolean;
  hasUIPreferences: boolean;
  hasConversationPreferences: boolean;
} {
  return {
    isAvailable: isStorageAvailable(),
    hasPersonalityPreferences: localStorage.getItem(STORAGE_KEYS.PERSONALITY_PREFERENCES) !== null,
    hasUIPreferences: localStorage.getItem(STORAGE_KEYS.UI_PREFERENCES) !== null,
    hasConversationPreferences: localStorage.getItem(STORAGE_KEYS.CONVERSATION_PREFERENCES) !== null,
  };
}

// Export types for use in other modules
export type { UIPreferences, ConversationPreferences };